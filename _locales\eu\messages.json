{"appName": {"message": "Bitwarden"}, "appLogoLabel": {"message": "Bitwarden logo"}, "extName": {"message": "Bitwarden pasa<PERSON>z kudeatzailea", "description": "Extension name, MUST be less than 40 characters (Safari restriction)"}, "extDesc": {"message": "At home, at work, or on the go, <PERSON><PERSON><PERSON> easily secures all your passwords, passkeys, and sensitive information", "description": "Extension description, MUST be less than 112 characters (Safari restriction)"}, "loginOrCreateNewAccount": {"message": "<PERSON><PERSON> hasi edo sortu kontu berri bat zure kutxa gotorrera sartzeko."}, "inviteAccepted": {"message": "<PERSON>n<PERSON><PERSON><PERSON> on<PERSON>ua"}, "createAccount": {"message": "Sortu kontua"}, "newToBitwarden": {"message": "New to Bitwarden?"}, "logInWithPasskey": {"message": "Log in with passkey"}, "useSingleSignOn": {"message": "Use single sign-on"}, "welcomeBack": {"message": "<PERSON><PERSON> et<PERSON>ri berriro ere"}, "setAStrongPassword": {"message": "<PERSON><PERSON><PERSON><PERSON> sendo bat ezarri"}, "finishCreatingYourAccountBySettingAPassword": {"message": "<PERSON><PERSON><PERSON> zure kontua sortzen pasahit<PERSON> e<PERSON>"}, "enterpriseSingleSignOn": {"message": "Enpresentzako saio hasiera bakarra"}, "cancel": {"message": "Ezeztatu"}, "close": {"message": "Itxi"}, "submit": {"message": "<PERSON><PERSON><PERSON>"}, "emailAddress": {"message": "<PERSON><PERSON>"}, "masterPass": {"message": "Pasahitz nagusia"}, "masterPassDesc": {"message": "<PERSON><PERSON><PERSON><PERSON>, kut<PERSON> gotorrera sartzeko erabiltzen duzun pasahitza da. <PERSON><PERSON> garra<PERSON> da ez ahaztea, ah<PERSON><PERSON> baduzu, ez dago pasahitza berreskuratzeko modurik."}, "masterPassHintDesc": {"message": "Pa<PERSON><PERSON>z nagusia ah<PERSON>, pista batek pasahitza gogoratzen lagunduko dizu."}, "masterPassHintText": {"message": "If you forget your password, the password hint can be sent to your email. $CURRENT$/$MAXIMUM$ character maximum.", "placeholders": {"current": {"content": "$1", "example": "0"}, "maximum": {"content": "$2", "example": "50"}}}, "reTypeMasterPass": {"message": "<PERSON><PERSON><PERSON> be<PERSON> p<PERSON> nagusia"}, "masterPassHint": {"message": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> (aukerakoa)"}, "passwordStrengthScore": {"message": "Password strength score $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "joinOrganization": {"message": "Join organization"}, "joinOrganizationName": {"message": "Join $ORGANIZATIONNAME$", "placeholders": {"organizationName": {"content": "$1", "example": "My Org Name"}}}, "finishJoiningThisOrganizationBySettingAMasterPassword": {"message": "Finish joining this organization by setting a master password."}, "tab": {"message": "Fitxak"}, "vault": {"message": "<PERSON><PERSON><PERSON> got<PERSON>"}, "myVault": {"message": "<PERSON><PERSON><PERSON> got<PERSON>"}, "allVaults": {"message": "<PERSON><PERSON><PERSON> gotor guztiak"}, "tools": {"message": "Tresnak"}, "settings": {"message": "Ezarpenak"}, "currentTab": {"message": "<PERSON><PERSON><PERSON> fit<PERSON>"}, "copyPassword": {"message": "<PERSON><PERSON><PERSON>"}, "copyPassphrase": {"message": "Copy passphrase"}, "copyNote": {"message": "<PERSON><PERSON><PERSON>"}, "copyUri": {"message": "Kopiatu URIa"}, "copyUsername": {"message": "Kopiatu erabiltzaile-izena"}, "copyNumber": {"message": "Kopiatu zen<PERSON>a"}, "copySecurityCode": {"message": "<PERSON><PERSON><PERSON> segu<PERSON>-kodea"}, "copyName": {"message": "<PERSON><PERSON><PERSON>"}, "copyCompany": {"message": "Copy company"}, "copySSN": {"message": "Copy Social Security number"}, "copyPassportNumber": {"message": "Copy passport number"}, "copyLicenseNumber": {"message": "Copy license number"}, "copyPrivateKey": {"message": "Copy private key"}, "copyPublicKey": {"message": "Copy public key"}, "copyFingerprint": {"message": "Copy fingerprint"}, "copyCustomField": {"message": "Copy $FIELD$", "placeholders": {"field": {"content": "$1", "example": "Custom field label"}}}, "copyWebsite": {"message": "Copy website"}, "copyNotes": {"message": "<PERSON><PERSON><PERSON>"}, "copy": {"message": "Kopiatu", "description": "Copy to clipboard"}, "fill": {"message": "<PERSON><PERSON>", "description": "This string is used on the vault page to indicate autofilling. Horizontal space is limited in the interface here so try and keep translations as concise as possible."}, "autoFill": {"message": "Auto-betetzea"}, "autoFillLogin": {"message": "Autofill login"}, "autoFillCard": {"message": "Auto-bete txartela"}, "autoFillIdentity": {"message": "Auto-bete nortasuna"}, "fillVerificationCode": {"message": "Bete egiaztapen-kodea"}, "fillVerificationCodeAria": {"message": "Bete egiaztapen-kodea", "description": "Aria label for the heading displayed the inline menu for totp code autofill"}, "generatePasswordCopied": {"message": "<PERSON>rtu p<PERSON> (kopiatuta)"}, "copyElementIdentifier": {"message": "Eremu pertsonalizatuaren izena kopiatu"}, "noMatchingLogins": {"message": "<PERSON> datozen saio-has<PERSON><PERSON> gabe"}, "noCards": {"message": "Txartelik ez"}, "noIdentities": {"message": "Nortasunik ez"}, "addLoginMenu": {"message": "Gehitu logina"}, "addCardMenu": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>"}, "addIdentityMenu": {"message": "Gehitu nortasuna"}, "unlockVaultMenu": {"message": "Desblokeatu kutxa gotorra"}, "loginToVaultMenu": {"message": "<PERSON>i sa<PERSON>a zure kutxa gotorrean"}, "autoFillInfo": {"message": "Ez dago auto-betetzeko saio-hasierarik nabigatzailearen uneko fitxan."}, "addLogin": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> gehitu"}, "addItem": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "accountEmail": {"message": "Account email"}, "requestHint": {"message": "Request hint"}, "requestPasswordHint": {"message": "Request password hint"}, "enterYourAccountEmailAddressAndYourPasswordHintWillBeSentToYou": {"message": "Enter your account email address and your password hint will be sent to you"}, "getMasterPasswordHint": {"message": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> nagus<PERSON> pista"}, "continue": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sendVerificationCode": {"message": "Bidali egiaztatze-kodea zure emailera"}, "sendCode": {"message": "<PERSON><PERSON><PERSON> kodea"}, "codeSent": {"message": "Kodea bidalia"}, "verificationCode": {"message": "Egiaztatze-kodea"}, "confirmIdentity": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, berretsi zure identitatea."}, "changeMasterPassword": {"message": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> nagusia"}, "continueToWebApp": {"message": "Continue to web app?"}, "continueToWebAppDesc": {"message": "Explore more features of your Bitwarden account on the web app."}, "continueToHelpCenter": {"message": "Continue to Help Center?"}, "continueToHelpCenterDesc": {"message": "Learn more about how to use Bitwarden on the Help Center."}, "continueToBrowserExtensionStore": {"message": "Continue to browser extension store?"}, "continueToBrowserExtensionStoreDesc": {"message": "Help others find out if <PERSON><PERSON><PERSON> is right for them. Visit your browser's extension store and leave a rating now."}, "changeMasterPasswordOnWebConfirmation": {"message": "You can change your master password on the Bitwarden web app."}, "fingerprintPhrase": {"message": "Hatz-marka digitalaren esaldia", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "yourAccountsFingerprint": {"message": "<PERSON><PERSON> kontuko hatz-marka digitalaren esaldia", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "twoStepLogin": {"message": "Bi urratseko sa<PERSON> hasiera"}, "logOut": {"message": "Itxi saioa"}, "aboutBitwarden": {"message": "About Bitwarden"}, "about": {"message": "<PERSON><PERSON> b<PERSON>z"}, "moreFromBitwarden": {"message": "More from Bitwarden"}, "continueToBitwardenDotCom": {"message": "Continue to bitwarden.com?"}, "bitwardenForBusiness": {"message": "Bitwarden for Business"}, "bitwardenAuthenticator": {"message": "Bitwarden Authenticator"}, "continueToAuthenticatorPageDesc": {"message": "Bitwarden Authenticator allows you to store authenticator keys and generate TOTP codes for 2-step verification flows. Learn more on the bitwarden.com website"}, "bitwardenSecretsManager": {"message": "Bitwarden Secrets Manager"}, "continueToSecretsManagerPageDesc": {"message": "Securely store, manage, and share developer secrets with Bitwarden Secrets Manager. Learn more on the bitwarden.com website."}, "passwordlessDotDev": {"message": "Passwordless.dev"}, "continueToPasswordlessDotDevPageDesc": {"message": "Create smooth and secure login experiences free from traditional passwords with Passwordless.dev. Learn more on the bitwarden.com website."}, "freeBitwardenFamilies": {"message": "Free Bitwarden Families"}, "freeBitwardenFamiliesPageDesc": {"message": "You are eligible for Free Bitwarden Families. Redeem this offer today in the web app."}, "version": {"message": "\n<PERSON><PERSON><PERSON>"}, "save": {"message": "Gorde"}, "move": {"message": "<PERSON><PERSON><PERSON>"}, "addFolder": {"message": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>"}, "name": {"message": "<PERSON><PERSON><PERSON>"}, "editFolder": {"message": "Editat<PERSON>"}, "editFolderWithName": {"message": "Edit folder: $FOLDERNAME$", "placeholders": {"foldername": {"content": "$1", "example": "Social"}}}, "newFolder": {"message": "New folder"}, "folderName": {"message": "Folder name"}, "folderHintText": {"message": "Nest a folder by adding the parent folder's name followed by a “/”. Example: Social/Forums"}, "noFoldersAdded": {"message": "No folders added"}, "createFoldersToOrganize": {"message": "Create folders to organize your vault items"}, "deleteFolderPermanently": {"message": "Are you sure you want to permanently delete this folder?"}, "deleteFolder": {"message": "<PERSON>za<PERSON><PERSON> ka<PERSON>eta"}, "folders": {"message": "Karpetak"}, "noFolders": {"message": "<PERSON>z dago erakusteko ka<PERSON>."}, "helpFeedback": {"message": "Laguntza eta iritz<PERSON>k"}, "helpCenter": {"message": "Bitwarden Laguntz<PERSON> zentroa"}, "communityForums": {"message": "Esploratu Bitwarden komunitatearen foroak"}, "contactSupport": {"message": "<PERSON><PERSON><PERSON> lagun<PERSON> ta<PERSON>"}, "sync": {"message": "Sinkronizatu"}, "syncVaultNow": {"message": "Sinkronizatu kutxa gotorra orain"}, "lastSync": {"message": "Azken sinkronizazioa:"}, "passGen": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "generator": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Short for 'credential generator'."}, "passGenInfo": {"message": "Automatikoki pasahitz sendo eta bakarrak sortzen ditu zure saio-hasieratarako."}, "bitWebVaultApp": {"message": "Bitwarden web app"}, "importItems": {"message": "Inportatu elementuak"}, "select": {"message": "Hautatu"}, "generatePassword": {"message": "<PERSON>rtu pasa<PERSON>"}, "generatePassphrase": {"message": "Generate passphrase"}, "passwordGenerated": {"message": "Password generated"}, "passphraseGenerated": {"message": "Passphrase generated"}, "usernameGenerated": {"message": "Username generated"}, "emailGenerated": {"message": "Email generated"}, "regeneratePassword": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "options": {"message": "<PERSON><PERSON><PERSON>"}, "length": {"message": "Luzera"}, "include": {"message": "Include", "description": "Card header for password generator include block"}, "uppercaseDescription": {"message": "Include uppercase characters", "description": "Tooltip for the password generator uppercase character checkbox"}, "uppercaseLabel": {"message": "A-Z", "description": "Label for the password generator uppercase character checkbox"}, "lowercaseDescription": {"message": "Include lowercase characters", "description": "Full description for the password generator lowercase character checkbox"}, "lowercaseLabel": {"message": "a-z", "description": "Label for the password generator lowercase character checkbox"}, "numbersDescription": {"message": "Include numbers", "description": "Full description for the password generator numbers checkbox"}, "numbersLabel": {"message": "0-9", "description": "Label for the password generator numbers checkbox"}, "specialCharactersDescription": {"message": "Include special characters", "description": "Full description for the password generator special characters checkbox"}, "numWords": {"message": "<PERSON><PERSON> kop<PERSON>a"}, "wordSeparator": {"message": "<PERSON><PERSON>"}, "capitalize": {"message": "<PERSON><PERSON><PERSON> letra la<PERSON>a", "description": "Make the first letter of a work uppercase."}, "includeNumber": {"message": "Sartu zenbakia"}, "minNumbers": {"message": "Gutxieneko zenbaki kop<PERSON>a"}, "minSpecial": {"message": "Gutxieneko ka<PERSON> be<PERSON>"}, "avoidAmbiguous": {"message": "Avoid ambiguous characters", "description": "Label for the avoid ambiguous characters checkbox."}, "generatorPolicyInEffect": {"message": "Enterprise policy requirements have been applied to your generator options.", "description": "Indicates that a policy limits the credential generator screen."}, "searchVault": {"message": "B<PERSON>tu kutxa got<PERSON>n"}, "edit": {"message": "Editatu"}, "view": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "noItemsInList": {"message": "<PERSON>z dago erakusteko <PERSON>."}, "itemInformation": {"message": "Elementuaren informazioa"}, "username": {"message": "Erabiltzaile izena"}, "password": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "totp": {"message": "Authenticator secret"}, "passphrase": {"message": "Pasaesaldia"}, "favorite": {"message": "Gogokoa"}, "unfavorite": {"message": "Unfavorite"}, "itemAddedToFavorites": {"message": "Item added to favorites"}, "itemRemovedFromFavorites": {"message": "<PERSON><PERSON> removed from favorites"}, "notes": {"message": "Oharrak"}, "privateNote": {"message": "Private note"}, "note": {"message": "<PERSON><PERSON>"}, "editItem": {"message": "Editatu elementua"}, "folder": {"message": "<PERSON><PERSON><PERSON>"}, "deleteItem": {"message": "Eza<PERSON><PERSON>ua"}, "viewItem": {"message": "<PERSON><PERSON>rat<PERSON>"}, "launch": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "launchWebsite": {"message": "Launch website"}, "launchWebsiteName": {"message": "Launch website $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret item"}}}, "website": {"message": "Webgunea"}, "toggleVisibility": {"message": "Txandakatu ikusgarritasuna"}, "manage": {"message": "Kudeatu"}, "other": {"message": "Bestelakoak"}, "unlockMethods": {"message": "Unlock options"}, "unlockMethodNeededToChangeTimeoutActionDesc": {"message": "Set up an unlock method to change your vault timeout action."}, "unlockMethodNeeded": {"message": "Set up an unlock method in Settings"}, "sessionTimeoutHeader": {"message": "Session timeout"}, "vaultTimeoutHeader": {"message": "Vault timeout"}, "otherOptions": {"message": "Other options"}, "rateExtension": {"message": "<PERSON>lora<PERSON>"}, "browserNotSupportClipboard": {"message": "Zure web nabigatzaileak ez du onartzen arbelean erraz kopiatzea. Eskuz kopiatu."}, "verifyYourIdentity": {"message": "Verify your identity"}, "weDontRecognizeThisDevice": {"message": "We don't recognize this device. Enter the code sent to your email to verify your identity."}, "continueLoggingIn": {"message": "Continue logging in"}, "yourVaultIsLocked": {"message": "Zure kutxa gotorra blokeatuta dago. Egiaztatu zure identitatea jarraitzeko."}, "yourVaultIsLockedV2": {"message": "Your vault is locked"}, "yourAccountIsLocked": {"message": "Your account is locked"}, "or": {"message": "or"}, "unlock": {"message": "Desblokeatu"}, "loggedInAsOn": {"message": "$HOSTNAME$-en $EMAIL$ bezala saioa hasita.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}, "hostname": {"content": "$2", "example": "bitwarden.com"}}}, "invalidMasterPassword": {"message": "<PERSON><PERSON><PERSON><PERSON> nagusi bali<PERSON>a"}, "vaultTimeout": {"message": "Kutxa gotorraren itxa<PERSON>ldia"}, "vaultTimeout1": {"message": "Timeout"}, "lockNow": {"message": "Blokeatu orain"}, "lockAll": {"message": "Lock all"}, "immediately": {"message": "Berehala"}, "tenSeconds": {"message": "10 segundu"}, "twentySeconds": {"message": "20 segundu"}, "thirtySeconds": {"message": "30 segundu"}, "oneMinute": {"message": "Minutu 1"}, "twoMinutes": {"message": "2 minutu"}, "fiveMinutes": {"message": "5 minutu"}, "fifteenMinutes": {"message": "15 minutu"}, "thirtyMinutes": {"message": "30 minutu"}, "oneHour": {"message": "Ordu 1"}, "fourHours": {"message": "4 ordu"}, "onLocked": {"message": "Sistema blo<PERSON>"}, "onRestart": {"message": "Nabigat<PERSON><PERSON>"}, "never": {"message": "Inoiz ez"}, "security": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "confirmMasterPassword": {"message": "Confirm master password"}, "masterPassword": {"message": "Master password"}, "masterPassImportant": {"message": "Your master password cannot be recovered if you forget it!"}, "masterPassHintLabel": {"message": "Master password hint"}, "errorOccurred": {"message": "Akats bat gertatu da"}, "emailRequired": {"message": "<PERSON><PERSON><PERSON>."}, "invalidEmail": {"message": "<PERSON>ail helbide baliogabea"}, "masterPasswordRequired": {"message": "Pasa<PERSON>z nagusia der<PERSON> da."}, "confirmMasterPasswordRequired": {"message": "Pa<PERSON><PERSON><PERSON> nagusia berridaz<PERSON>a derrigor<PERSON> da."}, "masterPasswordMinlength": {"message": "Master password must be at least $VALUE$ characters long.", "description": "The Master Password must be at least a specific number of characters long.", "placeholders": {"value": {"content": "$1", "example": "8"}}}, "masterPassDoesntMatch": {"message": "Pasahitz nagusiaren egiaztatzea ez dator bat."}, "newAccountCreated": {"message": "<PERSON>ure kontua egina dago. Orain <PERSON> has dezakezu."}, "newAccountCreated2": {"message": "Your new account has been created!"}, "youHaveBeenLoggedIn": {"message": "You have been logged in!"}, "youSuccessfullyLoggedIn": {"message": "You successfully logged in"}, "youMayCloseThisWindow": {"message": "You may close this window"}, "masterPassSent": {"message": "Mezu elektroniko bat bidali dizugu zure pasahitz nagusiaren pistarekin."}, "verificationCodeRequired": {"message": "Egiaztatze-kodea behar da."}, "webauthnCancelOrTimeout": {"message": "The authentication was cancelled or took too long. Please try again."}, "invalidVerificationCode": {"message": "Egiaztatze-kodea ez da baliozkoa"}, "valueCopied": {"message": "$VALUE$ kopiatuta", "description": "Value has been copied to the clipboard.", "placeholders": {"value": {"content": "$1", "example": "Password"}}}, "autofillError": {"message": "Ezin izan da orri honetan hautatutako elementua auto-bete. Kopiatu eta itsatsi informazioa dagokion tokian."}, "totpCaptureError": {"message": "Unable to scan QR code from the current webpage"}, "totpCaptureSuccess": {"message": "Authenticator key added"}, "totpCapture": {"message": "Scan authenticator QR code from current webpage"}, "totpHelperTitle": {"message": "Make 2-step verification seamless"}, "totpHelper": {"message": "Bitwarden can store and fill 2-step verification codes. Copy and paste the key into this field."}, "totpHelperWithCapture": {"message": "Bitwarden can store and fill 2-step verification codes. Select the camera icon to take a screenshot of this website's authenticator QR code, or copy and paste the key into this field."}, "learnMoreAboutAuthenticators": {"message": "Learn more about authenticators"}, "copyTOTP": {"message": "Copy Authenticator key (TOTP)"}, "loggedOut": {"message": "Sai<PERSON> itxita"}, "loggedOutDesc": {"message": "You have been logged out of your account."}, "loginExpired": {"message": "<PERSON><PERSON> amaitu da."}, "logIn": {"message": "Log in"}, "logInToBitwarden": {"message": "Log in to Bitwarden"}, "enterTheCodeSentToYourEmail": {"message": "Enter the code sent to your email"}, "enterTheCodeFromYourAuthenticatorApp": {"message": "Enter the code from your authenticator app"}, "pressYourYubiKeyToAuthenticate": {"message": "Press your YubiKey to authenticate"}, "duoTwoFactorRequiredPageSubtitle": {"message": "Duo two-step login is required for your account. Follow the steps below to finish logging in."}, "followTheStepsBelowToFinishLoggingIn": {"message": "Follow the steps below to finish logging in."}, "followTheStepsBelowToFinishLoggingInWithSecurityKey": {"message": "Follow the steps below to finish logging in with your security key."}, "restartRegistration": {"message": "Restart registration"}, "expiredLink": {"message": "Expired link"}, "pleaseRestartRegistrationOrTryLoggingIn": {"message": "Please restart registration or try logging in."}, "youMayAlreadyHaveAnAccount": {"message": "You may already have an account"}, "logOutConfirmation": {"message": "<PERSON><PERSON><PERSON> zaude saioa itxi nahi duzula?"}, "yes": {"message": "<PERSON>"}, "no": {"message": "Ez"}, "location": {"message": "Location"}, "unexpectedError": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> akatsa gertatu da."}, "nameRequired": {"message": "<PERSON><PERSON><PERSON> da."}, "addedFolder": {"message": "<PERSON><PERSON><PERSON> gehituta"}, "twoStepLoginConfirmation": {"message": "Bi urratseko saio hasiera dela eta, zure kontua seguruagoa da, beste aplikazio/gailu batekin saioa hastea eskatzen baitizu; adibi<PERSON>, se<PERSON><PERSON><PERSON>-gak<PERSON>, autentifikazio-aplikazio, SMS, telefono dei edo email bidez. Bi urratseko saio hasiera bitwarden.com webgunean aktibatu daiteke. Orain joan nahi duzu webgunera?"}, "twoStepLoginConfirmationContent": {"message": "Make your account more secure by setting up two-step login in the Bitwarden web app."}, "twoStepLoginConfirmationTitle": {"message": "Continue to web app?"}, "editedFolder": {"message": "Karpeta editatuta"}, "deleteFolderConfirmation": {"message": "<PERSON><PERSON><PERSON> al zaude karpeta hau ezabatu nahi duzula?"}, "deletedFolder": {"message": "Karpeta ezabatuta"}, "gettingStartedTutorial": {"message": "<PERSON><PERSON> tutoriala"}, "gettingStartedTutorialVideo": {"message": "Ikusi lehen urra<PERSON>etako tutoriala nabigatzailearen gehigarriari ahalik eta etekin handiena nola atera ikasteko."}, "syncingComplete": {"message": "Sinkronizatu da"}, "syncingFailed": {"message": "Sinkronizazioak huts egin du"}, "passwordCopied": {"message": "Pasa<PERSON><PERSON> k<PERSON>"}, "uri": {"message": "URI"}, "uriPosition": {"message": "URI $POSITION$", "description": "A listing of URIs. Ex: URI 1, URI 2, URI 3, etc.", "placeholders": {"position": {"content": "$1", "example": "2"}}}, "newUri": {"message": "URI berria"}, "addDomain": {"message": "Add domain", "description": "'Domain' here refers to an internet domain name (e.g. 'bitwarden.com') and the message in whole described the act of putting a domain value into the context."}, "addedItem": {"message": "Elementua gehituta"}, "editedItem": {"message": "Elementua editatuta"}, "deleteItemConfirmation": {"message": "<PERSON><PERSON><PERSON> zaude elementu hau zakarront<PERSON>ra bidali nahi duzula?"}, "deletedItem": {"message": "Elementua zakarrontzira bidalia"}, "overwritePassword": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "overwritePasswordConfirmation": {"message": "<PERSON><PERSON><PERSON> al zaude pasahitza berridatzi nahi duzula?"}, "overwriteUsername": {"message": "Erabiltz<PERSON><PERSON><PERSON><PERSON><PERSON>a <PERSON>"}, "overwriteUsernameConfirmation": {"message": "<PERSON><PERSON><PERSON> al zaude erabiltzaile-izena berridatzi nahi duzula?"}, "searchFolder": {"message": "<PERSON><PERSON><PERSON> karpeta"}, "searchCollection": {"message": "<PERSON><PERSON><PERSON> bilduma"}, "searchType": {"message": "B<PERSON>keta mota"}, "noneFolder": {"message": "Karpetarik ez", "description": "This is the folder for uncategorized items"}, "enableAddLoginNotification": {"message": "Galdetu saio-hasiera gehitz<PERSON>o"}, "vaultSaveOptionsTitle": {"message": "Save to vault options"}, "addLoginNotificationDesc": {"message": "Elementu bat gehitu nahi duzun galdetu, elementu hau zure kutxa gotorrean ez badago."}, "addLoginNotificationDescAlt": {"message": "Ask to add an item if one isn't found in your vault. Applies to all logged in accounts."}, "showCardsInVaultViewV2": {"message": "Always show cards as Autofill suggestions on Vault view"}, "showCardsCurrentTab": {"message": "<PERSON><PERSON><PERSON><PERSON> txa<PERSON> fitxa orrian"}, "showCardsCurrentTabDesc": {"message": "Erakutsi elementuen txartelak fitxa orrian, erraz auto-betetzeko."}, "showIdentitiesInVaultViewV2": {"message": "Always show identities as Autofill suggestions on Vault view"}, "showIdentitiesCurrentTab": {"message": "Erakutsi identitateak fitxa orrian"}, "showIdentitiesCurrentTabDesc": {"message": "Erakutsi identitateak fitxa orrian, erraz auto-betetzeko."}, "clickToAutofillOnVault": {"message": "Click items to autofill on Vault view"}, "clickToAutofill": {"message": "Click items in autofill suggestion to fill"}, "clearClipboard": {"message": "<PERSON><PERSON><PERSON> a<PERSON>", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "clearClipboardDesc": {"message": "Ezabatu automatikoki arbelean kopiatutako balioak.", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "notificationAddDesc": {"message": "Bitwardenek pasahitz hau gogoratu beharko lizuke?"}, "notificationAddSave": {"message": "Gorde"}, "notificationViewAria": {"message": "View $ITEMNAME$, opens in new window", "placeholders": {"itemName": {"content": "$1"}}, "description": "Aria label for the view button in notification bar confirmation message"}, "notificationNewItemAria": {"message": "New Item, opens in new window", "description": "Aria label for the new item button in notification bar confirmation message when error is prompted"}, "notificationEditTooltip": {"message": "Edit before saving", "description": "Tooltip and Aria label for edit button on cipher item"}, "newNotification": {"message": "New notification"}, "labelWithNotification": {"message": "$LABEL$: New notification", "description": "Label for the notification with a new login suggestion.", "placeholders": {"label": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "notificationLoginSaveConfirmation": {"message": "saved to Bitwarden.", "description": "Shown to user after item is saved."}, "notificationLoginUpdatedConfirmation": {"message": "updated in Bitwarden.", "description": "Shown to user after item is updated."}, "selectItemAriaLabel": {"message": "Select $ITEMTYPE$, $ITEMNAME$", "description": "Used by screen readers. $1 is the item type (like vault or folder), $2 is the selected item name.", "placeholders": {"itemType": {"content": "$1"}, "itemName": {"content": "$2"}}}, "saveAsNewLoginAction": {"message": "Save as new login", "description": "Button text for saving login details as a new entry."}, "updateLoginAction": {"message": "Update login", "description": "Button text for updating an existing login entry."}, "unlockToSave": {"message": "Unlock to save this login", "description": "User prompt to take action in order to save the login they just entered."}, "saveLogin": {"message": "Save login", "description": "Prompt asking the user if they want to save their login details."}, "updateLogin": {"message": "Update existing login", "description": "Prompt asking the user if they want to update an existing login entry."}, "loginSaveSuccess": {"message": "<PERSON><PERSON> saved", "description": "Message displayed when login details are successfully saved."}, "loginUpdateSuccess": {"message": "Login updated", "description": "Message displayed when login details are successfully updated."}, "loginUpdateTaskSuccess": {"message": "Great job! You took the steps to make you and $ORGANIZATION$ more secure.", "placeholders": {"organization": {"content": "$1"}}, "description": "Shown to user after login is updated."}, "loginUpdateTaskSuccessAdditional": {"message": "Thank you for making $ORGANIZATION$ more secure. You have $TASK_COUNT$ more passwords to update.", "placeholders": {"organization": {"content": "$1"}, "task_count": {"content": "$2"}}, "description": "Shown to user after login is updated."}, "nextSecurityTaskAction": {"message": "Change next password", "description": "Message prompting user to undertake completion of another security task."}, "saveFailure": {"message": "Error saving", "description": "Error message shown when the system fails to save login details."}, "saveFailureDetails": {"message": "Oh no! We couldn't save this. Try entering the details manually.", "description": "Detailed error message shown when saving login details fails."}, "enableChangedPasswordNotification": {"message": "Galdetu uneko sa<PERSON>-hasiera eguneratzeko"}, "changedPasswordNotificationDesc": {"message": "Galdetu saio-hasiera baten pasa<PERSON><PERSON><PERSON>, webgune batean aldaketaren bat atzematen denean."}, "changedPasswordNotificationDescAlt": {"message": "Ask to update a login's password when a change is detected on a website. Applies to all logged in accounts."}, "enableUsePasskeys": {"message": "Ask to save and use passkeys"}, "usePasskeysDesc": {"message": "Ask to save new passkeys or log in with passkeys stored in your vault. Applies to all logged in accounts."}, "notificationChangeDesc": {"message": "Bitwardenen pasahitz hau eguneratu nahi duzu?"}, "notificationChangeSave": {"message": "Eguneratu"}, "notificationUnlockDesc": {"message": "Unlock your Bitwarden vault to complete the autofill request."}, "notificationUnlock": {"message": "Unlock"}, "additionalOptions": {"message": "Additional options"}, "enableContextMenuItem": {"message": "<PERSON><PERSON><PERSON><PERSON> laster-<PERSON><PERSON>"}, "contextMenuItemDesc": {"message": "Erabili bigarren mailako klika webgunerako pasahitzak eta saio-hasierak sort<PERSON>ko."}, "contextMenuItemDescAlt": {"message": "Use a secondary click to access password generation and matching logins for the website. Applies to all logged in accounts."}, "defaultUriMatchDetection": {"message": "Lehenetsitako detekzioa URI kointzidentziarako", "description": "Default URI match detection for autofill."}, "defaultUriMatchDetectionDesc": {"message": "Hautatu auto-betetzea bezalako saio-hasierako ekintzetarako erabiliko den URI kointzidentzia detektatzeko modu lehenetsia."}, "theme": {"message": "Gaia"}, "themeDesc": {"message": "Aldatu aplikaziorako kolore gaia."}, "themeDescAlt": {"message": "Change the application's color theme. Applies to all logged in accounts."}, "dark": {"message": "Iluna", "description": "Dark color"}, "light": {"message": "<PERSON><PERSON><PERSON>", "description": "Light color"}, "exportFrom": {"message": "Export from"}, "exportVault": {"message": "Esportatu kutxa gotorra"}, "fileFormat": {"message": "Fitxategiaren formatua"}, "fileEncryptedExportWarningDesc": {"message": "This file export will be password protected and require the file password to decrypt."}, "filePassword": {"message": "File password"}, "exportPasswordDescription": {"message": "This password will be used to export and import this file"}, "accountRestrictedOptionDescription": {"message": "Use your account encryption key, derived from your account's username and Master Password, to encrypt the export and restrict import to only the current Bitwarden account."}, "passwordProtectedOptionDescription": {"message": "Set a file password to encrypt the export and import it to any Bitwarden account using the password for decryption."}, "exportTypeHeading": {"message": "Export type"}, "accountRestricted": {"message": "Account restricted"}, "filePasswordAndConfirmFilePasswordDoNotMatch": {"message": "“File password”  and “Confirm file password“ do not match."}, "warning": {"message": "KONTUZ", "description": "WARNING (should stay in capitalized letters if the language permits)"}, "warningCapitalized": {"message": "Warning", "description": "Warning (should maintain locale-relevant capitalization)"}, "confirmVaultExport": {"message": "Baieztatu kutxa gotorra esportatzea"}, "exportWarningDesc": {"message": "Esportazio honek kutxa gotorraren datuak zifratu gabeko formatuan biltzen ditu. Ez zenuke gorde edo kanal ez-seguruetaik (emaila, adibidez) bidali behar. Erabili eta berehala ezabatu."}, "encExportKeyWarningDesc": {"message": "Esportazio honek zure datuak zifratzen ditu zure kontuaren zifratze-gakoa erabiliz. Inoiz zure kontuko zifratze-gakoa aldatuz gero, berriro esportatu beharko du<PERSON>, ezin izango baituzu fitxategi hori deszifratu."}, "encExportAccountWarningDesc": {"message": "Kontua zifratzeko gakoak Bitwarden erabiltzaile bakoitzarentzako bakarrik dira; beraz, ezin da inportatu beste kontu batean zifratutako esportazio bat."}, "exportMasterPassword": {"message": "Sartu pasahitz nagusia kutxa gotorreko datuak esportatzeko."}, "shared": {"message": "Partekatua"}, "bitwardenForBusinessPageDesc": {"message": "Bitwarden for Business allows you to share your vault items with others by using an organization. Learn more on the bitwarden.com website."}, "moveToOrganization": {"message": "<PERSON><PERSON><PERSON>"}, "movedItemToOrg": {"message": "$ITEMNAME$ $ORGNAME$-ra mugituta", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "orgname": {"content": "$2", "example": "Company Name"}}}, "moveToOrgDesc": {"message": "Aukeratu elementu hau zein erakundetara eraman nahi duzun. Erakunde batera pasatzeak elementuaren jabetza erakunde horretara transferitzen du. Zu ez zara elementu honen jabe zuzena izango mugitzen duzunean."}, "learnMore": {"message": "Gehiago i<PERSON>i"}, "authenticatorKeyTotp": {"message": "Autentifikazio-gakoa (TOTP)"}, "verificationCodeTotp": {"message": "Egiaztatze-kodea (TOTP)"}, "copyVerificationCode": {"message": "Kopiatu egiaztatze-kodea"}, "attachments": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "deleteAttachment": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "deleteAttachmentConfirmation": {"message": "<PERSON><PERSON><PERSON> zaude eranskina ezabatu nahi duzula?"}, "deletedAttachment": {"message": "Eranskina e<PERSON>"}, "newAttachment": {"message": "<PERSON><PERSON><PERSON><PERSON> era<PERSON><PERSON> be<PERSON>a"}, "noAttachments": {"message": "<PERSON>z dago eranskinik"}, "attachmentSaved": {"message": "Eranskina gorde da."}, "file": {"message": "Fitxategia"}, "fileToShare": {"message": "File to share"}, "selectFile": {"message": "<PERSON><PERSON><PERSON> fitxategia."}, "maxFileSize": {"message": "Eranskinaren gehienezko tamaina 500MB."}, "featureUnavailable": {"message": "Ezaugarria ez dago era<PERSON>gar<PERSON>"}, "legacyEncryptionUnsupported": {"message": "Legacy encryption is no longer supported. Please contact support to recover your account."}, "premiumMembership": {"message": "Premium bazkidea"}, "premiumManage": {"message": "Bazkidetza kudeatu"}, "premiumManageAlert": {"message": "Zure bazkidetza bitwarden.com webguneko kutxa gotorrean kudeatu dezakezu. Orain bisitatu nahi duzu webgunea?"}, "premiumRefresh": {"message": "Eguneratu baz<PERSON>detza"}, "premiumNotCurrentMember": {"message": "Orain ez zara premium bazkide."}, "premiumSignUpAndGet": {"message": "Erregistra zaitez premium bazkide gisa eta honakoa lortu:"}, "ppremiumSignUpStorage": {"message": "Eranskinentzako 1GB-eko zifratutako biltegia."}, "premiumSignUpEmergency": {"message": "Emergency access."}, "premiumSignUpTwoStepOptions": {"message": "Proprietary two-step login options such as YubiKey and Duo."}, "ppremiumSignUpReports": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> higie<PERSON>, kontuaren egoera eta datu-bortxaketen txostenak, kutxa gotorra seguru mantentz<PERSON>."}, "ppremiumSignUpTotp": {"message": "TOTP (2FA) egiaztatze-kode sortzailea gotor kutxako erregistroetarako."}, "ppremiumSignUpSupport": {"message": "Lehentasunezko bezeroarentzako arreta."}, "ppremiumSignUpFuture": {"message": "Etorkizuneko premium ezaugarri guztiak. Laister gehiago!"}, "premiumPurchase": {"message": "Premium erosi"}, "premiumPurchaseAlertV2": {"message": "You can purchase Premium from your account settings on the Bitwarden web app."}, "premiumCurrentMember": {"message": "Premium bazkide zara!"}, "premiumCurrentMemberThanks": {"message": "Eskerrik asko Bitwarden babesteagatik."}, "premiumFeatures": {"message": "Upgrade to Premium and receive:"}, "premiumPrice": {"message": "<PERSON><PERSON>, urtean $PRICE$gatik!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "premiumPriceV2": {"message": "All for just $PRICE$ per year!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "refreshComplete": {"message": "Eguneratz<PERSON> eginda"}, "enableAutoTotpCopy": {"message": "Kopiatu TOTP automatikoki"}, "disableAutoTotpCopyDesc": {"message": "<PERSON><PERSON> hasiera batek autentifikazio-g<PERSON><PERSON>u, TOTP egiaztatze-kodea arbelean automatikoki kopiatuko da saio hasiera bat auto-betetzean."}, "enableAutoBiometricsPrompt": {"message": "Biometria eskatu saioa hastean"}, "premiumRequired": {"message": "Premium izatea beharrezkoa da"}, "premiumRequiredDesc": {"message": "Premium bazkidetza beharrezkoa da ezaugarri hau erabiltzeko."}, "authenticationTimeout": {"message": "Authentication timeout"}, "authenticationSessionTimedOut": {"message": "The authentication session timed out. Please restart the login process."}, "verificationCodeEmailSent": {"message": "Egiaztatze emaila $EMAIL$-era bidalia.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "dontAskAgainOnThisDeviceFor30Days": {"message": "Don't ask again on this device for 30 days"}, "selectAnotherMethod": {"message": "Select another method", "description": "Select another two-step login method"}, "useYourRecoveryCode": {"message": "Use your recovery code"}, "insertU2f": {"message": "Sartu zure segurtasun-gakoa ordenagailuaren USB atakan. <PERSON><PERSON><PERSON>, sakatu e<PERSON><PERSON>."}, "openInNewTab": {"message": "Open in new tab"}, "webAuthnAuthenticate": {"message": "WebAuthn auten<PERSON>"}, "readSecurityKey": {"message": "Read security key"}, "awaitingSecurityKeyInteraction": {"message": "Awaiting security key interaction..."}, "loginUnavailable": {"message": "<PERSON>z dago eskura<PERSON>ri sa<PERSON>-has<PERSON><PERSON>"}, "noTwoStepProviders": {"message": "Kontu honek bi urratseko saio hasiera du gaituta, baina ezarritako bi urratserako hornitzailea ez da web-nabigatzaile honekin bateragarria."}, "noTwoStepProviders2": {"message": "<PERSON><PERSON><PERSON>, erabili nabigatzaile bateragarri bat (ad<PERSON>dez, Chrome) eta/edo gehitu bateragarritasun obea duten nabigatzaile bidezko (autentifikazio aplikazio gisa) autentifikazio modu gehigarriak."}, "twoStepOptions": {"message": "<PERSON>i urratseko saio has<PERSON><PERSON> au<PERSON>ak"}, "selectTwoStepLoginMethod": {"message": "Select two-step login method"}, "recoveryCodeDesc": {"message": "Bi urratseko egiaztatzeko modu guztietarako sarbidea galdu duzu? Erabili zure berreskuratze-kodea zure kontuko bi urratseko egiaztatze hornitzaile guztiak desaktibatzeko."}, "recoveryCodeTitle": {"message": "Berreskuratze-kodea"}, "authenticatorAppTitle": {"message": "Autentifikazio aplikazioa"}, "authenticatorAppDescV2": {"message": "Enter a code generated by an authenticator app like Bitwarden Authenticator.", "description": "'Bitwarden Authenticator' is a product name and should not be translated."}, "yubiKeyTitleV2": {"message": "Yubico OTP Security Key"}, "yubiKeyDesc": {"message": "Erabili YubiKey zure kontuan sartzeko. YubiKey 4, 4 <PERSON><PERSON>, 4C eta NEO gailuekin dabil."}, "duoDescV2": {"message": "Enter a code generated by Duo Security.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "duoOrganizationDesc": {"message": "Egiaztatu zure erakunderako Duo Securityrekin Duo Mobile aplikazioa, SMS, telefono deia edo U2F segurtasun-gakoa erabiliz.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "webAuthnTitle": {"message": "FIDO2 WebAuthn"}, "webAuthnDesc": {"message": "<PERSON><PERSON><PERSON> gait<PERSON> edozein WebAuthn segurtasun-gako zure kontura sartzeko."}, "emailTitle": {"message": "<PERSON><PERSON><PERSON>"}, "emailDescV2": {"message": "Enter a code sent to your email."}, "selfHostedEnvironment": {"message": "Ostatze ingurune propioa"}, "selfHostedBaseUrlHint": {"message": "Specify the base URL of your on-premises hosted Bitwarden installation. Example: https://bitwarden.company.com"}, "selfHostedCustomEnvHeader": {"message": "For advanced configuration, you can specify the base URL of each service independently."}, "selfHostedEnvFormInvalid": {"message": "You must add either the base Server URL or at least one custom environment."}, "customEnvironment": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "baseUrl": {"message": "Zerbitzariaren URL-a"}, "selfHostBaseUrl": {"message": "Self-host server URL", "description": "Label for field requesting a self-hosted integration service URL"}, "apiUrl": {"message": "API zerbitzariaren URL-a"}, "webVaultUrl": {"message": "Web kutxa gotorreko zerbitzariaren URL-a"}, "identityUrl": {"message": "Identitate zerbitzariaren URL-a"}, "notificationsUrl": {"message": "Jakinarazpenen zerbitzariaren URL-a"}, "iconsUrl": {"message": "Ikonoen zerbitzariaren URL-a"}, "environmentSaved": {"message": "Inguruneko URL-ak gorde dira."}, "showAutoFillMenuOnFormFields": {"message": "Show autofill menu on form fields", "description": "Represents the message for allowing the user to enable the autofill overlay"}, "autofillSuggestionsSectionTitle": {"message": "Autofill suggestions"}, "autofillSpotlightTitle": {"message": "Easily find autofill suggestions"}, "autofillSpotlightDesc": {"message": "Turn off your browser's autofill settings, so they don't conflict with Bitwarden."}, "turnOffBrowserAutofill": {"message": "Turn off $BROWSER$ autofill", "placeholders": {"browser": {"content": "$1", "example": "Chrome"}}}, "turnOffAutofill": {"message": "Turn off autofill"}, "showInlineMenuLabel": {"message": "Show autofill suggestions on form fields"}, "showInlineMenuIdentitiesLabel": {"message": "Display identities as suggestions"}, "showInlineMenuCardsLabel": {"message": "Display cards as suggestions"}, "showInlineMenuOnIconSelectionLabel": {"message": "Display suggestions when icon is selected"}, "showInlineMenuOnFormFieldsDescAlt": {"message": "Applies to all logged in accounts."}, "turnOffBrowserBuiltInPasswordManagerSettings": {"message": "Turn off your browser's built in password manager settings to avoid conflicts."}, "turnOffBrowserBuiltInPasswordManagerSettingsLink": {"message": "Edit browser settings."}, "autofillOverlayVisibilityOff": {"message": "<PERSON><PERSON><PERSON>", "description": "Overlay setting select option for disabling autofill overlay"}, "autofillOverlayVisibilityOnFieldFocus": {"message": "When field is selected (on focus)", "description": "Overlay appearance select option for showing the field on focus of the input element"}, "autofillOverlayVisibilityOnButtonClick": {"message": "When autofill icon is selected", "description": "Overlay appearance select option for showing the field on click of the overlay icon"}, "enableAutoFillOnPageLoadSectionTitle": {"message": "Autofill on page load"}, "enableAutoFillOnPageLoad": {"message": "Auto-bete orrialdea karga<PERSON>"}, "enableAutoFillOnPageLoadDesc": {"message": "Sai<PERSON>-hasierako formulario bat detektatzen bada, auto-bete webgunea kargatzen denean."}, "experimentalFeature": {"message": "Compromised or untrusted websites can exploit autofill on page load."}, "learnMoreAboutAutofillOnPageLoadLinkText": {"message": "Learn more about risks"}, "learnMoreAboutAutofill": {"message": "Learn more about autofill"}, "defaultAutoFillOnPageLoad": {"message": "Saio-has<PERSON><PERSON> le<PERSON> auto-betetzearen konfigurazioa"}, "defaultAutoFillOnPageLoadDesc": {"message": "Orrialdearen kargatze aukeretan auto-betetzea aktibatu ondoren, banakako saio-hasierak aktibatu edo desaktibatu ditzakezu."}, "itemAutoFillOnPageLoad": {"message": "Auto-bete or<PERSON><PERSON> ka<PERSON> (ezarpenetan g<PERSON><PERSON> badago)"}, "autoFillOnPageLoadUseDefault": {"message": "Erabili ezar<PERSON>"}, "autoFillOnPageLoadYes": {"message": "Auto-bete orrialdea karga<PERSON>"}, "autoFillOnPageLoadNo": {"message": "Ez auto-bete orrialdea kargatzean"}, "commandOpenPopup": {"message": "<PERSON><PERSON><PERSON> gain<PERSON> ireki kutxa gotorra"}, "commandOpenSidebar": {"message": "Alboko barran ireki kutxa gotorra"}, "commandAutofillLoginDesc": {"message": "Autofill the last used login for the current website"}, "commandAutofillCardDesc": {"message": "Autofill the last used card for the current website"}, "commandAutofillIdentityDesc": {"message": "Autofill the last used identity for the current website"}, "commandGeneratePasswordDesc": {"message": "Zorizko p<PERSON> berria sortu eta kopiatu arbelean"}, "commandLockVaultDesc": {"message": "Blokeatu kutxa gotorra"}, "customFields": {"message": "<PERSON><PERSON><PERSON>onaliza<PERSON>ak"}, "copyValue": {"message": "<PERSON><PERSON><PERSON> balioa"}, "value": {"message": "<PERSON><PERSON>"}, "newCustomField": {"message": "<PERSON><PERSON><PERSON> be<PERSON>a"}, "dragToSort": {"message": "Arrastatu txukuntzeko"}, "dragToReorder": {"message": "Drag to reorder"}, "cfTypeText": {"message": "Test<PERSON>"}, "cfTypeHidden": {"message": "Ezkutatua"}, "cfTypeBoolean": {"message": "<PERSON><PERSON><PERSON>"}, "cfTypeCheckbox": {"message": "Checkbox"}, "cfTypeLinked": {"message": "Lot<PERSON>", "description": "This describes a field that is 'linked' (tied) to another field."}, "linkedValue": {"message": "Balioa lotuta", "description": "This describes a value that is 'linked' (tied) to another value."}, "popup2faCloseMessage": {"message": "Leiho gainjarritik kanpora klik eginez gero, zure emaila egiaztatzeko leiho gainjarria itxi egingo da. Leiho berri batean ireki nahi duzu leiho gainjarri hau itxi ez dadin?"}, "popupU2fCloseMessage": {"message": "Nabigatzaile honek ezin ditu U2F eskaerak prozesatu leiho gainjarri honetan. Leiho berri batean ireki nahi duzu leiho gainjarri hau saioa U2F erabiliz hasi ahal izateko?"}, "enableFavicon": {"message": "Erakutsi webguneko i<PERSON>k"}, "faviconDesc": {"message": "<PERSON><PERSON><PERSON><PERSON> i<PERSON>i bat saio-hasiera bakoitzaren ondoan."}, "faviconDescAlt": {"message": "Show a recognizable image next to each login. Applies to all logged in accounts."}, "enableBadgeCounter": {"message": "Erakuts<PERSON> txa<PERSON>len kont<PERSON>"}, "badgeCounterDesc": {"message": "<PERSON><PERSON><PERSON> zenbat saio-hasiera dituzun uneko webgunerako."}, "cardholderName": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>a"}, "number": {"message": "Zenbakia"}, "brand": {"message": "<PERSON><PERSON>"}, "expirationMonth": {"message": "Iraungitze hilabetea"}, "expirationYear": {"message": "Iraungitze urtea"}, "expiration": {"message": "Iraungitze data"}, "january": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "february": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "march": {"message": "Mart<PERSON><PERSON>"}, "april": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "may": {"message": "<PERSON><PERSON><PERSON>"}, "june": {"message": "<PERSON><PERSON><PERSON>"}, "july": {"message": "<PERSON>ztail<PERSON>"}, "august": {"message": "Abuztua"}, "september": {"message": "<PERSON><PERSON>"}, "october": {"message": "<PERSON><PERSON><PERSON>"}, "november": {"message": "<PERSON><PERSON><PERSON>"}, "december": {"message": "Abendua"}, "securityCode": {"message": "<PERSON><PERSON><PERSON><PERSON>-kodea"}, "ex": {"message": "adib."}, "title": {"message": "Titulua"}, "mr": {"message": "Jn."}, "mrs": {"message": "And."}, "ms": {"message": "And."}, "dr": {"message": "Jn."}, "mx": {"message": "Mx"}, "firstName": {"message": "<PERSON><PERSON><PERSON>"}, "middleName": {"message": "<PERSON><PERSON><PERSON>"}, "lastName": {"message": "<PERSON><PERSON>zen<PERSON>"}, "fullName": {"message": "<PERSON><PERSON> o<PERSON>a"}, "identityName": {"message": "Identitate izena"}, "company": {"message": "Enpresa"}, "ssn": {"message": "Segurtasun sozialaren zenba<PERSON>a"}, "passportNumber": {"message": "Pasaporte zenbakia"}, "licenseNumber": {"message": "Lizentzia zenbakia"}, "email": {"message": "<PERSON><PERSON><PERSON>"}, "phone": {"message": "Telefonoa"}, "address": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "address1": {"message": "1go he<PERSON><PERSON><PERSON>"}, "address2": {"message": "2. <PERSON><PERSON><PERSON><PERSON>"}, "address3": {"message": "3. <PERSON><PERSON><PERSON><PERSON>"}, "cityTown": {"message": "Hiria / Herria"}, "stateProvince": {"message": "Estatua / Probintzia"}, "zipPostalCode": {"message": "<PERSON><PERSON> kodea"}, "country": {"message": "Herrialdea"}, "type": {"message": "<PERSON><PERSON>"}, "typeLogin": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "typeLogins": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "typeSecureNote": {"message": "<PERSON><PERSON> segu<PERSON>a"}, "typeCard": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "typeIdentity": {"message": "Identitatea"}, "typeSshKey": {"message": "SSH key"}, "newItemHeader": {"message": "New $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "editItemHeader": {"message": "Edit $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "viewItemHeader": {"message": "View $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "passwordHistory": {"message": "Pasahitz historia"}, "generatorHistory": {"message": "Generator history"}, "clearGeneratorHistoryTitle": {"message": "Clear generator history"}, "cleargGeneratorHistoryDescription": {"message": "If you continue, all entries will be permanently deleted from generator's history. Are you sure you want to continue?"}, "back": {"message": "<PERSON><PERSON><PERSON>"}, "collections": {"message": "Bildumak"}, "nCollections": {"message": "$COUNT$ collections", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "favorites": {"message": "Gogokoak"}, "popOutNewWindow": {"message": "<PERSON><PERSON><PERSON> le<PERSON>o be<PERSON>"}, "refresh": {"message": "Freskatu"}, "cards": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "identities": {"message": "Identitateak"}, "logins": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "secureNotes": {"message": "<PERSON><PERSON>"}, "sshKeys": {"message": "SSH Keys"}, "clear": {"message": "Ezabat<PERSON>", "description": "To clear something out. example: To clear browser history."}, "checkPassword": {"message": "Egiaztatu pasahitza konprometituta dagoen."}, "passwordExposed": {"message": "Pasahitz hau $VALUE$ aldiz datu-iragazketetan aurkitu da. Aldatu egin beharko zenuke.", "placeholders": {"value": {"content": "$1", "example": "2"}}}, "passwordSafe": {"message": "Pasahitz hau ez da inongo datu-filtrazio ezagunetan aurkitu. Erabiltzea segurua izan beharko luke."}, "baseDomain": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Domain name. Ex. website.com"}, "baseDomainOptionRecommended": {"message": "Base domain (recommended)", "description": "Domain name. Ex. website.com"}, "domainName": {"message": "<PERSON><PERSON><PERSON>", "description": "Domain name. Ex. website.com"}, "host": {"message": "Ostalaria", "description": "A URL's host value. For example, the host of https://sub.domain.com:443 is 'sub.domain.com:443'."}, "exact": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "startsWith": {"message": "<PERSON><PERSON> honekin"}, "regEx": {"message": "Expresio erregu<PERSON>", "description": "A programming term, also known as 'RegEx'."}, "matchDetection": {"message": "<PERSON><PERSON><PERSON> modua", "description": "URI match detection for autofill."}, "defaultMatchDetection": {"message": "Lehenetsitako detekzio modua", "description": "Default URI match detection for autofill."}, "toggleOptions": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> au<PERSON>"}, "toggleCurrentUris": {"message": "Uneko URI-ak txandakatu", "description": "Toggle the display of the URIs of the currently open tabs in the browser."}, "currentUri": {"message": "Uneko URI-a", "description": "The URI of one of the current open tabs in the browser."}, "organization": {"message": "Era<PERSON><PERSON>a", "description": "An entity of multiple related people (ex. a team or business organization)."}, "types": {"message": "Motak"}, "allItems": {"message": "Elementu guztiak"}, "noPasswordsInList": {"message": "<PERSON>z dago erakusteko p<PERSON>hitzik."}, "clearHistory": {"message": "Clear history"}, "nothingToShow": {"message": "Nothing to show"}, "nothingGeneratedRecently": {"message": "You haven't generated anything recently"}, "remove": {"message": "Ezabat<PERSON>"}, "default": {"message": "Lehenetsia"}, "dateUpdated": {"message": "Eguneratua", "description": "ex. Date this item was updated"}, "dateCreated": {"message": "Sortuta", "description": "ex. Date this item was created"}, "datePasswordUpdated": {"message": "Pasahitza eguneratu da", "description": "ex. Date this password was updated"}, "neverLockWarning": {"message": "<PERSON><PERSON><PERSON> \"Inoiz ez\" aukera erabili nahi duzula? <PERSON>ure blokeo aukerak \"Inoiz ez\" bezala konfiguratzeak kutxa gotorraren zifratze-gakoa gailuan gordetzen du. Aukera hau erabiltzen baduzu, gailua behar bezala babestuta duzula ziurtatu behar duzu."}, "noOrganizationsList": {"message": "Zu ez zara inongo erakundekoa. Erakundeek elementuak beste erabiltzaile batzuekin modu seguruan partekatzeko aukera ematen dute."}, "noCollectionsInList": {"message": "<PERSON>z dago erakusteko bi<PERSON>."}, "ownership": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "whoOwnsThisItem": {"message": "Nork du elementu hau?"}, "strong": {"message": "Sendoa", "description": "ex. A strong password. Scale: Weak -> Good -> Strong"}, "good": {"message": "<PERSON><PERSON>", "description": "ex. A good password. Scale: Weak -> Good -> Strong"}, "weak": {"message": "<PERSON><PERSON>", "description": "ex. A weak password. Scale: Weak -> Good -> Strong"}, "weakMasterPassword": {"message": "<PERSON><PERSON><PERSON><PERSON> nagusi ahula"}, "weakMasterPasswordDesc": {"message": "Aukeratu duzun pasahitza ahula da. Pasa<PERSON>z nagusi sendo bat (edo pasa<PERSON>ald<PERSON> bat) erabili beharko zenuke <PERSON>ward<PERSON> kontua behar bezala babe<PERSON>. Z<PERSON>r zaude pasahitz nagusi hau erabili nahi duzula?"}, "pin": {"message": "PIN-a", "description": "PIN code. Ex. The short code (often numeric) that you use to unlock a device."}, "unlockWithPin": {"message": "PIN-are<PERSON>"}, "setYourPinTitle": {"message": "Set PIN"}, "setYourPinButton": {"message": "Set PIN"}, "setYourPinCode": {"message": "<PERSON><PERSON><PERSON> zure PIN kodea Bitwarden desblokeatzeko. Zure PIN-aren konfigurazioa berrezarrik<PERSON>, noizbait aplikaziotik erabat saioa ixten baduzu."}, "setPinCode": {"message": "You can use this PIN to unlock Bitwarden. Your PIN will be reset if you ever fully log out of the application."}, "pinRequired": {"message": "PIN-a beharrezkoa da."}, "invalidPin": {"message": "PIN baliogabea."}, "tooManyInvalidPinEntryAttemptsLoggingOut": {"message": "Too many invalid PIN entry attempts. Logging out."}, "unlockWithBiometrics": {"message": "Desblokeatu biometria erabiliz"}, "unlockWithMasterPassword": {"message": "Unlock with master password"}, "awaitDesktop": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> aldetiko berre<PERSON> zain"}, "awaitDesktopDesc": {"message": "<PERSON><PERSON><PERSON>, egiaztatu biometrikoen erabilera Mahaigaineko Bitwarden aplikazioan, nabigatzailerako biometrikoak gaitzeko."}, "lockWithMasterPassOnRestart": {"message": "Nabigatzailea berrabiaraztean pasa<PERSON>z nagusiar<PERSON> blokeatu"}, "lockWithMasterPassOnRestart1": {"message": "Require master password on browser restart"}, "selectOneCollection": {"message": "Gutxienez bilduma bat aukeratu behar duzu."}, "cloneItem": {"message": "Klonatu elementua"}, "clone": {"message": "Klonatu"}, "passwordGenerator": {"message": "Password generator"}, "usernameGenerator": {"message": "Username generator"}, "useThisEmail": {"message": "Use this email"}, "useThisPassword": {"message": "Use this password"}, "useThisPassphrase": {"message": "Use this passphrase"}, "useThisUsername": {"message": "Use this username"}, "securePasswordGenerated": {"message": "Secure password generated! Don't forget to also update your password on the website."}, "useGeneratorHelpTextPartOne": {"message": "Use the generator", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "useGeneratorHelpTextPartTwo": {"message": "to create a strong unique password", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "vaultCustomization": {"message": "Vault customization"}, "vaultTimeoutAction": {"message": "Kutxa gotorraren itxaronaldiaren ekintza"}, "vaultTimeoutAction1": {"message": "Timeout action"}, "lock": {"message": "Blokeatu", "description": "Verb form: to make secure or inaccessible by"}, "trash": {"message": "Zakarrontzia", "description": "Noun: a special folder to hold deleted items"}, "searchTrash": {"message": "<PERSON><PERSON><PERSON>"}, "permanentlyDeleteItem": {"message": "Ezabatu elementua bet<PERSON>"}, "permanentlyDeleteItemConfirmation": {"message": "<PERSON><PERSON><PERSON> zaude elementu hau betirako ezabatu nahi duzula?"}, "permanentlyDeletedItem": {"message": "Elementua betirako ezabatua"}, "restoreItem": {"message": "Berresk<PERSON><PERSON>"}, "restoredItem": {"message": "Elementua berreskuratua"}, "alreadyHaveAccount": {"message": "Already have an account?"}, "vaultTimeoutLogOutConfirmation": {"message": "Saioa ixteak kutxa gotorreko sarrera guztia kenduko du eta itxaronaldiaren ondoren lineako autentifikazioa eskatuko du. Z<PERSON>r zaude hau egin nahi duzula?"}, "vaultTimeoutLogOutConfirmationTitle": {"message": "Baieztatu itxaronaldiaren ekintza"}, "autoFillAndSave": {"message": "Auto-bete eta gorde"}, "fillAndSave": {"message": "Fill and save"}, "autoFillSuccessAndSavedUri": {"message": "Elementua auto-betea eta URIa gordeta"}, "autoFillSuccess": {"message": "Elementua auto-beteta"}, "insecurePageWarning": {"message": "Warning: This is an unsecured HTTP page, and any information you submit can potentially be seen and changed by others. This Login was originally saved on a secure (HTTPS) page."}, "insecurePageWarningFillPrompt": {"message": "Do you still wish to fill this login?"}, "autofillIframeWarning": {"message": "The form is hosted by a different domain than the URI of your saved login. <PERSON>ose OK to autofill anyway, or Cancel to stop."}, "autofillIframeWarningTip": {"message": "To prevent this warning in the future, save this URI, $HOSTNAME$, to your Bitwarden login item for this site.", "placeholders": {"hostname": {"content": "$1", "example": "www.example.com"}}}, "setMasterPassword": {"message": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> na<PERSON>ia"}, "currentMasterPass": {"message": "Oraingo pasahitz nagusia"}, "newMasterPass": {"message": "<PERSON><PERSON><PERSON><PERSON> nagusi be<PERSON>a"}, "confirmNewMasterPass": {"message": "Confirm new master password"}, "masterPasswordPolicyInEffect": {"message": "Erakundeko politika batek edo gehiagok pasahitz nagusia behar dute baldintza hauek betetzeko:"}, "policyInEffectMinComplexity": {"message": "$SCORE$-en gutxieneko konplexutasun puntuazioa", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "policyInEffectMinLength": {"message": "$LENGTH$-en gutxieneko luzera", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "policyInEffectUppercase": {"message": "Karakt<PERSON> larri bat edo gehiago edukitzea"}, "policyInEffectLowercase": {"message": "Karaktere txiki bat edo gehiago edukitzea"}, "policyInEffectNumbers": {"message": "Zenbaki bat edo gehiago edukitzea"}, "policyInEffectSpecial": {"message": "<PERSON><PERSON><PERSON> berezi <PERSON> ($CHARS$) bat edo gehiago edukitzea", "placeholders": {"chars": {"content": "$1", "example": "!@#$%^&*"}}}, "masterPasswordPolicyRequirementsNotMet": {"message": "<PERSON><PERSON> pasahitz nagusi berriak ez ditu baldintzak betetzen."}, "receiveMarketingEmailsV2": {"message": "Get advice, announcements, and research opportunities from Bitwarden in your inbox."}, "unsubscribe": {"message": "Unsubscribe"}, "atAnyTime": {"message": "at any time."}, "byContinuingYouAgreeToThe": {"message": "By continuing, you agree to the"}, "and": {"message": "and"}, "acceptPolicies": {"message": "Laukitxo hau mark<PERSON>, honakoa onartzen duzu:"}, "acceptPoliciesRequired": {"message": "Zerbitzuaren <PERSON> eta pribatutasun politika ez dira onartu."}, "termsOfService": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "privacyPolicy": {"message": "Pribatutasun politika"}, "yourNewPasswordCannotBeTheSameAsYourCurrentPassword": {"message": "Your new password cannot be the same as your current password."}, "hintEqualsPassword": {"message": "<PERSON><PERSON> pasahitza ezin da izan zure pasahitzaren pistaren berdina."}, "ok": {"message": "<PERSON><PERSON>"}, "errorRefreshingAccessToken": {"message": "Access Token Refresh Error"}, "errorRefreshingAccessTokenDesc": {"message": "No refresh token or API keys found. Please try logging out and logging back in."}, "desktopSyncVerificationTitle": {"message": "Ma<PERSON><PERSON><PERSON>ko sinkronizazioaren egiaztatzea"}, "desktopIntegrationVerificationText": {"message": "<PERSON><PERSON><PERSON>, egiaztatu mahaigaineko aplikazioak hatz-marka digital hau erakusten duela: "}, "desktopIntegrationDisabledTitle": {"message": "Nabigatzailearen integrazioa ez dago gaituta"}, "desktopIntegrationDisabledDesc": {"message": "Nabigatzailearen integrazioa ez dago gaituta mahaigaineko Bitwarden aplikazioan. Me<PERSON>ez, gaitu mahaigaineko aplikazioko ezarpenetan."}, "startDesktopTitle": {"message": "<PERSON><PERSON> <PERSON>warden aplikazioa"}, "startDesktopDesc": {"message": "Biometria bidez desblokeatu aurretik mahaigaineko Bitwarden aplikazioak hasita egon behar du."}, "errorEnableBiometricTitle": {"message": "<PERSON><PERSON> izan da biometria gaitu"}, "errorEnableBiometricDesc": {"message": "Mahaigaineko aplikazioak ekintza geldiarazi du"}, "nativeMessagingInvalidEncryptionDesc": {"message": "Mahaigaineko aplikazioak komunikazio kanal segurua geldiarazi du<PERSON>, sa<PERSON><PERSON> be<PERSON>ro"}, "nativeMessagingInvalidEncryptionTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> komunikazioa eten da"}, "nativeMessagingWrongUserDesc": {"message": "Mahaigaineko aplikazioa beste kontu batekin konektatu da<PERSON>, ziurtatu bi aplikazioak kontu berean konektatzen direla."}, "nativeMessagingWrongUserTitle": {"message": "Kontu ezberdinak dira"}, "nativeMessagingWrongUserKeyTitle": {"message": "Biometric key missmatch"}, "nativeMessagingWrongUserKeyDesc": {"message": "Biometric unlock failed. The biometric secret key failed to unlock the vault. Please try to set up biometrics again."}, "biometricsNotEnabledTitle": {"message": "Biometria desgaitua"}, "biometricsNotEnabledDesc": {"message": "Nabigatzailearen biometriak lehenik mahaigainaren biometria gaitzeko eskatzen du."}, "biometricsNotSupportedTitle": {"message": "Ezin da biometria erabili"}, "biometricsNotSupportedDesc": {"message": "Nabigatzailearen biometria ezin da gailu honetan erabili."}, "biometricsNotUnlockedTitle": {"message": "User locked or logged out"}, "biometricsNotUnlockedDesc": {"message": "Please unlock this user in the desktop application and try again."}, "biometricsNotAvailableTitle": {"message": "Biometric unlock unavailable"}, "biometricsNotAvailableDesc": {"message": "Biometric unlock is currently unavailable. Please try again later."}, "biometricsFailedTitle": {"message": "Biometrics failed"}, "biometricsFailedDesc": {"message": "Biometrics cannot be completed, consider using a master password or logging out. If this persists, please contact Bitwarden support."}, "nativeMessaginPermissionErrorTitle": {"message": "<PERSON><PERSON><PERSON> ukatuta"}, "nativeMessaginPermissionErrorDesc": {"message": "Mahaigaineko Bitwarden aplikazioarekin komunikatzeko baimen<PERSON>abe, ezin diogu datu biometrikorik eman nabigatzailearen gehigarriari. <PERSON><PERSON><PERSON>, sa<PERSON><PERSON> be<PERSON>."}, "nativeMessaginPermissionSidebarTitle": {"message": "<PERSON><PERSON><PERSON> baimen eska<PERSON>"}, "nativeMessaginPermissionSidebarDesc": {"message": "Ekintza hau ezin da alboko barran egin. <PERSON><PERSON><PERSON> berriro leiho gain<PERSON> edo leiho berri batean."}, "personalOwnershipSubmitError": {"message": "Erakundeko politika bat dela eta, ezin dituzu elementuak zure kutxa gotor pertsonalean gorde. Aldatu jabe aukera erakunde aukera batera, eta aukeratu bilduma erabilgarrien artean."}, "personalOwnershipPolicyInEffect": {"message": "Erakunde politika batek, jabetza aukerei eragiten die."}, "personalOwnershipPolicyInEffectImports": {"message": "An organization policy has blocked importing items into your individual vault."}, "domainsTitle": {"message": "Domains", "description": "A category title describing the concept of web domains"}, "blockedDomains": {"message": "Blocked domains"}, "learnMoreAboutBlockedDomains": {"message": "Learn more about blocked domains"}, "excludedDomains": {"message": "Kanporatutako dome<PERSON>uak"}, "excludedDomainsDesc": {"message": "Bitwardenek ez du eskatuko domeinu horietarako saio-hasierako xeheta<PERSON>ak gordetzea. Orrialdea eguneratu behar duzu aldaketek eragina izan <PERSON>."}, "excludedDomainsDescAlt": {"message": "Bitwarden will not ask to save login details for these domains for all logged in accounts. You must refresh the page for changes to take effect."}, "blockedDomainsDesc": {"message": "Autofill and other related features will not be offered for these websites. You must refresh the page for changes to take effect."}, "autofillBlockedNoticeV2": {"message": "Autofill is blocked for this website."}, "autofillBlockedNoticeGuidance": {"message": "Change this in settings"}, "change": {"message": "Change"}, "changePassword": {"message": "Change password", "description": "Change password button for browser at risk notification on login."}, "changeButtonTitle": {"message": "Change password - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "atRiskPassword": {"message": "At-risk password"}, "atRiskPasswords": {"message": "At-risk passwords"}, "atRiskPasswordDescSingleOrg": {"message": "$ORGANIZATION$ is requesting you change one password because it is at-risk.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}}, "atRiskPasswordsDescSingleOrgPlural": {"message": "$ORGANIZATION$ is requesting you change the $COUNT$ passwords because they are at-risk.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}, "count": {"content": "$2", "example": "2"}}}, "atRiskPasswordsDescMultiOrgPlural": {"message": "Your organizations are requesting you change the $COUNT$ passwords because they are at-risk.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "atRiskChangePrompt": {"message": "Your password for this site is at-risk. $ORGANIZATION$ has requested that you change it.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}, "description": "Notification body when a login triggers an at-risk password change request and the change password domain is known."}, "atRiskNavigatePrompt": {"message": "$ORGANIZATION$ wants you to change this password because it is at-risk. Navigate to your account settings to change the password.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}, "description": "Notification body when a login triggers an at-risk password change request and no change password domain is provided."}, "reviewAndChangeAtRiskPassword": {"message": "Review and change one at-risk password"}, "reviewAndChangeAtRiskPasswordsPlural": {"message": "Review and change $COUNT$ at-risk passwords", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "changeAtRiskPasswordsFaster": {"message": "Change at-risk passwords faster"}, "changeAtRiskPasswordsFasterDesc": {"message": "Update your settings so you can quickly autofill your passwords and generate new ones"}, "reviewAtRiskLogins": {"message": "Review at-risk logins"}, "reviewAtRiskPasswords": {"message": "Review at-risk passwords"}, "reviewAtRiskLoginsSlideDesc": {"message": "Your organization passwords are at-risk because they are weak, reused, and/or exposed.", "description": "Description of the review at-risk login slide on the at-risk password page carousel"}, "reviewAtRiskLoginSlideImgAltPeriod": {"message": "Illustration of a list of logins that are at-risk."}, "generatePasswordSlideDesc": {"message": "Quickly generate a strong, unique password with the Bitwarden autofill menu on the at-risk site.", "description": "Description of the generate password slide on the at-risk password page carousel"}, "generatePasswordSlideImgAltPeriod": {"message": "Illustration of the Bitwarden autofill menu displaying a generated password."}, "updateInBitwarden": {"message": "Update in Bitwarden"}, "updateInBitwardenSlideDesc": {"message": "Bitwarden will then prompt you to update the password in the password manager.", "description": "Description of the update in Bitwarden slide on the at-risk password page carousel"}, "updateInBitwardenSlideImgAltPeriod": {"message": "Illustration of a Bitwarden’s notification prompting the user to update the login."}, "turnOnAutofill": {"message": "Turn on autofill"}, "turnedOnAutofill": {"message": "Turned on autofill"}, "dismiss": {"message": "<PERSON><PERSON><PERSON>"}, "websiteItemLabel": {"message": "Website $number$ (URI)", "placeholders": {"number": {"content": "$1", "example": "3"}}}, "excludedDomainsInvalidDomain": {"message": "$DOMAIN$ ez da onartutako domeinu bat", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "blockedDomainsSavedSuccess": {"message": "Blocked domain changes saved"}, "excludedDomainsSavedSuccess": {"message": "Excluded domain changes saved"}, "limitSendViews": {"message": "Limit views"}, "limitSendViewsHint": {"message": "No one can view this Send after the limit is reached.", "description": "Displayed under the limit views field on Send"}, "limitSendViewsCount": {"message": "$ACCESSCOUNT$ views left", "description": "Displayed under the limit views field on Send", "placeholders": {"accessCount": {"content": "$1", "example": "2"}}}, "send": {"message": "Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDetails": {"message": "Send details", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendTypeText": {"message": "Test<PERSON>"}, "sendTypeTextToShare": {"message": "Text to share"}, "sendTypeFile": {"message": "Fitxategia"}, "allSends": {"message": "Send guztiak", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "maxAccessCountReached": {"message": "Max access count reached", "description": "This text will be displayed after a Send has been accessed the maximum amount of times."}, "hideTextByDefault": {"message": "Hide text by default"}, "expired": {"message": "<PERSON><PERSON><PERSON>"}, "passwordProtected": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "copyLink": {"message": "Copy link"}, "copySendLink": {"message": "Send esteka kopiatu", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "removePassword": {"message": "<PERSON><PERSON>"}, "delete": {"message": "Ezabat<PERSON>"}, "removedPassword": {"message": "<PERSON><PERSON><PERSON><PERSON> kendua"}, "deletedSend": {"message": "Send-a eza<PERSON>ua", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLink": {"message": "Send esteka", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "disabled": {"message": "Desgaitu<PERSON>"}, "removePasswordConfirmation": {"message": "<PERSON><PERSON><PERSON> zaude pasahitz hau ezabatu nahi duzula?"}, "deleteSend": {"message": "Ezabatu Send-a", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendConfirmation": {"message": "<PERSON><PERSON><PERSON> al zaude Send hau ezabatu nahi duzula?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendPermanentConfirmation": {"message": "Are you sure you want to permanently delete this Send?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editSend": {"message": "Editatu Send-a", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deletionDate": {"message": "Ezabatze data"}, "deletionDateDescV2": {"message": "The Send will be permanently deleted on this date.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "expirationDate": {"message": "Iraungitze data"}, "oneDay": {"message": "Egun 1"}, "days": {"message": "$DAYS$ egun", "placeholders": {"days": {"content": "$1", "example": "2"}}}, "custom": {"message": "Pertsonalizatua"}, "sendPasswordDescV3": {"message": "Add an optional password for recipients to access this Send.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createSend": {"message": "Sortu Send berria", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "newPassword": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sendDisabled": {"message": "Send-a desgaitua", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDisabledWarning": {"message": "Enpresa-politika baten on<PERSON>, lehendik dagoen Send-a bakarrik ezaba dezakezu.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSend": {"message": "Send-a sortua", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSendSuccessfully": {"message": "Send created successfully!", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHoursSingle": {"message": "The Send will be available to anyone with the link for the next 1 hour.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHours": {"message": "The Send will be available to anyone with the link for the next $HOURS$ hours.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"hours": {"content": "$1", "example": "5"}}}, "sendExpiresInDaysSingle": {"message": "The Send will be available to anyone with the link for the next 1 day.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInDays": {"message": "The Send will be available to anyone with the link for the next $DAYS$ days.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"days": {"content": "$1", "example": "5"}}}, "sendLinkCopied": {"message": "Send link copied", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editedSend": {"message": "Send-a editatua", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogText": {"message": "Pop out extension?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogDesc": {"message": "To create a file Send, you need to pop out the extension to a new window.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLinuxChromiumFileWarning": {"message": "Fitxategi bat auker<PERSON><PERSON><PERSON>, ireki gehigarria alboko barran (ahal bada) edo atera leiho berri batera banner honetan klik eginez."}, "sendFirefoxFileWarning": {"message": "Firefox erabiliz fitxategi bat auker<PERSON><PERSON>ko, ireki gehigarria alboko barratik edo ireki beste leiho bat banner hau sakatuz."}, "sendSafariFileWarning": {"message": "Safari erabiliz fitxategi bat auker<PERSON><PERSON>ko, ireki beste leiho bat banner hau sakatuz."}, "popOut": {"message": "Pop out"}, "sendFileCalloutHeader": {"message": "<PERSON><PERSON>"}, "expirationDateIsInvalid": {"message": "Iraungitze data ez da baliozkoa."}, "deletionDateIsInvalid": {"message": "Ezabatze data ez da baliozkoa."}, "expirationDateAndTimeRequired": {"message": "Iraungitze data eta ordua behar dira."}, "deletionDateAndTimeRequired": {"message": "Ezabatze data eta ordua behar dira."}, "dateParsingError": {"message": "Akatsa gertatu da ezabatze eta iraungitze datak gordetzean."}, "hideYourEmail": {"message": "Hide your email address from viewers."}, "passwordPrompt": {"message": "<PERSON><PERSON><PERSON> eskatu pasahitz nagusia"}, "passwordConfirmation": {"message": "Baieztatu pasahitz nagusia"}, "passwordConfirmationDesc": {"message": "Ekintza hau babestuta dago. <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, sartu berriro pasahitz nagusia zure identitatea egiaztatzeko."}, "emailVerificationRequired": {"message": "Egiaztapen emaila beharrezkoa da"}, "emailVerifiedV2": {"message": "Email verified"}, "emailVerificationRequiredDesc": {"message": "Emaila egiaztatu behar duzu funtzio hau erabiltzeko. Emaila web-eko kutxa gotorrean egiazta dezakezu."}, "updatedMasterPassword": {"message": "Pasahitz nagusia eguneratuta"}, "updateMasterPassword": {"message": "Pasahitz nagusia eguneratu"}, "updateMasterPasswordWarning": {"message": "Zure erakundeko administratzaile batek pasahitz nagusia aldatu berri du. Kutxa gotorrera sartzeko, pasahitz nagusia orain eguneratu behar duzu. <PERSON><PERSON>, oraingo saiotik atera eta saioa hasteko eskatuko zaizu. Beste gailu batzuetako saio aktiboek ordubete iraun dezakete aktibo."}, "updateWeakMasterPasswordWarning": {"message": "Your master password does not meet one or more of your organization policies. In order to access the vault, you must update your master password now. Proceeding will log you out of your current session, requiring you to log back in. Active sessions on other devices may continue to remain active for up to one hour."}, "tdeDisabledMasterPasswordRequired": {"message": "Your organization has disabled trusted device encryption. Please set a master password to access your vault."}, "resetPasswordPolicyAutoEnroll": {"message": "Izen-emate automat<PERSON><PERSON>"}, "resetPasswordAutoEnrollInviteWarning": {"message": "Erakunde horrek enpresa politika bat du, eta automatikoki pasahitza berrezartzean izen-emango du. Izen-emateak aukera emango die erakundeko administratzaileei pasahitz nagusia aldatzeko."}, "selectFolder": {"message": "<PERSON><PERSON><PERSON> karpeta..."}, "noFoldersFound": {"message": "No folders found", "description": "Used as a message within the notification bar when no folders are found"}, "orgPermissionsUpdatedMustSetPassword": {"message": "Your organization permissions were updated, requiring you to set a master password.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "orgRequiresYouToSetPassword": {"message": "Your organization requires you to set a master password.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "cardMetrics": {"message": "out of $TOTAL$", "placeholders": {"total": {"content": "$1", "example": "5"}}}, "verificationRequired": {"message": "Verification required", "description": "Default title for the user verification dialog."}, "hours": {"message": "Ordu"}, "minutes": {"message": "<PERSON><PERSON><PERSON>"}, "vaultTimeoutPolicyAffectingOptions": {"message": "Enterprise policy requirements have been applied to your timeout options"}, "vaultTimeoutPolicyInEffect": {"message": "Zure erakundearen politikek zure itxaronaldiari eragiten diote. Itxaronaldiak gehienez ere $HOURS$ ordu eta $MINUTES$ minutu izango ditu", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyInEffect1": {"message": "$HOURS$ hour(s) and $MINUTES$ minute(s) maximum.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyMaximumError": {"message": "Timeout exceeds the restriction set by your organization: $HOURS$ hour(s) and $MINUTES$ minute(s) maximum", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyWithActionInEffect": {"message": "Your organization policies are affecting your vault timeout. Maximum allowed vault timeout is $HOURS$ hour(s) and $MINUTES$ minute(s). Your vault timeout action is set to $ACTION$.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}, "action": {"content": "$3", "example": "Lock"}}}, "vaultTimeoutActionPolicyInEffect": {"message": "Your organization policies have set your vault timeout action to $ACTION$.", "placeholders": {"action": {"content": "$1", "example": "Lock"}}}, "vaultTimeoutTooLarge": {"message": "<PERSON>ure kutxa gotor<PERSON><PERSON>, zure erakundeak ezarritako murrizpenak gainditzen ditu."}, "vaultExportDisabled": {"message": "Kutxa gotorraren esportazioa desgaituta"}, "personalVaultExportPolicyInEffect": {"message": "Erakundeko politika batek edo gehiagok kutxa gotorra esportatzea galarazten dute."}, "copyCustomFieldNameInvalidElement": {"message": "Ez da gai elementu bat behar bezala identifikatzeko. Saiatu HTML bere ordez ikuskatzen."}, "copyCustomFieldNameNotUnique": {"message": "Ez da identifikatzaile bakarrik aurkitu."}, "removeMasterPasswordForOrganizationUserKeyConnector": {"message": "A master password is no longer required for members of the following organization. Please confirm the domain below with your organization administrator."}, "organizationName": {"message": "Organization name"}, "keyConnectorDomain": {"message": "Key Connector domain"}, "leaveOrganization": {"message": "<PERSON><PERSON><PERSON>"}, "removeMasterPassword": {"message": "Ezabatu pasahitz nagusia"}, "removedMasterPassword": {"message": "Pasa<PERSON>z na<PERSON> e<PERSON>."}, "leaveOrganizationConfirmation": {"message": "<PERSON><PERSON><PERSON> al zaude erakundea utzi nahi duzula?"}, "leftOrganization": {"message": "<PERSON><PERSON><PERSON><PERSON> utzi duzu."}, "toggleCharacterCount": {"message": "Karaktere kontaketak txandakatu"}, "sessionTimeout": {"message": "<PERSON><PERSON> amaitu da<PERSON>, itzuli eta saiatu berriro saioa hasten."}, "exportingPersonalVaultTitle": {"message": "Ku<PERSON><PERSON> gotor pertsonala esportatzen"}, "exportingIndividualVaultDescription": {"message": "Only the individual vault items associated with $EMAIL$ will be exported. Organization vault items will not be included. Only vault item information will be exported and will not include associated attachments.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingIndividualVaultWithAttachmentsDescription": {"message": "Only the individual vault items including attachments associated with $EMAIL$ will be exported. Organization vault items will not be included", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingOrganizationVaultTitle": {"message": "Exporting organization vault"}, "exportingOrganizationVaultDesc": {"message": "Only the organization vault associated with $ORGANIZATION$ will be exported. Items in individual vaults or other organizations will not be included.", "placeholders": {"organization": {"content": "$1", "example": "ACME Moving Co."}}}, "error": {"message": "<PERSON><PERSON><PERSON>"}, "decryptionError": {"message": "Decryption error"}, "couldNotDecryptVaultItemsBelow": {"message": "<PERSON><PERSON><PERSON> could not decrypt the vault item(s) listed below."}, "contactCSToAvoidDataLossPart1": {"message": "Contact customer success", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "contactCSToAvoidDataLossPart2": {"message": "to avoid additional data loss.", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "generateUsername": {"message": "Sortu erabiltzaile izena"}, "generateEmail": {"message": "Generate email"}, "spinboxBoundariesHint": {"message": "Value must be between $MIN$ and $MAX$.", "description": "Explains spin box minimum and maximum values to the user", "placeholders": {"min": {"content": "$1", "example": "8"}, "max": {"content": "$2", "example": "128"}}}, "passwordLengthRecommendationHint": {"message": " Use $RECOMMENDED$ characters or more to generate a strong password.", "description": "Appended to `spinboxBoundariesHint` to recommend a length to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "14"}}}, "passphraseNumWordsRecommendationHint": {"message": " Use $RECOMMENDED$ words or more to generate a strong passphrase.", "description": "Appended to `spinboxBoundariesHint` to recommend a number of words to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "6"}}}, "plusAddressedEmail": {"message": "Atzizkidun emaila", "description": "Username generator option that appends a random sub-address to the username. For example: <EMAIL>"}, "plusAddressedEmailDesc": {"message": "Erabili email<PERSON>n <PERSON> a<PERSON>pi<PERSON>bideratze gaitasunak."}, "catchallEmail": {"message": "Harrapatu email guztiak"}, "catchallEmailDesc": {"message": "Erabili zure domeinuan konfiguratutako sa<PERSON>o on<PERSON>."}, "random": {"message": "Ausazkoa"}, "randomWord": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "websiteName": {"message": "<PERSON>gune izena"}, "service": {"message": "Zerbitzua"}, "forwardedEmail": {"message": "Emaileko e<PERSON>a berbidalia"}, "forwardedEmailDesc": {"message": "Emaileko ezizen bat sortu kanpoko bidalketa zerbitzu batekin."}, "forwarderDomainName": {"message": "Email domain", "description": "Labels the domain name email forwarder service option"}, "forwarderDomainNameHint": {"message": "Choose a domain that is supported by the selected service", "description": "Guidance provided for email forwarding services that support multiple email domains."}, "forwarderError": {"message": "$SERVICENAME$ error: $ERRORMESSAGE$", "description": "Reports an error returned by a forwarding service to the user.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Invalid characters in domain name."}}}, "forwarderGeneratedBy": {"message": "Generated by <PERSON><PERSON><PERSON>.", "description": "Displayed with the address on the forwarding service's configuration screen."}, "forwarderGeneratedByWithWebsite": {"message": "Website: $WEBSITE$. Generated by Bitwarden.", "description": "Displayed with the address on the forwarding service's configuration screen.", "placeholders": {"WEBSITE": {"content": "$1", "example": "www.example.com"}}}, "forwaderInvalidToken": {"message": "Invalid $SERVICENAME$ API token", "description": "Displayed when the user's API token is empty or rejected by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidTokenWithMessage": {"message": "Invalid $SERVICENAME$ API token: $ERRORMESSAGE$", "description": "Displayed when the user's API token is rejected by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwaderInvalidOperation": {"message": "$SERVICENAME$ refused your request. Please contact your service provider for assistance.", "description": "Displayed when the user is forbidden from using the API by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidOperationWithMessage": {"message": "$SERVICENAME$ refused your request: $ERRORMESSAGE$", "description": "Displayed when the user is forbidden from using the API by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwarderNoAccountId": {"message": "Unable to obtain $SERVICENAME$ masked email account ID.", "description": "Displayed when the forwarding service fails to return an account ID.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoDomain": {"message": "Invalid $SERVICENAME$ domain.", "description": "Displayed when the domain is empty or domain authorization failed at the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoUrl": {"message": "Invalid $SERVICENAME$ url.", "description": "Displayed when the url of the forwarding service wasn't supplied.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownError": {"message": "Unknown $SERVICENAME$ error occurred.", "description": "Displayed when the forwarding service failed due to an unknown error.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownForwarder": {"message": "Unknown forwarder: '$SERVICENAME$'.", "description": "Displayed when the forwarding service is not supported.", "placeholders": {"servicename": {"content": "$1", "example": "JustTrust.us"}}}, "hostname": {"message": "Ostalariaren <PERSON>a", "description": "Part of a URL."}, "apiAccessToken": {"message": "Token sarbide API-a"}, "apiKey": {"message": "API Gakoa"}, "ssoKeyConnectorError": {"message": "Errore bat gertatu da Key Connector-ekin: ziurtatu Key Connector erabilgarri dagoela eta behar bezala dabilela."}, "premiumSubcriptionRequired": {"message": "Premium harpidetza behar da"}, "organizationIsDisabled": {"message": "Erakundea desgaituta dago."}, "disabledOrganizationFilterError": {"message": "Ezin da sartu desgaitutako erakundeetako elementuetara. <PERSON><PERSON><PERSON><PERSON>, jar<PERSON> harre<PERSON> zure erakunde<PERSON>."}, "loggingInTo": {"message": "$DOMAIN$-en saioa hasten", "placeholders": {"domain": {"content": "$1", "example": "example.com"}}}, "serverVersion": {"message": "Zerbitzariaren berts<PERSON>"}, "selfHostedServer": {"message": "self-hosted"}, "thirdParty": {"message": "Hirugarrenen aplikazioak"}, "thirdPartyServerMessage": {"message": "Hirugarrenen zerbitzariaren inplementaziora konektatuta, $SERVERNAME$. <PERSON><PERSON><PERSON>, e<PERSON>z<PERSON><PERSON> akats<PERSON> zerbitzari ofiziala erabiliz, edo galdetu hirugarren zerbitzariari.", "placeholders": {"servername": {"content": "$1", "example": "ThirdPartyServerName"}}}, "lastSeenOn": {"message": "Azkenekoz ikusia: $DATE$", "placeholders": {"date": {"content": "$1", "example": "Jun 15, 2015"}}}, "loginWithMasterPassword": {"message": "<PERSON>i sa<PERSON>a p<PERSON><PERSON> na<PERSON>"}, "newAroundHere": {"message": "<PERSON><PERSON><PERSON>?"}, "rememberEmail": {"message": "<PERSON><PERSON><PERSON> go<PERSON>"}, "loginWithDevice": {"message": "Log in with device"}, "fingerprintPhraseHeader": {"message": "Fingerprint phrase"}, "fingerprintMatchInfo": {"message": "Please make sure your vault is unlocked and the Fingerprint phrase matches on the other device."}, "resendNotification": {"message": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>"}, "viewAllLogInOptions": {"message": "View all log in options"}, "notificationSentDevice": {"message": "A notification has been sent to your device."}, "notificationSentDevicePart1": {"message": "Unlock Bitwarden on your device or on the"}, "notificationSentDeviceAnchor": {"message": "web app"}, "notificationSentDevicePart2": {"message": "Make sure the Fingerprint phrase matches the one below before approving."}, "aNotificationWasSentToYourDevice": {"message": "A notification was sent to your device"}, "youWillBeNotifiedOnceTheRequestIsApproved": {"message": "You will be notified once the request is approved"}, "needAnotherOptionV1": {"message": "Need another option?"}, "loginInitiated": {"message": "<PERSON><PERSON> initiated"}, "logInRequestSent": {"message": "Request sent"}, "exposedMasterPassword": {"message": "Exposed Master Password"}, "exposedMasterPasswordDesc": {"message": "Password found in a data breach. Use a unique password to protect your account. Are you sure you want to use an exposed password?"}, "weakAndExposedMasterPassword": {"message": "Weak and Exposed Master Password"}, "weakAndBreachedMasterPasswordDesc": {"message": "Weak password identified and found in a data breach. Use a strong and unique password to protect your account. Are you sure you want to use this password?"}, "checkForBreaches": {"message": "Check known data breaches for this password"}, "important": {"message": "Garrantzitsua:"}, "masterPasswordHint": {"message": "Your master password cannot be recovered if you forget it!"}, "characterMinimum": {"message": "$LENGTH$ karaktere gutxienez", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "autofillPageLoadPolicyActivated": {"message": "Your organization policies have turned on autofill on page load."}, "howToAutofill": {"message": "How to autofill"}, "autofillSelectInfoWithCommand": {"message": "Select an item from this screen, use the shortcut $COMMAND$, or explore other options in settings.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillSelectInfoWithoutCommand": {"message": "Select an item from this screen, or explore other options in settings."}, "gotIt": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "autofillSettings": {"message": "Autofill settings"}, "autofillKeyboardShortcutSectionTitle": {"message": "Autofill shortcut"}, "autofillKeyboardShortcutUpdateLabel": {"message": "Change shortcut"}, "autofillKeyboardManagerShortcutsLabel": {"message": "Manage shortcuts"}, "autofillShortcut": {"message": "Autofill keyboard shortcut"}, "autofillLoginShortcutNotSet": {"message": "The autofill login shortcut is not set. Change this in the browser's settings."}, "autofillLoginShortcutText": {"message": "The autofill login shortcut is $COMMAND$. Manage all shortcuts in the browser's settings.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillShortcutTextSafari": {"message": "Default autofill shortcut: $COMMAND$.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "opensInANewWindow": {"message": "<PERSON><PERSON><PERSON> berri batean i<PERSON> da"}, "rememberThisDeviceToMakeFutureLoginsSeamless": {"message": "Remember this device to make future logins seamless"}, "deviceApprovalRequired": {"message": "Device approval required. Select an approval option below:"}, "deviceApprovalRequiredV2": {"message": "Device approval required"}, "selectAnApprovalOptionBelow": {"message": "Select an approval option below"}, "rememberThisDevice": {"message": "Gogoratu gailu hau"}, "uncheckIfPublicDevice": {"message": "Uncheck if using a public device"}, "approveFromYourOtherDevice": {"message": "On<PERSON><PERSON> zure beste gailutik"}, "requestAdminApproval": {"message": "Eskatu administratzailearen onarpena"}, "ssoIdentifierRequired": {"message": "Organization SSO identifier is required."}, "creatingAccountOn": {"message": "Creating account on"}, "checkYourEmail": {"message": "Check your email"}, "followTheLinkInTheEmailSentTo": {"message": "Follow the link in the email sent to"}, "andContinueCreatingYourAccount": {"message": "and continue creating your account."}, "noEmail": {"message": "No email?"}, "goBack": {"message": "Go back"}, "toEditYourEmailAddress": {"message": "to edit your email address."}, "eu": {"message": "EU", "description": "European Union"}, "accessDenied": {"message": "Access denied. You do not have permission to view this page."}, "general": {"message": "Orokorra"}, "display": {"message": "Bistaratzea"}, "accountSuccessfullyCreated": {"message": "Ko<PERSON>ua zuzen sortu da!"}, "adminApprovalRequested": {"message": "Administratzailearen onarpena eskatuta"}, "adminApprovalRequestSentToAdmins": {"message": "Z<PERSON> eskaera zure administratzaileari bidali zaio."}, "troubleLoggingIn": {"message": "Arazoak saioa hasterakoan?"}, "loginApproved": {"message": "Login approved"}, "userEmailMissing": {"message": "Era<PERSON>tzailearen emaila falta da"}, "activeUserEmailNotFoundLoggingYouOut": {"message": "Active user email not found. Logging you out."}, "deviceTrusted": {"message": "<PERSON><PERSON> trusted"}, "trustOrganization": {"message": "Trust organization"}, "trust": {"message": "Trust"}, "doNotTrust": {"message": "Do not trust"}, "organizationNotTrusted": {"message": "Organization is not trusted"}, "emergencyAccessTrustWarning": {"message": "For the security of your account, only confirm if you have granted emergency access to this user and their fingerprint matches what is displayed in their account"}, "orgTrustWarning": {"message": "For the security of your account, only proceed if you are a member of this organization, have account recovery enabled, and the fingerprint displayed below matches the organization's fingerprint."}, "orgTrustWarning1": {"message": "This organization has an Enterprise policy that will enroll you in account recovery. Enrollment will allow organization administrators to change your password. Only proceed if you recognize this organization and the fingerprint phrase displayed below matches the organization's fingerprint."}, "trustUser": {"message": "Trust user"}, "sendsTitleNoItems": {"message": "Send sensitive information safely", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendsBodyNoItems": {"message": "Share files and data securely with anyone, on any platform. Your information will remain end-to-end encrypted while limiting exposure.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "inputRequired": {"message": "Input is required."}, "required": {"message": "required"}, "search": {"message": "Search"}, "inputMinLength": {"message": "Input must be at least $COUNT$ characters long.", "placeholders": {"count": {"content": "$1", "example": "8"}}}, "inputMaxLength": {"message": "Input must not exceed $COUNT$ characters in length.", "placeholders": {"count": {"content": "$1", "example": "20"}}}, "inputForbiddenCharacters": {"message": "The following characters are not allowed: $CHARACTERS$", "placeholders": {"characters": {"content": "$1", "example": "@, #, $, %"}}}, "inputMinValue": {"message": "Input value must be at least $MIN$.", "placeholders": {"min": {"content": "$1", "example": "8"}}}, "inputMaxValue": {"message": "Input value must not exceed $MAX$.", "placeholders": {"max": {"content": "$1", "example": "100"}}}, "multipleInputEmails": {"message": "1 or more emails are invalid"}, "inputTrimValidator": {"message": "Input must not contain only whitespace.", "description": "Notification to inform the user that a form's input can't contain only whitespace."}, "inputEmail": {"message": "Input is not an email address."}, "fieldsNeedAttention": {"message": "$COUNT$ field(s) above need your attention.", "placeholders": {"count": {"content": "$1", "example": "4"}}}, "singleFieldNeedsAttention": {"message": "1 field needs your attention."}, "multipleFieldsNeedAttention": {"message": "$COUNT$ fields need your attention.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "selectPlaceholder": {"message": "-- Select --"}, "multiSelectPlaceholder": {"message": "-- Type to filter --"}, "multiSelectLoading": {"message": "Retrieving options..."}, "multiSelectNotFound": {"message": "No items found"}, "multiSelectClearAll": {"message": "Clear all"}, "plusNMore": {"message": "+ $QUANTITY$ more", "placeholders": {"quantity": {"content": "$1", "example": "5"}}}, "submenu": {"message": "Submenu"}, "toggleCollapse": {"message": "Toggle collapse", "description": "Toggling an expand/collapse state."}, "aliasDomain": {"message": "Alias domain"}, "passwordRepromptDisabledAutofillOnPageLoad": {"message": "Items with master password re-prompt cannot be autofilled on page load. Autofill on page load turned off.", "description": "Toast message for describing that master password re-prompt cannot be autofilled on page load."}, "autofillOnPageLoadSetToDefault": {"message": "Autofill on page load set to use default setting.", "description": "Toast message for informing the user that autofill on page load has been set to the default setting."}, "turnOffMasterPasswordPromptToEditField": {"message": "Turn off master password re-prompt to edit this field", "description": "Message appearing below the autofill on load message when master password reprompt is set for a vault item."}, "toggleSideNavigation": {"message": "Toggle side navigation"}, "skipToContent": {"message": "Skip to content"}, "bitwardenOverlayButton": {"message": "Bitwarden autofill menu button", "description": "Page title for the iframe containing the overlay button"}, "toggleBitwardenVaultOverlay": {"message": "Toggle Bitwarden autofill menu", "description": "Screen reader and tool tip label for the overlay button"}, "bitwardenVault": {"message": "Bitwarden autofill menu", "description": "Page title in overlay"}, "unlockYourAccountToViewMatchingLogins": {"message": "Unlock your account to view matching logins", "description": "Text to display in overlay when the account is locked."}, "unlockYourAccountToViewAutofillSuggestions": {"message": "Unlock your account to view autofill suggestions", "description": "Text to display in overlay when the account is locked."}, "unlockAccount": {"message": "Unlock account", "description": "Button text to display in overlay when the account is locked."}, "unlockAccountAria": {"message": "Unlock your account, opens in a new window", "description": "Screen reader text (aria-label) for unlock account button in overlay"}, "totpCodeAria": {"message": "Time-based One-Time Password Verification Code", "description": "Aria label for the totp code displayed in the inline menu for autofill"}, "totpSecondsSpanAria": {"message": "Time remaining before current TOTP expires", "description": "Aria label for the totp seconds displayed in the inline menu for autofill"}, "fillCredentialsFor": {"message": "Fill credentials for", "description": "Screen reader text for when overlay item is in focused"}, "partialUsername": {"message": "Partial username", "description": "Screen reader text for when a login item is focused where a partial username is displayed. SR will announce this phrase before reading the text of the partial username"}, "noItemsToShow": {"message": "<PERSON>z dago elementurik erakusteko", "description": "Text to show in overlay if there are no matching items"}, "newItem": {"message": "<PERSON><PERSON><PERSON> be<PERSON>", "description": "Button text to display in overlay when there are no matching items"}, "addNewVaultItem": {"message": "Add new vault item", "description": "Screen reader text (aria-label) for new item button in overlay"}, "newLogin": {"message": "New login", "description": "Button text to display within inline menu when there are no matching items on a login field"}, "addNewLoginItemAria": {"message": "Add new vault login item, opens in a new window", "description": "Screen reader text (aria-label) for new login button within inline menu"}, "newCard": {"message": "New card", "description": "Button text to display within inline menu when there are no matching items on a credit card field"}, "addNewCardItemAria": {"message": "Add new vault card item, opens in a new window", "description": "Screen reader text (aria-label) for new card button within inline menu"}, "newIdentity": {"message": "New identity", "description": "Button text to display within inline menu when there are no matching items on an identity field"}, "addNewIdentityItemAria": {"message": "Add new vault identity item, opens in a new window", "description": "Screen reader text (aria-label) for new identity button within inline menu"}, "bitwardenOverlayMenuAvailable": {"message": "Bitwarden autofill menu available. Press the down arrow key to select.", "description": "Screen reader text for announcing when the overlay opens on the page"}, "turnOn": {"message": "Piztu"}, "ignore": {"message": "Ezikusi"}, "importData": {"message": "Inportatu datuak", "description": "Used for the header of the import dialog, the import button and within the file-password-prompt"}, "importError": {"message": "Errorea inportatzerakoan"}, "importErrorDesc": {"message": "Inportatzen saiatu zaren datuekin arazo bat egon da. <PERSON>, konpondu ondoren adierazten diren akatsak eta saiatu berriro."}, "resolveTheErrorsBelowAndTryAgain": {"message": "Konpondu beheko akatsak eta saiatu berriro."}, "description": {"message": "Deskribapena"}, "importSuccess": {"message": "<PERSON><PERSON>ak zuzen inportatu dira"}, "importSuccessNumberOfItems": {"message": "Guztira $AMOUNT$ elementu inportatu dira.", "placeholders": {"amount": {"content": "$1", "example": "2"}}}, "tryAgain": {"message": "<PERSON><PERSON><PERSON>"}, "verificationRequiredForActionSetPinToContinue": {"message": "Verification required for this action. Set a PIN to continue."}, "setPin": {"message": "Set PIN"}, "verifyWithBiometrics": {"message": "Egiaztatu biometria erabiliz"}, "awaitingConfirmation": {"message": "Baieztapenaren zain"}, "couldNotCompleteBiometrics": {"message": "Could not complete biometrics."}, "needADifferentMethod": {"message": "Need a different method?"}, "useMasterPassword": {"message": "Erabili pasahitz nagusia"}, "usePin": {"message": "Erabili PIN kodea"}, "useBiometrics": {"message": "Erabili biometria"}, "enterVerificationCodeSentToEmail": {"message": "Enter the verification code that was sent to your email."}, "resendCode": {"message": "Resend code"}, "total": {"message": "Guz<PERSON><PERSON>"}, "importWarning": {"message": "$ORGANIZATION$(e)ra datuak inportatzen ari zara. Zure datuak erakunde horretako kideekin parteka daitezke. Jarraitu nahi duzu?", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "duoHealthCheckResultsInNullAuthUrlError": {"message": "Error connecting with the Duo service. Use a different two-step login method or contact Duo for assistance."}, "duoRequiredForAccount": {"message": "Duo two-step login is required for your account."}, "popoutExtension": {"message": "Popout extension"}, "launchDuo": {"message": "Launch Duo"}, "importFormatError": {"message": "Data is not formatted correctly. Please check your import file and try again."}, "importNothingError": {"message": "Nothing was imported."}, "importEncKeyError": {"message": "Error decrypting the exported file. Your encryption key does not match the encryption key used export the data."}, "invalidFilePassword": {"message": "Invalid file password, please use the password you entered when you created the export file."}, "destination": {"message": "Destination"}, "learnAboutImportOptions": {"message": "Learn about your import options"}, "selectImportFolder": {"message": "Select a folder"}, "selectImportCollection": {"message": "Select a collection"}, "importTargetHint": {"message": "Select this option if you want the imported file contents moved to a $DESTINATION$", "description": "Located as a hint under the import target. Will be appended by either folder or collection, depending if the user is importing into an individual or an organizational vault.", "placeholders": {"destination": {"content": "$1", "example": "folder or collection"}}}, "importUnassignedItemsError": {"message": "File contains unassigned items."}, "selectFormat": {"message": "Select the format of the import file"}, "selectImportFile": {"message": "Select the import file"}, "chooseFile": {"message": "Choose <PERSON>"}, "noFileChosen": {"message": "No file chosen"}, "orCopyPasteFileContents": {"message": "or copy/paste the import file contents"}, "instructionsFor": {"message": "$NAME$ Instructions", "description": "The title for the import tool instructions.", "placeholders": {"name": {"content": "$1", "example": "LastPass (csv)"}}}, "confirmVaultImport": {"message": "Confirm vault import"}, "confirmVaultImportDesc": {"message": "This file is password-protected. Please enter the file password to import data."}, "confirmFilePassword": {"message": "Confirm file password"}, "exportSuccess": {"message": "Vault data exported"}, "typePasskey": {"message": "Passkey"}, "accessing": {"message": "Accessing"}, "loggedInExclamation": {"message": "Logged in!"}, "passkeyNotCopied": {"message": "Passkey will not be copied"}, "passkeyNotCopiedAlert": {"message": "The passkey will not be copied to the cloned item. Do you want to continue cloning this item?"}, "passkeyFeatureIsNotImplementedForAccountsWithoutMasterPassword": {"message": "Verification required by the initiating site. This feature is not yet implemented for accounts without master password."}, "logInWithPasskeyQuestion": {"message": "Log in with passkey?"}, "passkeyAlreadyExists": {"message": "A passkey already exists for this application."}, "noPasskeysFoundForThisApplication": {"message": "No passkeys found for this application."}, "noMatchingPasskeyLogin": {"message": "You do not have a matching login for this site."}, "noMatchingLoginsForSite": {"message": "No matching logins for this site"}, "searchSavePasskeyNewLogin": {"message": "Search or save passkey as new login"}, "confirm": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "savePasskey": {"message": "Save passkey"}, "savePasskeyNewLogin": {"message": "Save passkey as new login"}, "chooseCipherForPasskeySave": {"message": "Choose a login to save this passkey to"}, "chooseCipherForPasskeyAuth": {"message": "Choose a passkey to log in with"}, "passkeyItem": {"message": "<PERSON><PERSON> Item"}, "overwritePasskey": {"message": "Overwrite passkey?"}, "overwritePasskeyAlert": {"message": "This item already contains a passkey. Are you sure you want to overwrite the current passkey?"}, "featureNotSupported": {"message": "Feature not yet supported"}, "yourPasskeyIsLocked": {"message": "Authentication required to use passkey. Verify your identity to continue."}, "multifactorAuthenticationCancelled": {"message": "Multifactor authentication cancelled"}, "noLastPassDataFound": {"message": "No LastPass data found"}, "incorrectUsernameOrPassword": {"message": "Incorrect username or password"}, "incorrectPassword": {"message": "Incorrect password"}, "incorrectCode": {"message": "Incorrect code"}, "incorrectPin": {"message": "Incorrect PIN"}, "multifactorAuthenticationFailed": {"message": "Multifactor authentication failed"}, "includeSharedFolders": {"message": "Include shared folders"}, "lastPassEmail": {"message": "LastPass Email"}, "importingYourAccount": {"message": "Importing your account..."}, "lastPassMFARequired": {"message": "LastPass multifactor authentication required"}, "lastPassMFADesc": {"message": "Enter your one-time passcode from your authentication app"}, "lastPassOOBDesc": {"message": "Approve the login request in your authentication app or enter a one-time passcode."}, "passcode": {"message": "Passcode"}, "lastPassMasterPassword": {"message": "LastPass master password"}, "lastPassAuthRequired": {"message": "LastPass authentication required"}, "awaitingSSO": {"message": "Awaiting SSO authentication"}, "awaitingSSODesc": {"message": "Please continue to log in using your company credentials."}, "seeDetailedInstructions": {"message": "See detailed instructions on our help site at", "description": "This is followed a by a hyperlink to the help website."}, "importDirectlyFromLastPass": {"message": "Import directly from LastPass"}, "importFromCSV": {"message": "Import from CSV"}, "lastPassTryAgainCheckEmail": {"message": "Try again or look for an email from LastPass to verify it's you."}, "collection": {"message": "Collection"}, "lastPassYubikeyDesc": {"message": "Insert the YubiKey associated with your LastPass account into your computer's USB port, then touch its button."}, "switchAccount": {"message": "Switch account"}, "switchAccounts": {"message": "Switch accounts"}, "switchToAccount": {"message": "Switch to account"}, "activeAccount": {"message": "Active account"}, "bitwardenAccount": {"message": "Bitwarden account"}, "availableAccounts": {"message": "Available accounts"}, "accountLimitReached": {"message": "Account limit reached. Log out of an account to add another."}, "active": {"message": "active"}, "locked": {"message": "locked"}, "unlocked": {"message": "unlocked"}, "server": {"message": "server"}, "hostedAt": {"message": "hosted at"}, "useDeviceOrHardwareKey": {"message": "Use your device or hardware key"}, "justOnce": {"message": "Just once"}, "alwaysForThisSite": {"message": "Always for this site"}, "domainAddedToExcludedDomains": {"message": "$DOMAIN$ added to excluded domains.", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "commonImportFormats": {"message": "Common formats", "description": "Label indicating the most common import formats"}, "confirmContinueToBrowserSettingsTitle": {"message": "Continue to browser settings?", "description": "Title for dialog which asks if the user wants to proceed to a relevant browser settings page"}, "confirmContinueToHelpCenter": {"message": "Continue to Help Center?", "description": "Title for dialog which asks if the user wants to proceed to a relevant Help Center page"}, "confirmContinueToHelpCenterPasswordManagementContent": {"message": "Change your browser's autofill and password management settings.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser password management settings"}, "confirmContinueToHelpCenterKeyboardShortcutsContent": {"message": "You can view and set extension shortcuts in your browser's settings.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser keyboard shortcut settings"}, "confirmContinueToBrowserPasswordManagementSettingsContent": {"message": "Change your browser's autofill and password management settings.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's password management settings page"}, "confirmContinueToBrowserKeyboardShortcutSettingsContent": {"message": "You can view and set extension shortcuts in your browser's settings.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's keyboard shortcut settings page"}, "overrideDefaultBrowserAutofillTitle": {"message": "Make <PERSON><PERSON><PERSON> your default password manager?", "description": "Dialog title facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutofillDescription": {"message": "Ignoring this option may cause conflicts between Bitwarden autofill suggestions and your browser's.", "description": "Dialog message facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutoFillSettings": {"message": "Make Bitwarden your default password manager", "description": "Label for the setting that allows overriding the default browser autofill settings"}, "privacyPermissionAdditionNotGrantedTitle": {"message": "Unable to set <PERSON><PERSON><PERSON> as the default password manager", "description": "Title for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "privacyPermissionAdditionNotGrantedDescription": {"message": "You must grant browser privacy permissions to Bitwarden to set it as the default password manager.", "description": "Description for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "makeDefault": {"message": "Make default", "description": "Button text for the setting that allows overriding the default browser autofill settings"}, "saveCipherAttemptSuccess": {"message": "Credentials saved successfully!", "description": "Notification message for when saving credentials has succeeded."}, "passwordSaved": {"message": "Password saved!", "description": "Notification message for when saving credentials has succeeded."}, "updateCipherAttemptSuccess": {"message": "Credentials updated successfully!", "description": "Notification message for when updating credentials has succeeded."}, "passwordUpdated": {"message": "Password updated!", "description": "Notification message for when updating credentials has succeeded."}, "saveCipherAttemptFailed": {"message": "Error saving credentials. Check console for details.", "description": "Notification message for when saving credentials has failed."}, "success": {"message": "Success"}, "removePasskey": {"message": "Remove passkey"}, "passkeyRemoved": {"message": "Passkey removed"}, "autofillSuggestions": {"message": "Autofill suggestions"}, "itemSuggestions": {"message": "Suggested items"}, "autofillSuggestionsTip": {"message": "Save a login item for this site to autofill"}, "yourVaultIsEmpty": {"message": "Your vault is empty"}, "noItemsMatchSearch": {"message": "No items match your search"}, "clearFiltersOrTryAnother": {"message": "Clear filters or try another search term"}, "copyInfoTitle": {"message": "Copy info - $ITEMNAME$", "description": "Title for a button that opens a menu with options to copy information from an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "copyNoteTitle": {"message": "Copy Note - $ITEMNAME$", "description": "Title for a button copies a note to the clipboard.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Note Item"}}}, "moreOptionsLabel": {"message": "More options, $ITEMNAME$", "description": "Aria label for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "moreOptionsTitle": {"message": "More options - $ITEMNAME$", "description": "Title for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitle": {"message": "View item - $ITEMNAME$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitleWithField": {"message": "View item - $ITEMNAME$ - $FIELD$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "autofillTitle": {"message": "Autofill - $ITEMNAME$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "autofillTitleWithField": {"message": "Autofill - $ITEMNAME$ - $FIELD$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "copyFieldValue": {"message": "Copy $FIELD$, $VALUE$", "description": "Title for a button that copies a field value to the clipboard.", "placeholders": {"field": {"content": "$1", "example": "Username"}, "value": {"content": "$2", "example": "Foo"}}}, "noValuesToCopy": {"message": "No values to copy"}, "assignToCollections": {"message": "Assign to collections"}, "copyEmail": {"message": "Copy email"}, "copyPhone": {"message": "Copy phone"}, "copyAddress": {"message": "Copy address"}, "adminConsole": {"message": "<PERSON><PERSON>"}, "accountSecurity": {"message": "Account security"}, "notifications": {"message": "Notifications"}, "appearance": {"message": "Appearance"}, "errorAssigningTargetCollection": {"message": "Error assigning target collection."}, "errorAssigningTargetFolder": {"message": "Error assigning target folder."}, "viewItemsIn": {"message": "View items in $NAME$", "description": "Button to view the contents of a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "backTo": {"message": "Back to $NAME$", "description": "Navigate back to a previous folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "new": {"message": "New"}, "removeItem": {"message": "Remove $NAME$", "description": "Remove a selected option, such as a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "itemsWithNoFolder": {"message": "Items with no folder"}, "itemDetails": {"message": "Item details"}, "itemName": {"message": "Item name"}, "organizationIsDeactivated": {"message": "Organization is deactivated"}, "owner": {"message": "Owner"}, "selfOwnershipLabel": {"message": "You", "description": "Used as a label to indicate that the user is the owner of an item."}, "contactYourOrgAdmin": {"message": "Items in deactivated organizations cannot be accessed. Contact your organization owner for assistance."}, "additionalInformation": {"message": "Additional information"}, "itemHistory": {"message": "Item history"}, "lastEdited": {"message": "Last edited"}, "ownerYou": {"message": "Owner: You"}, "linked": {"message": "Linked"}, "copySuccessful": {"message": "Copy Successful"}, "upload": {"message": "Upload"}, "addAttachment": {"message": "Add attachment"}, "maxFileSizeSansPunctuation": {"message": "Maximum file size is 500 MB"}, "deleteAttachmentName": {"message": "Delete attachment $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadAttachmentName": {"message": "Download $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadBitwarden": {"message": "Download Bitwarden"}, "downloadBitwardenOnAllDevices": {"message": "Download Bitwarden on all devices"}, "getTheMobileApp": {"message": "Get the mobile app"}, "getTheMobileAppDesc": {"message": "Access your passwords on the go with the Bitwarden mobile app."}, "getTheDesktopApp": {"message": "Get the desktop app"}, "getTheDesktopAppDesc": {"message": "Access your vault without a browser, then set up unlock with biometrics to expedite unlocking in both the desktop app and browser extension."}, "downloadFromBitwardenNow": {"message": "Download from bitwarden.com now"}, "getItOnGooglePlay": {"message": "Get it on Google Play"}, "downloadOnTheAppStore": {"message": "Download on the App Store"}, "permanentlyDeleteAttachmentConfirmation": {"message": "Are you sure you want to permanently delete this attachment?"}, "premium": {"message": "Premium"}, "freeOrgsCannotUseAttachments": {"message": "Free organizations cannot use attachments"}, "filters": {"message": "Filters"}, "filterVault": {"message": "Filter vault"}, "filterApplied": {"message": "One filter applied"}, "filterAppliedPlural": {"message": "$COUNT$ filters applied", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "personalDetails": {"message": "Personal details"}, "identification": {"message": "Identification"}, "contactInfo": {"message": "Contact info"}, "downloadAttachment": {"message": "Download - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Your File"}}}, "cardNumberEndsWith": {"message": "card number ends with", "description": "Used within the inline menu to provide an aria description when users are attempting to fill a card cipher."}, "loginCredentials": {"message": "Login credentials"}, "authenticatorKey": {"message": "Authenticator key"}, "autofillOptions": {"message": "Autofill options"}, "websiteUri": {"message": "Website (URI)"}, "websiteUriCount": {"message": "Website (URI) $COUNT$", "description": "Label for an input field that contains a website URI. The input field is part of a list of fields, and the count indicates the position of the field in the list.", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "websiteAdded": {"message": "Website added"}, "addWebsite": {"message": "Add website"}, "deleteWebsite": {"message": "Delete website"}, "defaultLabel": {"message": "Default ($VALUE$)", "description": "A label that indicates the default value for a field with the current default value in parentheses.", "placeholders": {"value": {"content": "$1", "example": "Base domain"}}}, "showMatchDetection": {"message": "Show match detection $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "hideMatchDetection": {"message": "Hide match detection $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "autoFillOnPageLoad": {"message": "Autofill on page load?"}, "cardExpiredTitle": {"message": "Expired card"}, "cardExpiredMessage": {"message": "If you've renewed it, update the card's information"}, "cardDetails": {"message": "Card details"}, "cardBrandDetails": {"message": "$BRAND$ details", "placeholders": {"brand": {"content": "$1", "example": "Visa"}}}, "enableAnimations": {"message": "Enable animations"}, "showAnimations": {"message": "Show animations"}, "addAccount": {"message": "Add account"}, "loading": {"message": "Loading"}, "data": {"message": "Data"}, "passkeys": {"message": "Passkeys", "description": "A section header for a list of passkeys."}, "passwords": {"message": "Passwords", "description": "A section header for a list of passwords."}, "logInWithPasskeyAriaLabel": {"message": "Log in with passkey", "description": "ARIA label for the inline menu button that logs in with a passkey."}, "assign": {"message": "Assign"}, "bulkCollectionAssignmentDialogDescriptionSingular": {"message": "Only organization members with access to these collections will be able to see the item."}, "bulkCollectionAssignmentDialogDescriptionPlural": {"message": "Only organization members with access to these collections will be able to see the items."}, "bulkCollectionAssignmentWarning": {"message": "You have selected $TOTAL_COUNT$ items. You cannot update $READONLY_COUNT$ of the items because you do not have edit permissions.", "placeholders": {"total_count": {"content": "$1", "example": "10"}, "readonly_count": {"content": "$2"}}}, "addField": {"message": "Add field"}, "add": {"message": "Add"}, "fieldType": {"message": "Field type"}, "fieldLabel": {"message": "Field label"}, "textHelpText": {"message": "Use text fields for data like security questions"}, "hiddenHelpText": {"message": "Use hidden fields for sensitive data like a password"}, "checkBoxHelpText": {"message": "Use checkboxes if you'd like to autofill a form's checkbox, like a remember email"}, "linkedHelpText": {"message": "Use a linked field when you are experiencing autofill issues for a specific website."}, "linkedLabelHelpText": {"message": "Enter the the field's html id, name, aria-label, or placeholder."}, "editField": {"message": "Edit field"}, "editFieldLabel": {"message": "Edit $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "deleteCustomField": {"message": "Delete $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "fieldAdded": {"message": "$LABEL$ added", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderToggleButton": {"message": "Reorder $LABEL$. Use arrow key to move item up or down.", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderWebsiteUriButton": {"message": "Reorder website URI. Use arrow key to move item up or down."}, "reorderFieldUp": {"message": "$LABEL$ moved up, position $INDEX$ of $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "selectCollectionsToAssign": {"message": "Select collections to assign"}, "personalItemTransferWarningSingular": {"message": "1 item will be permanently transferred to the selected organization. You will no longer own this item."}, "personalItemsTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ items will be permanently transferred to the selected organization. You will no longer own these items.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}}}, "personalItemWithOrgTransferWarningSingular": {"message": "1 item will be permanently transferred to $ORG$. You will no longer own this item.", "placeholders": {"org": {"content": "$1", "example": "Organization name"}}}, "personalItemsWithOrgTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ items will be permanently transferred to $ORG$. You will no longer own these items.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}, "org": {"content": "$2", "example": "Organization name"}}}, "successfullyAssignedCollections": {"message": "Successfully assigned collections"}, "nothingSelected": {"message": "You have not selected anything."}, "itemsMovedToOrg": {"message": "Items moved to $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "itemMovedToOrg": {"message": "Item moved to $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "reorderFieldDown": {"message": "$LABEL$ moved down, position $INDEX$ of $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "itemLocation": {"message": "Item Location"}, "fileSend": {"message": "File Send"}, "fileSends": {"message": "File Sends"}, "textSend": {"message": "Text Send"}, "textSends": {"message": "Text Sends"}, "accountActions": {"message": "Account actions"}, "showNumberOfAutofillSuggestions": {"message": "Show number of login autofill suggestions on extension icon"}, "showQuickCopyActions": {"message": "Show quick copy actions on Vault"}, "systemDefault": {"message": "System default"}, "enterprisePolicyRequirementsApplied": {"message": "Enterprise policy requirements have been applied to this setting"}, "sshPrivateKey": {"message": "Private key"}, "sshPublicKey": {"message": "Public key"}, "sshFingerprint": {"message": "Fingerprint"}, "sshKeyAlgorithm": {"message": "Key type"}, "sshKeyAlgorithmED25519": {"message": "ED25519"}, "sshKeyAlgorithmRSA2048": {"message": "RSA 2048-Bit"}, "sshKeyAlgorithmRSA3072": {"message": "RSA 3072-Bit"}, "sshKeyAlgorithmRSA4096": {"message": "RSA 4096-Bit"}, "retry": {"message": "Retry"}, "vaultCustomTimeoutMinimum": {"message": "Minimum custom timeout is 1 minute."}, "additionalContentAvailable": {"message": "Additional content is available"}, "fileSavedToDevice": {"message": "File saved to device. Manage from your device downloads."}, "showCharacterCount": {"message": "Show character count"}, "hideCharacterCount": {"message": "Hide character count"}, "itemsInTrash": {"message": "Items in trash"}, "noItemsInTrash": {"message": "No items in trash"}, "noItemsInTrashDesc": {"message": "Items you delete will appear here and be permanently deleted after 30 days"}, "trashWarning": {"message": "Items that have been in trash more than 30 days will automatically be deleted"}, "restore": {"message": "Rest<PERSON>"}, "deleteForever": {"message": "Delete forever"}, "noEditPermissions": {"message": "You don't have permission to edit this item"}, "biometricsStatusHelptextUnlockNeeded": {"message": "Biometric unlock is unavailable because PIN or password unlock is required first."}, "biometricsStatusHelptextHardwareUnavailable": {"message": "Biometric unlock is currently unavailable."}, "biometricsStatusHelptextAutoSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextManualSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextDesktopDisconnected": {"message": "Biometric unlock is unavailable because the Bitwarden desktop app is closed."}, "biometricsStatusHelptextNotEnabledInDesktop": {"message": "Biometric unlock is unavailable because it is not enabled for $EMAIL$ in the Bitwarden desktop app.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "biometricsStatusHelptextUnavailableReasonUnknown": {"message": "Biometric unlock is currently unavailable for an unknown reason."}, "unlockVault": {"message": "Unlock your vault in seconds"}, "unlockVaultDesc": {"message": "You can customize your unlock and timeout settings to more quickly access your vault."}, "unlockPinSet": {"message": "Unlock PIN set"}, "authenticating": {"message": "Authenticating"}, "fillGeneratedPassword": {"message": "Fill generated password", "description": "Heading for the password generator within the inline menu"}, "passwordRegenerated": {"message": "Password regenerated", "description": "Notification message for when a password has been regenerated"}, "saveToBitwarden": {"message": "Save to Bitwarden", "description": "Confirmation message for saving a login to Bitwarden"}, "spaceCharacterDescriptor": {"message": "Space", "description": "Represents the space key in screen reader content as a readable word"}, "tildeCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ~ key in screen reader content as a readable word"}, "backtickCharacterDescriptor": {"message": "Backtick", "description": "Represents the ` key in screen reader content as a readable word"}, "exclamationCharacterDescriptor": {"message": "Exclamation mark", "description": "Represents the ! key in screen reader content as a readable word"}, "atSignCharacterDescriptor": {"message": "At sign", "description": "Represents the @ key in screen reader content as a readable word"}, "hashSignCharacterDescriptor": {"message": "Hash sign", "description": "Represents the # key in screen reader content as a readable word"}, "dollarSignCharacterDescriptor": {"message": "Dollar sign", "description": "Represents the $ key in screen reader content as a readable word"}, "percentSignCharacterDescriptor": {"message": "Percent sign", "description": "Represents the % key in screen reader content as a readable word"}, "caretCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ^ key in screen reader content as a readable word"}, "ampersandCharacterDescriptor": {"message": "Ampersand", "description": "Represents the & key in screen reader content as a readable word"}, "asteriskCharacterDescriptor": {"message": "Asterisk", "description": "Represents the * key in screen reader content as a readable word"}, "parenLeftCharacterDescriptor": {"message": "Left parenthesis", "description": "Represents the ( key in screen reader content as a readable word"}, "parenRightCharacterDescriptor": {"message": "Right parenthesis", "description": "Represents the ) key in screen reader content as a readable word"}, "hyphenCharacterDescriptor": {"message": "Underscore", "description": "Represents the _ key in screen reader content as a readable word"}, "underscoreCharacterDescriptor": {"message": "Hyphen", "description": "Represents the - key in screen reader content as a readable word"}, "plusCharacterDescriptor": {"message": "Plus", "description": "Represents the + key in screen reader content as a readable word"}, "equalsCharacterDescriptor": {"message": "Equals", "description": "Represents the = key in screen reader content as a readable word"}, "braceLeftCharacterDescriptor": {"message": "Left brace", "description": "Represents the { key in screen reader content as a readable word"}, "braceRightCharacterDescriptor": {"message": "Right brace", "description": "Represents the } key in screen reader content as a readable word"}, "bracketLeftCharacterDescriptor": {"message": "Left bracket", "description": "Represents the [ key in screen reader content as a readable word"}, "bracketRightCharacterDescriptor": {"message": "Right bracket", "description": "Represents the ] key in screen reader content as a readable word"}, "pipeCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the | key in screen reader content as a readable word"}, "backSlashCharacterDescriptor": {"message": "Back slash", "description": "Represents the back slash key in screen reader content as a readable word"}, "colonCharacterDescriptor": {"message": "Colon", "description": "Represents the : key in screen reader content as a readable word"}, "semicolonCharacterDescriptor": {"message": "Semicolon", "description": "Represents the ; key in screen reader content as a readable word"}, "doubleQuoteCharacterDescriptor": {"message": "Double quote", "description": "Represents the double quote key in screen reader content as a readable word"}, "singleQuoteCharacterDescriptor": {"message": "Single quote", "description": "Represents the ' key in screen reader content as a readable word"}, "lessThanCharacterDescriptor": {"message": "Less than", "description": "Represents the < key in screen reader content as a readable word"}, "greaterThanCharacterDescriptor": {"message": "Greater than", "description": "Represents the > key in screen reader content as a readable word"}, "commaCharacterDescriptor": {"message": "Comma", "description": "Represents the , key in screen reader content as a readable word"}, "periodCharacterDescriptor": {"message": "Period", "description": "Represents the . key in screen reader content as a readable word"}, "questionCharacterDescriptor": {"message": "Question mark", "description": "Represents the ? key in screen reader content as a readable word"}, "forwardSlashCharacterDescriptor": {"message": "Forward slash", "description": "Represents the / key in screen reader content as a readable word"}, "lowercaseAriaLabel": {"message": "Lowercase"}, "uppercaseAriaLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "generatedPassword": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "compactMode": {"message": "Modu trinkoa"}, "beta": {"message": "Beta"}, "extensionWidth": {"message": "Extension width"}, "wide": {"message": "Wide"}, "extraWide": {"message": "Extra wide"}, "sshKeyWrongPassword": {"message": "The password you entered is incorrect."}, "importSshKey": {"message": "Import"}, "confirmSshKeyPassword": {"message": "Confirm password"}, "enterSshKeyPasswordDesc": {"message": "Enter the password for the SSH key."}, "enterSshKeyPassword": {"message": "Enter password"}, "invalidSshKey": {"message": "The SSH key is invalid"}, "sshKeyTypeUnsupported": {"message": "The SSH key type is not supported"}, "importSshKeyFromClipboard": {"message": "Import key from clipboard"}, "sshKeyImported": {"message": "SSH key imported successfully"}, "cannotRemoveViewOnlyCollections": {"message": "You cannot remove collections with View only permissions: $COLLECTIONS$", "placeholders": {"collections": {"content": "$1", "example": "Work, Personal"}}}, "updateDesktopAppOrDisableFingerprintDialogTitle": {"message": "Please update your desktop application"}, "updateDesktopAppOrDisableFingerprintDialogMessage": {"message": "To use biometric unlock, please update your desktop application, or disable fingerprint unlock in the desktop settings."}, "changeAtRiskPassword": {"message": "Change at-risk password"}, "settingsVaultOptions": {"message": "Vault options"}, "emptyVaultDescription": {"message": "The vault protects more than just your passwords. Store secure logins, IDs, cards and notes securely here."}, "introCarouselLabel": {"message": "Welcome to Bitwarden"}, "securityPrioritized": {"message": "Security, prioritized"}, "securityPrioritizedBody": {"message": "Save logins, cards, and identities to your secure vault. Bitwarden uses zero-knowledge, end-to-end encryption to protect what’s important to you."}, "quickLogin": {"message": "Quick and easy login"}, "quickLoginBody": {"message": "Set up biometric unlock and autofill to log into your accounts without typing a single letter."}, "secureUser": {"message": "Level up your logins"}, "secureUserBody": {"message": "Use the generator to create and save strong, unique passwords for all your accounts."}, "secureDevices": {"message": "Your data, when and where you need it"}, "secureDevicesBody": {"message": "Save unlimited passwords across unlimited devices with Bitwarden mobile, browser, and desktop apps."}, "nudgeBadgeAria": {"message": "1 notification"}, "emptyVaultNudgeTitle": {"message": "Import existing passwords"}, "emptyVaultNudgeBody": {"message": "Use the importer to quickly transfer logins to Bitwarden without manually adding them."}, "emptyVaultNudgeButton": {"message": "Import now"}, "hasItemsVaultNudgeTitle": {"message": "Welcome to your vault!"}, "hasItemsVaultNudgeBodyOne": {"message": "Autofill items for the current page"}, "hasItemsVaultNudgeBodyTwo": {"message": "Favorite items for easy access"}, "hasItemsVaultNudgeBodyThree": {"message": "Search your vault for something else"}, "newLoginNudgeTitle": {"message": "Save time with autofill"}, "newLoginNudgeBodyOne": {"message": "Include a", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyBold": {"message": "Website", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyTwo": {"message": "so this login appears as an autofill suggestion.", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newCardNudgeTitle": {"message": "Seamless online checkout"}, "newCardNudgeBody": {"message": "With cards, easily autofill payment forms securely and accurately."}, "newIdentityNudgeTitle": {"message": "Simplify creating accounts"}, "newIdentityNudgeBody": {"message": "With identities, quickly autofill long registration or contact forms."}, "newNoteNudgeTitle": {"message": "Keep your sensitive data safe"}, "newNoteNudgeBody": {"message": "With notes, securely store sensitive data like banking or insurance details."}, "newSshNudgeTitle": {"message": "Developer-friendly SSH access"}, "newSshNudgeBodyOne": {"message": "Store your keys and connect with the SSH agent for fast, encrypted authentication.", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "newSshNudgeBodyTwo": {"message": "Learn more about SSH agent", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "generatorNudgeTitle": {"message": "Quickly create passwords"}, "generatorNudgeBodyOne": {"message": "Easily create strong and unique passwords by clicking on", "description": "Two part message", "example": "Easily create strong and unique passwords by clicking on {icon} to help you keep your logins secure."}, "generatorNudgeBodyTwo": {"message": "to help you keep your logins secure.", "description": "Two part message", "example": "Easily create strong and unique passwords by clicking on {icon} to help you keep your logins secure."}, "generatorNudgeBodyAria": {"message": "Easily create strong and unique passwords by clicking on the Generate password button to help you keep your logins secure.", "description": "Aria label for the body content of the generator nudge"}, "noPermissionsViewPage": {"message": "You do not have permissions to view this page. Try logging in with a different account."}}