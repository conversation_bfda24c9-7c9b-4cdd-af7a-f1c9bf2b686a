{"appName": {"message": "Bitwarden"}, "appLogoLabel": {"message": "Bitwarden logotipas"}, "extName": {"message": "„Bitwarden“ slaptažodžių tvarkyklė", "description": "Extension name, MUST be less than 40 characters (Safari restriction)"}, "extDesc": {"message": "<PERSON><PERSON><PERSON>, da<PERSON><PERSON> <PERSON><PERSON>, Bitwarden apsaugo jū<PERSON><PERSON>, r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir svarbią informaciją", "description": "Extension description, MUST be less than 112 characters (Safari restriction)"}, "loginOrCreateNewAccount": {"message": "Prisijunkite arba sukurkite naują paskyrą, kad gal<PERSON>tumėte pasiekti saugyklą."}, "inviteAccepted": {"message": "Kvietimas p<PERSON>imtas"}, "createAccount": {"message": "Sukurti paskyrą"}, "newToBitwarden": {"message": "Pirmą kartą Bitwarden?"}, "logInWithPasskey": {"message": "Prisijungt<PERSON> naudojant prieigos raktą"}, "useSingleSignOn": {"message": "Use single sign-on"}, "welcomeBack": {"message": "Sveiki sugrįžę"}, "setAStrongPassword": {"message": "Nustatyti stiprų slaptažodį"}, "finishCreatingYourAccountBySettingAPassword": {"message": "Baigkite kurti paskyrą nustatydami slaptažodį"}, "enterpriseSingleSignOn": {"message": "Vienkartinis įmonės prisijungimas"}, "cancel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "close": {"message": "Uždaryti"}, "submit": {"message": "<PERSON>š<PERSON>ug<PERSON><PERSON>"}, "emailAddress": {"message": "El. <PERSON>"}, "masterPass": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "masterPassDesc": {"message": "Pagrindinis slapta<PERSON>odis yra slap<PERSON>, kurį naudojate norėdami pasiekti savo saugyklą. Labai svarbu nepamiršti pagrindinio slaptažodžio. Nėra galimybių atkurti slaptažodį, jei jį pamirš<PERSON>."}, "masterPassHintDesc": {"message": "Pagrindinio slaptažodžio užuomina gali padėti Jums prisiminti slaptažodį, jei jį pamiršite."}, "masterPassHintText": {"message": "<PERSON>i p<PERSON> slaptažodį, slap<PERSON>žodžio užuomina gali būti išsi<PERSON> į jūsų el. paštą. $CURRENT$ / $MAXIMUM$ didžiausias simbolių skaičius.", "placeholders": {"current": {"content": "$1", "example": "0"}, "maximum": {"content": "$2", "example": "50"}}}, "reTypeMasterPass": {"message": "Pakartokite pagrindinį slaptažodį"}, "masterPassHint": {"message": "Pagrindinio slaptažodžio užuomina (neprivaloma)"}, "passwordStrengthScore": {"message": "Slaptažodžio stiprumas $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "joinOrganization": {"message": "Prisijungti prie organizacijos"}, "joinOrganizationName": {"message": "Prisijungti prie $ORGANIZATIONNAME$", "placeholders": {"organizationName": {"content": "$1", "example": "My Org Name"}}}, "finishJoiningThisOrganizationBySettingAMasterPassword": {"message": "Baigėte prisijungimą prie organizacijos nustatant pagrindinį slaptažodį."}, "tab": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "vault": {"message": "Saug<PERSON><PERSON>"}, "myVault": {"message": "<PERSON><PERSON>"}, "allVaults": {"message": "<PERSON><PERSON><PERSON>"}, "tools": {"message": "Įrankiai"}, "settings": {"message": "Nustatymai"}, "currentTab": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "copyPassword": {"message": "Kopijuoti slaptažodį"}, "copyPassphrase": {"message": "Kopijuoti slaptažodžio frazę"}, "copyNote": {"message": "Kopijuoti pastabą"}, "copyUri": {"message": "Kopijuoti nuorodą"}, "copyUsername": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> var<PERSON><PERSON> vard<PERSON>"}, "copyNumber": {"message": "Kopijuoti numerį"}, "copySecurityCode": {"message": "Kopijuoti saugos kodą"}, "copyName": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> vard<PERSON>"}, "copyCompany": {"message": "Kopijuoti įmonę"}, "copySSN": {"message": "Kopijuoti socialinės apsaugos numerį"}, "copyPassportNumber": {"message": "Kopijuoti paso numerį"}, "copyLicenseNumber": {"message": "Kopijuoti licenzijos numerį"}, "copyPrivateKey": {"message": "Kopijuoti privatų raktą"}, "copyPublicKey": {"message": "Kopijuoti viešą raktą"}, "copyFingerprint": {"message": "Kopijuoti p<PERSON>š<PERSON>"}, "copyCustomField": {"message": "Kopijuoti $FIELD$", "placeholders": {"field": {"content": "$1", "example": "Custom field label"}}}, "copyWebsite": {"message": "Kopijuoti svetainę"}, "copyNotes": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> pastabas"}, "copy": {"message": "Ko<PERSON><PERSON><PERSON><PERSON>", "description": "Copy to clipboard"}, "fill": {"message": "Fill", "description": "This string is used on the vault page to indicate autofilling. Horizontal space is limited in the interface here so try and keep translations as concise as possible."}, "autoFill": {"message": "Automat<PERSON><PERSON>"}, "autoFillLogin": {"message": "Prisijungimo automatinis <PERSON>"}, "autoFillCard": {"message": "Kortelės automatinis <PERSON>"}, "autoFillIdentity": {"message": "Tapatybės automatinis <PERSON>"}, "fillVerificationCode": {"message": "Fill verification code"}, "fillVerificationCodeAria": {"message": "Fill Verification Code", "description": "Aria label for the heading displayed the inline menu for totp code autofill"}, "generatePasswordCopied": {"message": "Sukurti slaptažodį (nukopijuotas)"}, "copyElementIdentifier": {"message": "Nukopijuoti pasirinktinio la<PERSON> p<PERSON>"}, "noMatchingLogins": {"message": "Nėra atitinkančių prisijungimų"}, "noCards": {"message": "Nėra kortelių"}, "noIdentities": {"message": "Nėra tapatybių"}, "addLoginMenu": {"message": "Pridėti p<PERSON>ijungimą"}, "addCardMenu": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>"}, "addIdentityMenu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unlockVaultMenu": {"message": "Atrakinti Jūsų saugyklą"}, "loginToVaultMenu": {"message": "Prisijungti prie Jūsų <PERSON>yklos"}, "autoFillInfo": {"message": "Nėra galimų prisijungimų prie dabartinio naršyklė<PERSON>."}, "addLogin": {"message": "Pridėti p<PERSON>ijungimą"}, "addItem": {"message": "Pridė<PERSON> elementą"}, "accountEmail": {"message": "Account email"}, "requestHint": {"message": "Request hint"}, "requestPasswordHint": {"message": "Request password hint"}, "enterYourAccountEmailAddressAndYourPasswordHintWillBeSentToYou": {"message": "Enter your account email address and your password hint will be sent to you"}, "getMasterPasswordHint": {"message": "<PERSON><PERSON><PERSON> pagrindinio slaptažodžio užuominą"}, "continue": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sendVerificationCode": {"message": "Siųsti patvir<PERSON> kod<PERSON> į el. paštą"}, "sendCode": {"message": "Siųsti kodą"}, "codeSent": {"message": "<PERSON><PERSON>"}, "verificationCode": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "confirmIdentity": {"message": "<PERSON><PERSON>, pat<PERSON><PERSON><PERSON><PERSON> tapat<PERSON>ę."}, "changeMasterPassword": {"message": "<PERSON><PERSON>i pagrindinį slaptažodį"}, "continueToWebApp": {"message": "Tęsti į žiniatinklio programėlę?"}, "continueToWebAppDesc": {"message": "Atraskite daugiau savo Bitwarden paskyros funkcijų web programoje."}, "continueToHelpCenter": {"message": "Eiti į pagalbos centrą?"}, "continueToHelpCenterDesc": {"message": "Pagalbos Centre sužinokite daugiau kaip naudotis Bitwarden."}, "continueToBrowserExtensionStore": {"message": "Eiti į naršyklės plėtinių svetainę?"}, "continueToBrowserExtensionStoreDesc": {"message": "Padėkite kitiems sužinoti ar Bitwarden yra jiems tinkamas. Apsilankykite naršyklės plėtinių svetainėje ir įvertinkite Bitwarden."}, "changeMasterPasswordOnWebConfirmation": {"message": "Pagrindinį slaptažodį galite pakeisti „Bitwarden“ žiniatinklio programėlėje."}, "fingerprintPhrase": {"message": "Pirštų atspaudų frazė", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "yourAccountsFingerprint": {"message": "Jūsų paskyros pirštų atspaudų frazė", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "twoStepLogin": {"message": "Dviejų žingsnių prisijungimas"}, "logOut": {"message": "<PERSON>si<PERSON><PERSON><PERSON>"}, "aboutBitwarden": {"message": "<PERSON><PERSON> Bitwarden"}, "about": {"message": "<PERSON><PERSON>"}, "moreFromBitwarden": {"message": "Daugiau iš <PERSON>"}, "continueToBitwardenDotCom": {"message": "Eiti į bitwarden.com?"}, "bitwardenForBusiness": {"message": "Bitwarden Verslui"}, "bitwardenAuthenticator": {"message": "Bitwarden Authenticator"}, "continueToAuthenticatorPageDesc": {"message": "„Bitwarden Authenticator“ leidžia saugoti autentifikavimo raktus ir generuoti TOTP kodus dviejų etapų patvirtinimui. Sužinokite daugiau bitwarden.com svetainėje"}, "bitwardenSecretsManager": {"message": "Bitwarden Secrets Manager"}, "continueToSecretsManagerPageDesc": {"message": "<PERSON><PERSON><PERSON><PERSON>, tvarkykite ir dalinkitė<PERSON> kūr<PERSON> slaptais duomenimis su „Bitwarden Secrets Manager“. Sužinokite daugiau bitwarden.com svetainėje."}, "passwordlessDotDev": {"message": "Passwordless.dev"}, "continueToPasswordlessDotDevPageDesc": {"message": "Sukurkite sklandžią ir saugią prisijungimo patirtį be tradicinių slaptažodžių su Passwordless.dev. Sužinokite daugiau bitwarden.com svetainėje."}, "freeBitwardenFamilies": {"message": "Nemokamas „Bitwarden Families“"}, "freeBitwardenFamiliesPageDesc": {"message": "<PERSON><PERSON><PERSON> turite galimybę gauti nemokamą „Bitwarden Families“ paskyrą. Pasinaudokite šiuo pasiūlymu šiandien Interneto svetainėje."}, "version": {"message": "<PERSON><PERSON><PERSON>"}, "save": {"message": "<PERSON>š<PERSON>ug<PERSON><PERSON>"}, "move": {"message": "<PERSON><PERSON><PERSON>"}, "addFolder": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "name": {"message": "Pavadinimas"}, "editFolder": {"message": "Redaguoti aplankalą"}, "editFolderWithName": {"message": "Edit folder: $FOLDERNAME$", "placeholders": {"foldername": {"content": "$1", "example": "Social"}}}, "newFolder": {"message": "New folder"}, "folderName": {"message": "Folder name"}, "folderHintText": {"message": "Nest a folder by adding the parent folder's name followed by a “/”. Example: Social/Forums"}, "noFoldersAdded": {"message": "No folders added"}, "createFoldersToOrganize": {"message": "Create folders to organize your vault items"}, "deleteFolderPermanently": {"message": "Are you sure you want to permanently delete this folder?"}, "deleteFolder": {"message": "<PERSON><PERSON><PERSON>"}, "folders": {"message": "Aplankalai"}, "noFolders": {"message": "Nėra <PERSON>, kuriuos būtų galima išvardyti."}, "helpFeedback": {"message": "Pagalba ir at<PERSON>"}, "helpCenter": {"message": "„Bitwarden“ Pagalbos centras"}, "communityForums": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> „Bitwarden“ bendruomenės forumus"}, "contactSupport": {"message": "Susisiekti su „Bitwarden“ palaikymo komanda"}, "sync": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "syncVaultNow": {"message": "Sinchronizuoti sa<PERSON> dabar"}, "lastSync": {"message": "<PERSON><PERSON><PERSON><PERSON>:"}, "passGen": {"message": "Slaptažodžių <PERSON>ius"}, "generator": {"message": "<PERSON><PERSON><PERSON>", "description": "Short for 'credential generator'."}, "passGenInfo": {"message": "Automatiš<PERSON> generu<PERSON><PERSON> stiprius, unikalius prisi<PERSON><PERSON><PERSON>."}, "bitWebVaultApp": {"message": "Bitwarden Interneto svetainė"}, "importItems": {"message": "Importuoti elementus"}, "select": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "generatePassword": {"message": "Sugeneruoti slaptažodį"}, "generatePassphrase": {"message": "Generate passphrase"}, "passwordGenerated": {"message": "Password generated"}, "passphraseGenerated": {"message": "Passphrase generated"}, "usernameGenerated": {"message": "Username generated"}, "emailGenerated": {"message": "Email generated"}, "regeneratePassword": {"message": "Generuoti slaptažodį iš naujo"}, "options": {"message": "Pasirink<PERSON><PERSON>"}, "length": {"message": "<PERSON><PERSON>"}, "include": {"message": "Include", "description": "Card header for password generator include block"}, "uppercaseDescription": {"message": "Include uppercase characters", "description": "Tooltip for the password generator uppercase character checkbox"}, "uppercaseLabel": {"message": "A-Z", "description": "Label for the password generator uppercase character checkbox"}, "lowercaseDescription": {"message": "Include lowercase characters", "description": "Full description for the password generator lowercase character checkbox"}, "lowercaseLabel": {"message": "a-z", "description": "Label for the password generator lowercase character checkbox"}, "numbersDescription": {"message": "Include numbers", "description": "Full description for the password generator numbers checkbox"}, "numbersLabel": {"message": "0-9", "description": "Label for the password generator numbers checkbox"}, "specialCharactersDescription": {"message": "Include special characters", "description": "Full description for the password generator special characters checkbox"}, "numWords": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>"}, "wordSeparator": {"message": "Žodži<PERSON>"}, "capitalize": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Make the first letter of a work uppercase."}, "includeNumber": {"message": "Įtraukti skai<PERSON>ius"}, "minNumbers": {"message": "Mažiausiai skaičių"}, "minSpecial": {"message": "Mažiausiai simbolių"}, "avoidAmbiguous": {"message": "Avoid ambiguous characters", "description": "Label for the avoid ambiguous characters checkbox."}, "generatorPolicyInEffect": {"message": "Jūsų generatoriaus parinktims taikomi įmonės politikos reikalavimai.", "description": "Indicates that a policy limits the credential generator screen."}, "searchVault": {"message": "Ieškoti saugykloje"}, "edit": {"message": "<PERSON><PERSON><PERSON>"}, "view": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "noItemsInList": {"message": "Nėra rodytinų elementų."}, "itemInformation": {"message": "Elemento informacija"}, "username": {"message": "<PERSON><PERSON><PERSON><PERSON> var<PERSON>"}, "password": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "totp": {"message": "Autentifi<PERSON><PERSON><PERSON> p<PERSON>"}, "passphrase": {"message": "Slaptafraz<PERSON>"}, "favorite": {"message": "Mėgs<PERSON><PERSON>"}, "unfavorite": {"message": "Pašalint<PERSON> iš m<PERSON>ių"}, "itemAddedToFavorites": {"message": "Elementas pridėtas prie mėgstamiausių"}, "itemRemovedFromFavorites": {"message": "Elementas pašalintas iš m<PERSON>gs<PERSON>usių"}, "notes": {"message": "Pastabos"}, "privateNote": {"message": "<PERSON>rivati pastaba"}, "note": {"message": "Pastaba"}, "editItem": {"message": "Redaguoti elementą"}, "folder": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "deleteItem": {"message": "Šalinti elementą"}, "viewItem": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "launch": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "launchWebsite": {"message": "<PERSON>ida<PERSON><PERSON> s<PERSON>"}, "launchWebsiteName": {"message": "Launch website $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret item"}}}, "website": {"message": "<PERSON><PERSON><PERSON>"}, "toggleVisibility": {"message": "<PERSON><PERSON><PERSON><PERSON> matom<PERSON>"}, "manage": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "other": {"message": "<PERSON><PERSON>"}, "unlockMethods": {"message": "<PERSON>rak<PERSON><PERSON> par<PERSON>"}, "unlockMethodNeededToChangeTimeoutActionDesc": {"message": "Nustatyk <PERSON><PERSON> bū<PERSON>, kad pakeistum saugyklos laiko limito ve<PERSON>."}, "unlockMethodNeeded": {"message": "Nustatykite nustatymuose atrakinimo metodą"}, "sessionTimeoutHeader": {"message": "Baigė<PERSON> la<PERSON>s"}, "vaultTimeoutHeader": {"message": "Vault timeout"}, "otherOptions": {"message": "Kit<PERSON> parinktys"}, "rateExtension": {"message": "Įvertinkite šį plėtinį"}, "browserNotSupportClipboard": {"message": "Jūsų žiniatinklio naršyklė nepalaiko automatinio kopijavimo. Vietoj to nukopijuokite rankiniu būdu."}, "verifyYourIdentity": {"message": "Verify your identity"}, "weDontRecognizeThisDevice": {"message": "We don't recognize this device. Enter the code sent to your email to verify your identity."}, "continueLoggingIn": {"message": "Continue logging in"}, "yourVaultIsLocked": {"message": "<PERSON><PERSON>sų saugykla užrakinta. Nor<PERSON><PERSON><PERSON> t<PERSON>, patikrinkite pagrindinį slaptažodį."}, "yourVaultIsLockedV2": {"message": "Your vault is locked"}, "yourAccountIsLocked": {"message": "Your account is locked"}, "or": {"message": "or"}, "unlock": {"message": "Atrakinti"}, "loggedInAsOn": {"message": "Prisijungta prie $HOSTNAME$ kaip $EMAIL$.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}, "hostname": {"content": "$2", "example": "bitwarden.com"}}}, "invalidMasterPassword": {"message": "Neteisingas pagrin<PERSON>"}, "vaultTimeout": {"message": "Atsijungta nuo sa<PERSON>"}, "vaultTimeout1": {"message": "Timeout"}, "lockNow": {"message": "Užrakinti dabar"}, "lockAll": {"message": "Užrakinti viską"}, "immediately": {"message": "<PERSON><PERSON><PERSON>"}, "tenSeconds": {"message": "10 sekundžių"}, "twentySeconds": {"message": "20 sekundžių"}, "thirtySeconds": {"message": "30 sekundžių"}, "oneMinute": {"message": "1 minutės"}, "twoMinutes": {"message": "2 minučių"}, "fiveMinutes": {"message": "5 minučių"}, "fifteenMinutes": {"message": "15 minučių"}, "thirtyMinutes": {"message": "30 minučių"}, "oneHour": {"message": "1 valandos"}, "fourHours": {"message": "4 valandų"}, "onLocked": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON>"}, "onRestart": {"message": "<PERSON><PERSON><PERSON><PERSON> iš naujo na<PERSON>"}, "never": {"message": "<PERSON><PERSON><PERSON>"}, "security": {"message": "Apsauga"}, "confirmMasterPassword": {"message": "Patvirtinkite pagrindinį slaptažodį"}, "masterPassword": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "masterPassImportant": {"message": "Pagrindinio <PERSON>žodž<PERSON> negal<PERSON> atkurti, jei jį pami<PERSON>."}, "masterPassHintLabel": {"message": "Pagrindinio slaptažodžio užuomina"}, "errorOccurred": {"message": "Įvyko klaida"}, "emailRequired": {"message": "Reikalingas el. pašto adres<PERSON>."}, "invalidEmail": {"message": "Klaid<PERSON>s el. pa<PERSON> ad<PERSON>."}, "masterPasswordRequired": {"message": "<PERSON><PERSON><PERSON><PERSON> pag<PERSON>."}, "confirmMasterPasswordRequired": {"message": "<PERSON><PERSON><PERSON>s prisijungi<PERSON>."}, "masterPasswordMinlength": {"message": "Pagrindinis slaptažodis turi b<PERSON> bent $VALUE$ simbolių ilgio.", "description": "The Master Password must be at least a specific number of characters long.", "placeholders": {"value": {"content": "$1", "example": "8"}}}, "masterPassDoesntMatch": {"message": "Pagrindinio slaptažodžio patvirtinimas nesutampa."}, "newAccountCreated": {"message": "Jūsų paskyra sukurta! Galite prisijungti."}, "newAccountCreated2": {"message": "Your new account has been created!"}, "youHaveBeenLoggedIn": {"message": "You have been logged in!"}, "youSuccessfullyLoggedIn": {"message": "<PERSON><PERSON><PERSON>"}, "youMayCloseThisWindow": {"message": "G<PERSON><PERSON> uždaryti šį langą"}, "masterPassSent": {"message": "Išsiuntėme jums el. laišką su pagrindinio slaptažodžio užuomina."}, "verificationCodeRequired": {"message": "<PERSON><PERSON><PERSON><PERSON> pat<PERSON><PERSON> k<PERSON>."}, "webauthnCancelOrTimeout": {"message": "Tapatybės nustatymas buvo atšauktas arba užtruko per ilgai. Bandykite dar kartą."}, "invalidVerificationCode": {"message": "Neteisingas pat<PERSON><PERSON> k<PERSON>"}, "valueCopied": {"message": "Nukopijuota $VALUE$", "description": "Value has been copied to the clipboard.", "placeholders": {"value": {"content": "$1", "example": "Password"}}}, "autofillError": {"message": "Nepavyko automatiškai užpildyti pasirinkto elemento šiame puslapyje. Nukopijuokite ir įklijuokite informaciją."}, "totpCaptureError": {"message": "Nepavyko nuskaityti QR kodo iš dabartinio tinklalapio"}, "totpCaptureSuccess": {"message": "Pridėtas autentifikatoriaus raktas"}, "totpCapture": {"message": "Nuskaitykite autentifikatoriaus QR kodą iš dabartinio tinklalapio"}, "totpHelperTitle": {"message": "Make 2-step verification seamless"}, "totpHelper": {"message": "Bitwarden can store and fill 2-step verification codes. Copy and paste the key into this field."}, "totpHelperWithCapture": {"message": "Bitwarden can store and fill 2-step verification codes. Select the camera icon to take a screenshot of this website's authenticator QR code, or copy and paste the key into this field."}, "learnMoreAboutAuthenticators": {"message": "Learn more about authenticators"}, "copyTOTP": {"message": "Kopiju<PERSON><PERSON> Autentifikatoriaus r<PERSON>ą (TOTP)"}, "loggedOut": {"message": "Atsijungt<PERSON>"}, "loggedOutDesc": {"message": "<PERSON><PERSON><PERSON>."}, "loginExpired": {"message": "<PERSON><PERSON><PERSON>."}, "logIn": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "logInToBitwarden": {"message": "Log in to Bitwarden"}, "enterTheCodeSentToYourEmail": {"message": "Enter the code sent to your email"}, "enterTheCodeFromYourAuthenticatorApp": {"message": "Enter the code from your authenticator app"}, "pressYourYubiKeyToAuthenticate": {"message": "Press your YubiKey to authenticate"}, "duoTwoFactorRequiredPageSubtitle": {"message": "Duo two-step login is required for your account. Follow the steps below to finish logging in."}, "followTheStepsBelowToFinishLoggingIn": {"message": "Follow the steps below to finish logging in."}, "followTheStepsBelowToFinishLoggingInWithSecurityKey": {"message": "Follow the steps below to finish logging in with your security key."}, "restartRegistration": {"message": "Restart registration"}, "expiredLink": {"message": "Nebegali<PERSON><PERSON> nuoroda"}, "pleaseRestartRegistrationOrTryLoggingIn": {"message": "Please restart registration or try logging in."}, "youMayAlreadyHaveAnAccount": {"message": "You may already have an account"}, "logOutConfirmation": {"message": "Ar tikrai norite atsijungti?"}, "yes": {"message": "<PERSON><PERSON>"}, "no": {"message": "Ne"}, "location": {"message": "Location"}, "unexpectedError": {"message": "Įvyko net<PERSON><PERSON><PERSON> k<PERSON>."}, "nameRequired": {"message": "Pavadinimas yra b<PERSON>."}, "addedFolder": {"message": "Katalogas pridėtas"}, "twoStepLoginConfirmation": {"message": "Prisijun<PERSON> dvie<PERSON>, <PERSON><PERSON><PERSON><PERSON> paskyra tampa saugesnė, re<PERSON><PERSON><PERSON><PERSON> patvir<PERSON>ti prisijungimą naudojant kitą įrenginį, pvz., saugos raktą, autentifikavimo programėlę, SMS, telefono skambutį ar el. paštą. Dviejų žingsnių prisijungimą galima įjungti „bitwarden.com“ interneto saugykloje. Ar norite dabar apsilankyti svetainėje?"}, "twoStepLoginConfirmationContent": {"message": "Make your account more secure by setting up two-step login in the Bitwarden web app."}, "twoStepLoginConfirmationTitle": {"message": "Continue to web app?"}, "editedFolder": {"message": "Katalogas atnaujintas"}, "deleteFolderConfirmation": {"message": "Ar tikrai norite ištrinti šį aplanką?"}, "deletedFolder": {"message": "Katalogas ištrintas"}, "gettingStartedTutorial": {"message": "Apmokymai"}, "gettingStartedTutorialVideo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka<PERSON> <PERSON>, ka<PERSON> visa<PERSON> naršyklės plėtinio galim<PERSON>bes."}, "syncingComplete": {"message": "Sinchronizacija baigta"}, "syncingFailed": {"message": "Sinchronizuo<PERSON>"}, "passwordCopied": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "uri": {"message": "URI"}, "uriPosition": {"message": "URI adresas $POSITION$", "description": "A listing of URIs. Ex: URI 1, URI 2, URI 3, etc.", "placeholders": {"position": {"content": "$1", "example": "2"}}}, "newUri": {"message": "<PERSON><PERSON>jas URI"}, "addDomain": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "'Domain' here refers to an internet domain name (e.g. 'bitwarden.com') and the message in whole described the act of putting a domain value into the context."}, "addedItem": {"message": "Pridėtas elementas"}, "editedItem": {"message": "Redaguotas elementas"}, "deleteItemConfirmation": {"message": "Ar tikrai norite perkelti į šiukšlinę?"}, "deletedItem": {"message": "Elementas perkeltas į šiukšlinę"}, "overwritePassword": {"message": "Perrašyti slaptažodį"}, "overwritePasswordConfirmation": {"message": "Ar tikrai norite perrašyti dabartinį slaptažodį?"}, "overwriteUsername": {"message": "Perrašyti Vartotojo Vardą"}, "overwriteUsernameConfirmation": {"message": "Ar tikrai norite perrašyti dabartinį vartotojo vardą?"}, "searchFolder": {"message": "Ieškoti aplanke"}, "searchCollection": {"message": "Ieškoti rinkinio"}, "searchType": {"message": "Ieškoti tipo"}, "noneFolder": {"message": "<PERSON> a<PERSON><PERSON>o", "description": "This is the folder for uncategorized items"}, "enableAddLoginNotification": {"message": "Prašyti pridėti prisijungimą"}, "vaultSaveOptionsTitle": {"message": "Save to vault options"}, "addLoginNotificationDesc": {"message": "Prisijungimo pridėjimo pranešimas automatiškai Jūs paragina išsaugoti naujus prisijungimus J<PERSON>ykloje, kuomet prisijungiate pirmą kartą."}, "addLoginNotificationDescAlt": {"message": "Paprašykite pridėti elementą, jei jo ne<PERSON>ta <PERSON>ykloje. Taikoma visoms prisijungusioms paskyroms."}, "showCardsInVaultViewV2": {"message": "Visada rodyti korteles kaip automatinio pildymo pasi<PERSON><PERSON><PERSON> rod<PERSON>"}, "showCardsCurrentTab": {"message": "<PERSON><PERSON><PERSON> k<PERSON>"}, "showCardsCurrentTabDesc": {"message": "Pateikti kortelių elementų skirtuko puslapyje sąrašą, kad būt<PERSON> lengva automatiškai užpildyti."}, "showIdentitiesInVaultViewV2": {"message": "Visada rodyti tapatybes kaip automatinio pildymo pasi<PERSON><PERSON><PERSON> sa<PERSON> rod<PERSON>"}, "showIdentitiesCurrentTab": {"message": "<PERSON><PERSON><PERSON> tapatybes <PERSON>"}, "showIdentitiesCurrentTabDesc": {"message": "Pateikti tapatybės elementų skirtuko pu<PERSON>je, kad būtų lengva automatiškai užpildyti."}, "clickToAutofillOnVault": {"message": "Click items to autofill on Vault view"}, "clickToAutofill": {"message": "Click items in autofill suggestion to fill"}, "clearClipboard": {"message": "Išvalyti iškarpinę", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "clearClipboardDesc": {"message": "Automatiškai išvalyti nukopijuotas reikšmes iškarpinėje.", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "notificationAddDesc": {"message": "A<PERSON> <PERSON>Bitwarden“ turėtų įsiminti šį slaptažodį už <PERSON>?"}, "notificationAddSave": {"message": "<PERSON>š<PERSON>ug<PERSON><PERSON>"}, "notificationViewAria": {"message": "View $ITEMNAME$, opens in new window", "placeholders": {"itemName": {"content": "$1"}}, "description": "Aria label for the view button in notification bar confirmation message"}, "notificationNewItemAria": {"message": "New Item, opens in new window", "description": "Aria label for the new item button in notification bar confirmation message when error is prompted"}, "notificationEditTooltip": {"message": "Edit before saving", "description": "Tooltip and Aria label for edit button on cipher item"}, "newNotification": {"message": "New notification"}, "labelWithNotification": {"message": "$LABEL$: New notification", "description": "Label for the notification with a new login suggestion.", "placeholders": {"label": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "notificationLoginSaveConfirmation": {"message": "saved to Bitwarden.", "description": "Shown to user after item is saved."}, "notificationLoginUpdatedConfirmation": {"message": "updated in Bitwarden.", "description": "Shown to user after item is updated."}, "selectItemAriaLabel": {"message": "Select $ITEMTYPE$, $ITEMNAME$", "description": "Used by screen readers. $1 is the item type (like vault or folder), $2 is the selected item name.", "placeholders": {"itemType": {"content": "$1"}, "itemName": {"content": "$2"}}}, "saveAsNewLoginAction": {"message": "Save as new login", "description": "Button text for saving login details as a new entry."}, "updateLoginAction": {"message": "Update login", "description": "Button text for updating an existing login entry."}, "unlockToSave": {"message": "Unlock to save this login", "description": "User prompt to take action in order to save the login they just entered."}, "saveLogin": {"message": "Save login", "description": "Prompt asking the user if they want to save their login details."}, "updateLogin": {"message": "Update existing login", "description": "Prompt asking the user if they want to update an existing login entry."}, "loginSaveSuccess": {"message": "<PERSON><PERSON> saved", "description": "Message displayed when login details are successfully saved."}, "loginUpdateSuccess": {"message": "Login updated", "description": "Message displayed when login details are successfully updated."}, "loginUpdateTaskSuccess": {"message": "Great job! You took the steps to make you and $ORGANIZATION$ more secure.", "placeholders": {"organization": {"content": "$1"}}, "description": "Shown to user after login is updated."}, "loginUpdateTaskSuccessAdditional": {"message": "Thank you for making $ORGANIZATION$ more secure. You have $TASK_COUNT$ more passwords to update.", "placeholders": {"organization": {"content": "$1"}, "task_count": {"content": "$2"}}, "description": "Shown to user after login is updated."}, "nextSecurityTaskAction": {"message": "Change next password", "description": "Message prompting user to undertake completion of another security task."}, "saveFailure": {"message": "Error saving", "description": "Error message shown when the system fails to save login details."}, "saveFailureDetails": {"message": "Oh no! We couldn't save this. Try entering the details manually.", "description": "Detailed error message shown when saving login details fails."}, "enableChangedPasswordNotification": {"message": "Paprašyti atnaujinti esamą prisijungimą"}, "changedPasswordNotificationDesc": {"message": "Pa<PERSON><PERSON><PERSON><PERSON> atnaujinti prisijungimo slaptažodį, kai pakeitimas aptiktas svetainė<PERSON>."}, "changedPasswordNotificationDescAlt": {"message": "Paprašykite atnaujinti prisijungimo slaptažodį, kai s<PERSON><PERSON><PERSON><PERSON> aptink<PERSON> pakeitimas. Taikoma visoms prisijungusioms paskyroms."}, "enableUsePasskeys": {"message": "Paprašykite išsaugoti ir naudoti prieigos raktus"}, "usePasskeysDesc": {"message": "Paprašykite išsaugoti naujus prieigos raktus arba prisijungti naudodant saugykloje saugomus prieigos raktus. Taikoma visoms prisijungusioms paskyroms."}, "notificationChangeDesc": {"message": "Ar nor<PERSON> šį slaptažodį „Bitwarden“ programėlėje?"}, "notificationChangeSave": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "notificationUnlockDesc": {"message": "Atrakinkite savo „Bitwarden“ saugyklą, kad užbaigtumėte automatinio užpildymo užklausą."}, "notificationUnlock": {"message": "Atrakinti"}, "additionalOptions": {"message": "Papildo<PERSON> parink<PERSON>"}, "enableContextMenuItem": {"message": "<PERSON><PERSON><PERSON> kontekstinio men<PERSON> p<PERSON>"}, "contextMenuItemDesc": {"message": "Naudokite antrinį paspaudimą, kad patekti į svetain<PERSON>s slap<PERSON>ž<PERSON> generavimo ir prisijungimo atitikimo parinktis. "}, "contextMenuItemDescAlt": {"message": "Naudokite antrinį paspaudimą, kad pasiektumėte slaptažodžio generavimą ir atitinkančius svetainės prisijungimus. Taikoma visoms prisijungusioms paskyroms."}, "defaultUriMatchDetection": {"message": "Numatytojo URI atitikimo aptikimas", "description": "Default URI match detection for autofill."}, "defaultUriMatchDetectionDesc": {"message": "Pasirinkite standartinį būdą, kuriuo URI būtų aptinkamas prisijungiant, kuomet yra naudojamas automatinio užpildymo ve<PERSON>."}, "theme": {"message": "<PERSON><PERSON>"}, "themeDesc": {"message": "Pakeisti programos spalvos temą"}, "themeDescAlt": {"message": "Pakeiskite programėlės spalvų temą. Taikoma visoms prisijungusioms paskyroms."}, "dark": {"message": "<PERSON><PERSON>", "description": "Dark color"}, "light": {"message": "Š<PERSON><PERSON>", "description": "Light color"}, "exportFrom": {"message": "Export from"}, "exportVault": {"message": "Eksportuoti saugyklą"}, "fileFormat": {"message": "Failo formatas"}, "fileEncryptedExportWarningDesc": {"message": "This file export will be password protected and require the file password to decrypt."}, "filePassword": {"message": "File password"}, "exportPasswordDescription": {"message": "This password will be used to export and import this file"}, "accountRestrictedOptionDescription": {"message": "Use your account encryption key, derived from your account's username and Master Password, to encrypt the export and restrict import to only the current Bitwarden account."}, "passwordProtectedOptionDescription": {"message": "Set a file password to encrypt the export and import it to any Bitwarden account using the password for decryption."}, "exportTypeHeading": {"message": "Export type"}, "accountRestricted": {"message": "Account restricted"}, "filePasswordAndConfirmFilePasswordDoNotMatch": {"message": "“File password”  and “Confirm file password“ do not match."}, "warning": {"message": "ĮSPĖJIMAS", "description": "WARNING (should stay in capitalized letters if the language permits)"}, "warningCapitalized": {"message": "Warning", "description": "Warning (should maintain locale-relevant capitalization)"}, "confirmVaultExport": {"message": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>portą"}, "exportWarningDesc": {"message": "Šiame duomenų exporte jūsų saugyklos duomenys yra neužšifruoti. Jūs neturėtumete laikyti ar siųsti išeksportuotos duomenų bylos nesaugiu komunikaciniu kanalu (tokiu kaip el. paš<PERSON>). Ištrinkite jį kaip galima greičiau po to kai pasinaudojote."}, "encExportKeyWarningDesc": {"message": "Šis duomenų exportavimas užšifruoja jūsų duomenis naudodamas jūsų prieigos kodų raktu. Jei jūs kada nuspręsite pakeisti prieigos kodų raktą, jūs tur<PERSON>tum<PERSON>te per naują eksportuoti duomenis, nes kit<PERSON><PERSON>, jūs negal<PERSON>site iššifruoti išeksportuotų duomenų."}, "encExportAccountWarningDesc": {"message": "Paskyros šifravimo raktai yra unikalūs kiekvienai „Bitwarden“ vartotojo paskyrai, todėl negalite importuoti užšifruoto eksportavimo į kitą paskyrą."}, "exportMasterPassword": {"message": "Įveskite pagrindinį slaptažodį norint i<PERSON> sa<PERSON> duomenis."}, "shared": {"message": "Pasidalint<PERSON>"}, "bitwardenForBusinessPageDesc": {"message": "Bitwarden for Business allows you to share your vault items with others by using an organization. Learn more on the bitwarden.com website."}, "moveToOrganization": {"message": "Perkelti į organizaciją"}, "movedItemToOrg": {"message": "$ITEMNAME$ perkelta(s) į $ORGNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "orgname": {"content": "$2", "example": "Company Name"}}}, "moveToOrgDesc": {"message": "Pasirinkite organizacija, į kurią norite priskirti šį elementą. Priskiriant elementą organizacijai, visos elemento valdymo teisės bus perleistos organizacijai. Jūs daugiau nebebūsite tiesioginis elemento valdytojas po to kai jis bus priskirtas organizacijai."}, "learnMore": {"message": "Suž<PERSON>ti daugiau"}, "authenticatorKeyTotp": {"message": "Vienkartinio autentifikavimo rak<PERSON> (TOTP)"}, "verificationCodeTotp": {"message": "<PERSON><PERSON><PERSON><PERSON> (TOTP)"}, "copyVerificationCode": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> pat<PERSON><PERSON><PERSON> k<PERSON>"}, "attachments": {"message": "Pried<PERSON>"}, "deleteAttachment": {"message": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>"}, "deleteAttachmentConfirmation": {"message": "Ar esate tikri, kad norite i<PERSON> šį priedą?"}, "deletedAttachment": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>"}, "newAttachment": {"message": "Pridėti nauj<PERSON> priedą"}, "noAttachments": {"message": "<PERSON><PERSON><PERSON> nėra."}, "attachmentSaved": {"message": "<PERSON><PERSON><PERSON>"}, "file": {"message": "<PERSON><PERSON><PERSON>"}, "fileToShare": {"message": "<PERSON><PERSON><PERSON>, kuri <PERSON>ti"}, "selectFile": {"message": "Pasirinkite failą."}, "maxFileSize": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> failo dyd<PERSON> – 500 MB."}, "featureUnavailable": {"message": "Funkcija neprieinama"}, "legacyEncryptionUnsupported": {"message": "Legacy encryption is no longer supported. Please contact support to recover your account."}, "premiumMembership": {"message": "Premium narystė"}, "premiumManage": {"message": "Tvarkyti na<PERSON>stę"}, "premiumManageAlert": {"message": "Savo narystę galite tvarkyti „bitwarden.com“ žiniatinklio saugykloje. Ar norite apsilankyti svetainė<PERSON> dabar?"}, "premiumRefresh": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> na<PERSON>"}, "premiumNotCurrentMember": {"message": "Neturite Premium narystės."}, "premiumSignUpAndGet": {"message": "Prisijunk prie Premium narystės ir gauk:"}, "ppremiumSignUpStorage": {"message": "1 GB užšifruotos vietos diske bylų prisegimams."}, "premiumSignUpEmergency": {"message": "Emergency access."}, "premiumSignUpTwoStepOptions": {"message": "Patentuotos dviejų žingsnių prisijungimo parinktys, to<PERSON><PERSON> ka<PERSON>."}, "ppremiumSignUpReports": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, prie<PERSON>s sveikata ir duomenų nutekinimo ataskaitos, kad tavo saugyklas būtų saugus."}, "ppremiumSignUpTotp": {"message": "TOTP patvir<PERSON><PERSON> (2FA) generatorius prisijungimams prie tavo saugyklos."}, "ppremiumSignUpSupport": {"message": "Prioritetinis klientų aptarnavimas."}, "ppremiumSignUpFuture": {"message": "Visos būsimos Premium savybės. Daugiau jau greitai!"}, "premiumPurchase": {"message": "Įsigyti Premium"}, "premiumPurchaseAlertV2": {"message": "You can purchase Premium from your account settings on the Bitwarden web app."}, "premiumCurrentMember": {"message": "Tu esi Premium narys!"}, "premiumCurrentMemberThanks": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kad palaikote „Bitwarden“."}, "premiumFeatures": {"message": "Upgrade to Premium and receive:"}, "premiumPrice": {"message": "Visa tai tik už $PRICE$ / metus!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "premiumPriceV2": {"message": "All for just $PRICE$ per year!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "refreshComplete": {"message": "Atnaujinimas įvykdytas"}, "enableAutoTotpCopy": {"message": "Kopijuoti vienkartinį kodą (TOTP) automatiškai"}, "disableAutoTotpCopyDesc": {"message": "Jei prisijungimas turi autentifikatoriaus raktą, nukopijuokite TOTP tikrinimo kodą į iš<PERSON>pinę, kai automatiškai užpildysite prisijungimą."}, "enableAutoBiometricsPrompt": {"message": "Paleidžiant patvirtinti biometrinius duomenis"}, "premiumRequired": {"message": "Premium reikalinga"}, "premiumRequiredDesc": {"message": "Premium narystė reikalinga šiai funkcijai naudoti."}, "authenticationTimeout": {"message": "Authentication timeout"}, "authenticationSessionTimedOut": {"message": "The authentication session timed out. Please restart the login process."}, "verificationCodeEmailSent": {"message": "Patvirtinimo elektroninis paštas išsiųstas į $EMAIL$.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "dontAskAgainOnThisDeviceFor30Days": {"message": "Don't ask again on this device for 30 days"}, "selectAnotherMethod": {"message": "Select another method", "description": "Select another two-step login method"}, "useYourRecoveryCode": {"message": "Use your recovery code"}, "insertU2f": {"message": "Įkišk savo saugos raktą į kompiuterio USB prievadą. Jei jame yra mygt<PERSON>, paliesk jį."}, "openInNewTab": {"message": "Open in new tab"}, "webAuthnAuthenticate": {"message": "Autentifikuoti WebAuthn"}, "readSecurityKey": {"message": "Read security key"}, "awaitingSecurityKeyInteraction": {"message": "Awaiting security key interaction..."}, "loginUnavailable": {"message": "<PERSON>risi<PERSON><PERSON><PERSON>"}, "noTwoStepProviders": {"message": "Šioje paskyroje nustatytas dviejų žingsnių prisijungimas, tačiau, nė vienas iš sukonfigūruotų dviejų žingsnių paslaugų teikėjų nėra palaikomas šioje interneto naršyklėje."}, "noTwoStepProviders2": {"message": "Prašome naudoti palaikomą interneto naršyklę (pvz., Chrome) ir/arba pridėti papildomus paslaugų teikėju<PERSON>, kurie geriau palaikomi įvairiose interneto naršyklėse (pvz., autentifikavimo programėlę)."}, "twoStepOptions": {"message": "Dviejų žingsnių prisijungimo parinktys"}, "selectTwoStepLoginMethod": {"message": "Select two-step login method"}, "recoveryCodeDesc": {"message": "Praradai prieigą prie visų savo dviejų veiksnių teikėjų? Naudok atkūrimo kodą, kad iš savo paskyros išjungtum visus dviejų veiksnių teikėjus."}, "recoveryCodeTitle": {"message": "Atkūrimo kodas"}, "authenticatorAppTitle": {"message": "Autentifikavimo programa"}, "authenticatorAppDescV2": {"message": "Įveskite autentifikatoriaus programėlės sugeneruotą kodą, pvz., „Bitwarden Authenticator“.", "description": "'Bitwarden Authenticator' is a product name and should not be translated."}, "yubiKeyTitleV2": {"message": "„Yubico“ OTP saugumo raktas"}, "yubiKeyDesc": {"message": "<PERSON><PERSON><PERSON>, kad prisijungtum prie savo paskyros. Veikia su YubiKey 4, 4 <PERSON><PERSON>, 4C ir NEO įrenginiais."}, "duoDescV2": {"message": "Įveskite „Duo Security“ sugeneruotą kodą.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "duoOrganizationDesc": {"message": "Patikrink su Duo Security savo organizacijai naudodamasis Duo Mobile programą, SMS žinutę, telefono skambutį arba U2F saugumo raktą.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "webAuthnTitle": {"message": "FIDO2 WebAuthn"}, "webAuthnDesc": {"message": "Naudok bet kurį WebAuthn palaikantį saugumo rakt<PERSON>, kad gal<PERSON>tum naudotis savo paskyra."}, "emailTitle": {"message": "El. <PERSON>"}, "emailDescV2": {"message": "Įveskite į el. paštą atsiųstą kodą."}, "selfHostedEnvironment": {"message": "Savarankiškai sukurta aplinka"}, "selfHostedBaseUrlHint": {"message": "Specify the base URL of your on-premises hosted Bitwarden installation. Example: https://bitwarden.company.com"}, "selfHostedCustomEnvHeader": {"message": "For advanced configuration, you can specify the base URL of each service independently."}, "selfHostedEnvFormInvalid": {"message": "You must add either the base Server URL or at least one custom environment."}, "customEnvironment": {"message": "Individualizuota aplinka"}, "baseUrl": {"message": "Serverio URL"}, "selfHostBaseUrl": {"message": "Self-host server URL", "description": "Label for field requesting a self-hosted integration service URL"}, "apiUrl": {"message": "API serverio nuoroda"}, "webVaultUrl": {"message": "Internetinės saugyklos serverio URL"}, "identityUrl": {"message": "Identifikavimo serverio URL"}, "notificationsUrl": {"message": "Notifikacijų serverio URL"}, "iconsUrl": {"message": "Piktogramų serverio URL"}, "environmentSaved": {"message": "Aplinkos URL adresai išsaugoti"}, "showAutoFillMenuOnFormFields": {"message": "Rodyti automatinio pildymo meniu formos laukeliuose", "description": "Represents the message for allowing the user to enable the autofill overlay"}, "autofillSuggestionsSectionTitle": {"message": "Autofill suggestions"}, "autofillSpotlightTitle": {"message": "Easily find autofill suggestions"}, "autofillSpotlightDesc": {"message": "Turn off your browser's autofill settings, so they don't conflict with Bitwarden."}, "turnOffBrowserAutofill": {"message": "Turn off $BROWSER$ autofill", "placeholders": {"browser": {"content": "$1", "example": "Chrome"}}}, "turnOffAutofill": {"message": "Turn off autofill"}, "showInlineMenuLabel": {"message": "Show autofill suggestions on form fields"}, "showInlineMenuIdentitiesLabel": {"message": "Display identities as suggestions"}, "showInlineMenuCardsLabel": {"message": "Display cards as suggestions"}, "showInlineMenuOnIconSelectionLabel": {"message": "Display suggestions when icon is selected"}, "showInlineMenuOnFormFieldsDescAlt": {"message": "Applies to all logged in accounts."}, "turnOffBrowserBuiltInPasswordManagerSettings": {"message": "Išjunkite naršyklėje integruotus slaptažodžių tvarkyklės nustatymus, kad išvengtumėte konfliktų."}, "turnOffBrowserBuiltInPasswordManagerSettingsLink": {"message": "Redaguoti naršyklės nustatymus."}, "autofillOverlayVisibilityOff": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Overlay setting select option for disabling autofill overlay"}, "autofillOverlayVisibilityOnFieldFocus": {"message": "<PERSON> p<PERSON> la<PERSON> (fokusuotas)", "description": "Overlay appearance select option for showing the field on focus of the input element"}, "autofillOverlayVisibilityOnButtonClick": {"message": "Kai pasirinkta automatinio užpildymo piktograma", "description": "Overlay appearance select option for showing the field on click of the overlay icon"}, "enableAutoFillOnPageLoadSectionTitle": {"message": "Autofill on page load"}, "enableAutoFillOnPageLoad": {"message": "Automatiškai užpildyti užsikrovus puslapiui"}, "enableAutoFillOnPageLoadDesc": {"message": "<PERSON>i aptikta pris<PERSON> forma, automatiš<PERSON><PERSON>, kai kra<PERSON>."}, "experimentalFeature": {"message": "Pažeistos arba nepatikimos svetainės gali išnaudoti automatinį užpildymą įkeliant puslapį."}, "learnMoreAboutAutofillOnPageLoadLinkText": {"message": "Learn more about risks"}, "learnMoreAboutAutofill": {"message": "Sužinokite daugiau apie automatinį užpildymą"}, "defaultAutoFillOnPageLoad": {"message": "Numatytasis automatinio užpildymo nustatymas prisijungimo elementams"}, "defaultAutoFillOnPageLoadDesc": {"message": "G<PERSON>te išjungti automatinį pildymą įkeliant puslapį atskiriems prisijungimo elementams, elemento redagavimo rodinyje."}, "itemAutoFillOnPageLoad": {"message": "Automatinis pildymas įkeliant puslapį (jei nustatyta Parinktyse)"}, "autoFillOnPageLoadUseDefault": {"message": "<PERSON><PERSON><PERSON> numa<PERSON><PERSON><PERSON><PERSON> nustat<PERSON>us"}, "autoFillOnPageLoadYes": {"message": "Automatinis <PERSON> įkeliant puslapį"}, "autoFillOnPageLoadNo": {"message": "Nepildyti automatiškai įkeliant puslapį"}, "commandOpenPopup": {"message": "Atidaryti sa<PERSON>yk<PERSON>ą naujame lange"}, "commandOpenSidebar": {"message": "Atidaryti saugyklą šoninėje juostoje"}, "commandAutofillLoginDesc": {"message": "Autofill the last used login for the current website"}, "commandAutofillCardDesc": {"message": "Autofill the last used card for the current website"}, "commandAutofillIdentityDesc": {"message": "Autofill the last used identity for the current website"}, "commandGeneratePasswordDesc": {"message": "Sugeneruokite ir nukopijuokite naują atsitiktinį slaptažodį į iškarpinę"}, "commandLockVaultDesc": {"message": "Užrakinti saugyklą"}, "customFields": {"message": "Pasirinktiniai laukai"}, "copyValue": {"message": "Kopijuoti vertę"}, "value": {"message": "Reikš<PERSON><PERSON>"}, "newCustomField": {"message": "<PERSON><PERSON><PERSON> pasirinktis lauke<PERSON>"}, "dragToSort": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kad surū<PERSON>um"}, "dragToReorder": {"message": "Drag to reorder"}, "cfTypeText": {"message": "Tekstas"}, "cfTypeHidden": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "cfTypeBoolean": {"message": "Taip/Ne"}, "cfTypeCheckbox": {"message": "<PERSON><PERSON><PERSON><PERSON> langelis"}, "cfTypeLinked": {"message": "<PERSON><PERSON>", "description": "This describes a field that is 'linked' (tied) to another field."}, "linkedValue": {"message": "Susijusi <PERSON>", "description": "This describes a value that is 'linked' (tied) to another value."}, "popup2faCloseMessage": {"message": "<PERSON><PERSON><PERSON><PERSON> už <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ngo, kad pat<PERSON>, ar el. pa<PERSON><PERSON> gausi pat<PERSON><PERSON> kod<PERSON>, uždarys šį iššokantį langą. Ar nori atidaryti šį iššokantį langą naujame lange, kad jis neuž<PERSON>dar<PERSON>?"}, "popupU2fCloseMessage": {"message": "Ši naršyklė negali apdoroti U2F prašymų šiame iššokančiame lange. Ar nori atidaryti šį iššokantį langą naujame lange, kad galėtum prisijungti naudodamas (-a) U2F?"}, "enableFavicon": {"message": "Rodyti tinklalapių ikonėles"}, "faviconDesc": {"message": "Rodyti atpažįstamą vaizdą šalia kiekvieno prisijungimo."}, "faviconDescAlt": {"message": "Rodyti atpažįstamą vaizdą šalia kiekvieno prisijungimo. Taikoma visoms prisijungusioms paskyroms."}, "enableBadgeCounter": {"message": "Rodyti ženkliukų skaitiklį"}, "badgeCounterDesc": {"message": "<PERSON><PERSON><PERSON><PERSON>, kiek prisijungimų turi dabartiniame žiniatinklio puslapyje."}, "cardholderName": {"message": "Mok<PERSON><PERSON><PERSON> k<PERSON> sa<PERSON>"}, "number": {"message": "Numeris"}, "brand": {"message": "Prekės <PERSON>"}, "expirationMonth": {"message": "Gali<PERSON><PERSON><PERSON> p<PERSON> m<PERSON>"}, "expirationYear": {"message": "Galioji<PERSON> p<PERSON>i"}, "expiration": {"message": "Galiojimo pabaiga"}, "january": {"message": "Sausis"}, "february": {"message": "<PERSON><PERSON><PERSON>"}, "march": {"message": "<PERSON><PERSON>"}, "april": {"message": "<PERSON><PERSON><PERSON>"}, "may": {"message": "Gegužė"}, "june": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "july": {"message": "<PERSON><PERSON>"}, "august": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "september": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "october": {"message": "<PERSON><PERSON>"}, "november": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "december": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "securityCode": {"message": "Apsaugos kodas"}, "ex": {"message": "pvz."}, "title": {"message": "Pavadinimas"}, "mr": {"message": "<PERSON><PERSON>"}, "mrs": {"message": "Ponia"}, "ms": {"message": "Panelė"}, "dr": {"message": "Dr"}, "mx": {"message": "Neutralinis (-i)"}, "firstName": {"message": "Vardas"}, "middleName": {"message": "<PERSON><PERSON> vardas"}, "lastName": {"message": "Pa<PERSON><PERSON>"}, "fullName": {"message": "<PERSON><PERSON><PERSON> ir pava<PERSON>"}, "identityName": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "company": {"message": "Įmonė"}, "ssn": {"message": "ID kortelės numeris"}, "passportNumber": {"message": "Paso numeris"}, "licenseNumber": {"message": "Licencijos numeris"}, "email": {"message": "El. <PERSON>"}, "phone": {"message": "Telefonas"}, "address": {"message": "<PERSON><PERSON><PERSON>"}, "address1": {"message": "Adresas 1"}, "address2": {"message": "Adresas 2"}, "address3": {"message": "Adresas 3"}, "cityTown": {"message": "Miestas"}, "stateProvince": {"message": "<PERSON><PERSON><PERSON>/apskritis"}, "zipPostalCode": {"message": "<PERSON><PERSON><PERSON> k<PERSON>"}, "country": {"message": "<PERSON><PERSON>"}, "type": {"message": "Tipas"}, "typeLogin": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "typeLogins": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "typeSecureNote": {"message": "Saugus įrašas"}, "typeCard": {"message": "Kortelė"}, "typeIdentity": {"message": "Tapatybė"}, "typeSshKey": {"message": "SSH key"}, "newItemHeader": {"message": "Naujas $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "editItemHeader": {"message": "Redaguoti $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "viewItemHeader": {"message": "Peržiūrėti $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "passwordHistory": {"message": "Slaptažodžio istorija"}, "generatorHistory": {"message": "Generator history"}, "clearGeneratorHistoryTitle": {"message": "Clear generator history"}, "cleargGeneratorHistoryDescription": {"message": "If you continue, all entries will be permanently deleted from generator's history. Are you sure you want to continue?"}, "back": {"message": "Atgal"}, "collections": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "nCollections": {"message": "$COUNT$ collections", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "favorites": {"message": "Mėgstamiausi"}, "popOutNewWindow": {"message": "Atverti naujame lange"}, "refresh": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "cards": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "identities": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "logins": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "secureNotes": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sshKeys": {"message": "SSH Keys"}, "clear": {"message": "Išvalyti", "description": "To clear something out. example: To clear browser history."}, "checkPassword": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ar <PERSON><PERSON><PERSON><PERSON> buvo atskleistas."}, "passwordExposed": {"message": "Šis slaptažodis buvo atskleistas $VALUE$ kartus dėl duomenų pažeidimų. Turėtumėte jį pasikeisti.", "placeholders": {"value": {"content": "$1", "example": "2"}}}, "passwordSafe": {"message": "<PERSON>is slap<PERSON>žodis nebuvo rastas per jokius žinomus duomenų pažeidimus. Jis turėtų būti saugus naudoti."}, "baseDomain": {"message": "<PERSON><PERSON><PERSON>", "description": "Domain name. Ex. website.com"}, "baseDomainOptionRecommended": {"message": "Base domain (recommended)", "description": "Domain name. Ex. website.com"}, "domainName": {"message": "<PERSON><PERSON>", "description": "Domain name. Ex. website.com"}, "host": {"message": "<PERSON><PERSON>", "description": "A URL's host value. For example, the host of https://sub.domain.com:443 is 'sub.domain.com:443'."}, "exact": {"message": "Tikslus"}, "startsWith": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "regEx": {"message": "Reguliari išraiška", "description": "A programming term, also known as 'RegEx'."}, "matchDetection": {"message": "Atitikmens aptiki<PERSON>", "description": "URI match detection for autofill."}, "defaultMatchDetection": {"message": "Numatytasis atitikties aptikimas", "description": "Default URI match detection for autofill."}, "toggleOptions": {"message": "<PERSON><PERSON><PERSON><PERSON> opci<PERSON>"}, "toggleCurrentUris": {"message": "<PERSON>jun<PERSON><PERSON> da<PERSON>", "description": "Toggle the display of the URIs of the currently open tabs in the browser."}, "currentUri": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "The URI of one of the current open tabs in the browser."}, "organization": {"message": "Organizacija", "description": "An entity of multiple related people (ex. a team or business organization)."}, "types": {"message": "Tipai"}, "allItems": {"message": "Visi elementai"}, "noPasswordsInList": {"message": "Slaptažodžių sąraše nėra."}, "clearHistory": {"message": "Clear history"}, "nothingToShow": {"message": "Nothing to show"}, "nothingGeneratedRecently": {"message": "You haven't generated anything recently"}, "remove": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "default": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "dateUpdated": {"message": "<PERSON>na<PERSON>jin<PERSON>", "description": "ex. Date this item was updated"}, "dateCreated": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "ex. Date this item was created"}, "datePasswordUpdated": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "ex. Date this password was updated"}, "neverLockWarning": {"message": "Ar tikrai norite naudoti parinktį „Niekada“? Nustačius užrak<PERSON> parinktis į „Niekada“, saug<PERSON>los šifravimo raktas išsaugomas įrenginyje. Jei naudojate šią parinktį, tur<PERSON><PERSON><PERSON><PERSON> užtikrinti, kad jūsų įrenginys būtų tinkamai apsaugotas."}, "noOrganizationsList": {"message": "<PERSON><PERSON><PERSON>ote jokiai organizacijai. Organizacijos leidžia Jums saugiai bendrinti elementus su kitais vartotojais."}, "noCollectionsInList": {"message": "Kolekcijų sąraše nėra."}, "ownership": {"message": "Nuosavybė"}, "whoOwnsThisItem": {"message": "<PERSON>m p<PERSON>o š<PERSON> elementas?"}, "strong": {"message": "St<PERSON><PERSON>", "description": "ex. A strong password. Scale: Weak -> Good -> Strong"}, "good": {"message": "<PERSON><PERSON><PERSON>", "description": "ex. A good password. Scale: Weak -> Good -> Strong"}, "weak": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "ex. A weak password. Scale: Weak -> Good -> Strong"}, "weakMasterPassword": {"message": "<PERSON><PERSON><PERSON><PERSON> pagrindinis <PERSON>"}, "weakMasterPasswordDesc": {"message": "Jūsų pasirinktas pagrindinis slaptažodis yra silpnas. Turėtumėte naudoti stiprų pagrindinį slaptažodį (arba slaptafrazę), kad tinkamai apsaugotumėte savo „Bitwarden“ paskyrą. Ar tikrai norite naudoti šį pagrindinį slaptažodį?"}, "pin": {"message": "PIN", "description": "PIN code. Ex. The short code (often numeric) that you use to unlock a device."}, "unlockWithPin": {"message": "Atrakinti PIN kodu"}, "setYourPinTitle": {"message": "Set PIN"}, "setYourPinButton": {"message": "Set PIN"}, "setYourPinCode": {"message": "Nustatykite savo PIN kodą, kad atrakintumėte „Bitwarden“. Jūsų PIN nustatymai bus nustatyti iš naujo, jei kada nors visiškai atsijungsite nuo programos."}, "setPinCode": {"message": "You can use this PIN to unlock Bitwarden. Your PIN will be reset if you ever fully log out of the application."}, "pinRequired": {"message": "PIN kodas yra privalomas."}, "invalidPin": {"message": "Neteisingas PIN kodas."}, "tooManyInvalidPinEntryAttemptsLoggingOut": {"message": "Per daug netinkamų PIN kodo įvedimo bandymų. Atsijungima."}, "unlockWithBiometrics": {"message": "Atrakinti naudojant biometrinius duomenis"}, "unlockWithMasterPassword": {"message": "Unlock with master password"}, "awaitDesktop": {"message": "<PERSON><PERSON><PERSON> pat<PERSON><PERSON><PERSON>"}, "awaitDesktopDesc": {"message": "Patvirtinkite naudodami biometrinius duomenis „Bitwarden“ darbalaukio programoje, kad nustatytumėte naršyklės biometrinius duomenis."}, "lockWithMasterPassOnRestart": {"message": "Užrakinkite pagrindiniu slaptažodžiu paleidus naršyklę iš naujo"}, "lockWithMasterPassOnRestart1": {"message": "Require master password on browser restart"}, "selectOneCollection": {"message": "Turite pasirinkti bent vieną kategoriją."}, "cloneItem": {"message": "Klonuoti elementą"}, "clone": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "passwordGenerator": {"message": "Password generator"}, "usernameGenerator": {"message": "Username generator"}, "useThisEmail": {"message": "Use this email"}, "useThisPassword": {"message": "Use this password"}, "useThisPassphrase": {"message": "Use this passphrase"}, "useThisUsername": {"message": "Use this username"}, "securePasswordGenerated": {"message": "Secure password generated! Don't forget to also update your password on the website."}, "useGeneratorHelpTextPartOne": {"message": "Use the generator", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "useGeneratorHelpTextPartTwo": {"message": "to create a strong unique password", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "vaultCustomization": {"message": "Vault customization"}, "vaultTimeoutAction": {"message": "<PERSON><PERSON> skirtojo laiko ve<PERSON>s"}, "vaultTimeoutAction1": {"message": "Timeout action"}, "lock": {"message": "Užrakinti", "description": "Verb form: to make secure or inaccessible by"}, "trash": {"message": "Šiukšliadėžė", "description": "Noun: a special folder to hold deleted items"}, "searchTrash": {"message": "Ieškoti šiukšliadėžėje"}, "permanentlyDeleteItem": {"message": "<PERSON><PERSON><PERSON><PERSON> visam laikui"}, "permanentlyDeleteItemConfirmation": {"message": "Ar tai tikrai norite visam laikui i<PERSON>?"}, "permanentlyDeletedItem": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> visam laikui"}, "restoreItem": {"message": "Atkurti elementą"}, "restoredItem": {"message": "Elementas atkurtas"}, "alreadyHaveAccount": {"message": "Already have an account?"}, "vaultTimeoutLogOutConfirmation": {"message": "Atsijungus bus pašalinta visa Jūsų prieiga prie saugyklos, o pasibaigus skirtajam laikotarpiui bus reikalinga autentifikacija internetu. Ar tikrai norite naudoti šį nustatymą?"}, "vaultTimeoutLogOutConfirmationTitle": {"message": "<PERSON><PERSON> p<PERSON> ve<PERSON> pat<PERSON>"}, "autoFillAndSave": {"message": "Automatiškai užpildyti ir išsaugoti"}, "fillAndSave": {"message": "Fill and save"}, "autoFillSuccessAndSavedUri": {"message": "Elementas automatiškai užpildytas ir URI išsaugotas"}, "autoFillSuccess": {"message": "Elementas užpildytas automatiškai "}, "insecurePageWarning": {"message": "Įspėjimas: Tai – neapsaugotas HTTP puslapis, to<PERSON><PERSON><PERSON> bet kokią pateiktą informaciją gali matyti ir keisti kiti asmenys. Šis prisijungimas iš pradžių buvo išsaugotas saugiam<PERSON> (HTTPS) puslapyje."}, "insecurePageWarningFillPrompt": {"message": "Ar vis tiek norite užpildyti šį prisijungimą?"}, "autofillIframeWarning": {"message": "Formą priglobia kitas domenas nei išsaugoto prisijungimo URI. Pasirinkite „Gerai“, kad vis tiek pildytumėte automatiškai arba „Atšaukti“, kad sustabdytumėte."}, "autofillIframeWarningTip": {"message": "Norėdami išvengti šio įspėjimo ateityje išsaugokite šį URI $HOSTNAME$, <PERSON><PERSON><PERSON><PERSON> „Bitwarden“, š<PERSON> s<PERSON>, prisijungimo elemente.", "placeholders": {"hostname": {"content": "$1", "example": "www.example.com"}}}, "setMasterPassword": {"message": "Pagrindinio slaptažodžio nustatymas"}, "currentMasterPass": {"message": "<PERSON><PERSON><PERSON><PERSON> pagrindinis <PERSON>"}, "newMasterPass": {"message": "<PERSON><PERSON><PERSON> pag<PERSON>"}, "confirmNewMasterPass": {"message": "<PERSON><PERSON><PERSON><PERSON> nauj<PERSON> pagrindinį slaptažodį"}, "masterPasswordPolicyInEffect": {"message": "Viena ar daugiau organizacijos politikos reikalauja, kad tavo pagrindinis slaptažodis atitiktų šiuos reikalavimus:"}, "policyInEffectMinComplexity": {"message": "Minimalus sudėtingumo balas $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "policyInEffectMinLength": {"message": "Minimalus ilgis $LENGTH$", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "policyInEffectUppercase": {"message": "<PERSON><PERSON> vien<PERSON> ar daugiau <PERSON>ž<PERSON>"}, "policyInEffectLowercase": {"message": "<PERSON><PERSON> vien<PERSON> ar daugiau mažųjų raidžių"}, "policyInEffectNumbers": {"message": "<PERSON><PERSON> vien<PERSON> ar daugiau s<PERSON>"}, "policyInEffectSpecial": {"message": "<PERSON><PERSON> vien<PERSON> ar daugiau iš šių specialiųjų simbolių: $CHARS$", "placeholders": {"chars": {"content": "$1", "example": "!@#$%^&*"}}}, "masterPasswordPolicyRequirementsNotMet": {"message": "Tavo naujasis pagrindinis slaptažodis neatitinka politikos reikalavimų."}, "receiveMarketingEmailsV2": {"message": "Get advice, announcements, and research opportunities from Bitwarden in your inbox."}, "unsubscribe": {"message": "Atsisakyti prenumeratos"}, "atAnyTime": {"message": "bet kuriuo metu."}, "byContinuingYouAgreeToThe": {"message": "Tęsiant sutinkate su"}, "and": {"message": "ir"}, "acceptPolicies": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> šį laukelį, sutinkate su šia<PERSON> da<PERSON>:"}, "acceptPoliciesRequired": {"message": "Paslaugų teikimo sąlygos ir privatumo politika nebuvo pripažinti."}, "termsOfService": {"message": "Paslaugų teikimo paslaugos"}, "privacyPolicy": {"message": "Privatumo politika"}, "yourNewPasswordCannotBeTheSameAsYourCurrentPassword": {"message": "Your new password cannot be the same as your current password."}, "hintEqualsPassword": {"message": "Jūsų slaptažodžio užuomina negali būti tokia pati kaip <PERSON> slaptažod<PERSON>."}, "ok": {"message": "G<PERSON><PERSON>"}, "errorRefreshingAccessToken": {"message": "Access Token Refresh Error"}, "errorRefreshingAccessTokenDesc": {"message": "No refresh token or API keys found. Please try logging out and logging back in."}, "desktopSyncVerificationTitle": {"message": "Darbalaukio sinchronizavimo verifikavimas"}, "desktopIntegrationVerificationText": {"message": "<PERSON><PERSON><PERSON><PERSON>, ar darb<PERSON><PERSON>o programoje rodoma<PERSON> š<PERSON> piršt<PERSON> atspaudas: "}, "desktopIntegrationDisabledTitle": {"message": "Naršyklės integracija nėra nustatyta"}, "desktopIntegrationDisabledDesc": {"message": "Naršyklės integravimas nenustatytas Bitwarden darbalaukio programoje. Nustatykite jį darbalaukio programos nustatymuose."}, "startDesktopTitle": {"message": "Paleiskite Bitwarden darbalaukio programą"}, "startDesktopDesc": {"message": "Reikia paleisti Bitwarden darbalaukio programą prieš naudojant atrakinimą biometriniais duomenimis."}, "errorEnableBiometricTitle": {"message": "Nepavyko nustatyti biometrinių duomenų"}, "errorEnableBiometricDesc": {"message": "Darbalaukio programa atšaukė veiksmą"}, "nativeMessagingInvalidEncryptionDesc": {"message": "Darbalaukio programa panaikino saugų ryšio kanalą. Pabandykite pakartoti šią operaciją"}, "nativeMessagingInvalidEncryptionTitle": {"message": "Darbalaukio ryš<PERSON>"}, "nativeMessagingWrongUserDesc": {"message": "Darbalaukio programa yra prisijungusi prie kitos paskyros. Įsitikinkite, kad abi programos yra prisijungusios prie tos pačios paskyros."}, "nativeMessagingWrongUserTitle": {"message": "<PERSON><PERSON><PERSON>"}, "nativeMessagingWrongUserKeyTitle": {"message": "Biometric key missmatch"}, "nativeMessagingWrongUserKeyDesc": {"message": "Biometric unlock failed. The biometric secret key failed to unlock the vault. Please try to set up biometrics again."}, "biometricsNotEnabledTitle": {"message": "Trūksta biometrinių duomenų nustatymų"}, "biometricsNotEnabledDesc": {"message": "Pirma reikia nustatymuose nustatyti darbalaukio biometrinius duomenys, prie<PERSON> juos naudo<PERSON>t na<PERSON>."}, "biometricsNotSupportedTitle": {"message": "Biometrika negali b<PERSON><PERSON> na<PERSON>"}, "biometricsNotSupportedDesc": {"message": "Šiame įrenginyje biometrikos negalima naudoti."}, "biometricsNotUnlockedTitle": {"message": "Naudotojas užrakintas arba atsijungęs"}, "biometricsNotUnlockedDesc": {"message": "Atrakinkite šį naudotoją darbalaukio programoje ir bandykite dar kartą."}, "biometricsNotAvailableTitle": {"message": "Biometric unlock unavailable"}, "biometricsNotAvailableDesc": {"message": "Biometric unlock is currently unavailable. Please try again later."}, "biometricsFailedTitle": {"message": "Biometrika nepavyko"}, "biometricsFailedDesc": {"message": "Biometriniai duomenys negali bū<PERSON>, apsvarstyk galimybę naudoti pagrindinį slaptažodį arba atsijungti. <PERSON>i tai tę<PERSON>, k<PERSON><PERSON><PERSON><PERSON><PERSON>s į Bitwarden pagalbą."}, "nativeMessaginPermissionErrorTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "nativeMessaginPermissionErrorDesc": {"message": "Negalime pateikti biometrinių duomenų naršyklės plėtinyje be leidimo susisiekti su Bitwarden darbalaukio programa. Bandykite dar kartą."}, "nativeMessaginPermissionSidebarTitle": {"message": "Teisių užklausos klaida"}, "nativeMessaginPermissionSidebarDesc": {"message": "Šio veiksmo negalima atlikti šoninėje juostoje, pabandykite dar kartą atlikti veiksmą iššokančiajame lange."}, "personalOwnershipSubmitError": {"message": "Dėl įmonės politikos Jums draudžiama saugoti elementus į savo asmeninę saugyklą. Pakeiskite nuosavybės parinktį į organizaciją ir pasirinkite iš galimų rinkinių."}, "personalOwnershipPolicyInEffect": {"message": "Organizacijos politika turi įtakos Jū<PERSON>ų nuosavybės pasirinkimo galimybėms."}, "personalOwnershipPolicyInEffectImports": {"message": "Organizacijos politika blokavo elementų importavimą į Jūsų individualią saugyklą."}, "domainsTitle": {"message": "Domenai", "description": "A category title describing the concept of web domains"}, "blockedDomains": {"message": "Blocked domains"}, "learnMoreAboutBlockedDomains": {"message": "Learn more about blocked domains"}, "excludedDomains": {"message": "Išskirti domenai"}, "excludedDomainsDesc": {"message": "„Bitwarden“ neprašys išsaugoti šių domenų prisijungimo duomenų. Turite atnaujinti puslapį, kad pokyčiai pradėtų galioti."}, "excludedDomainsDescAlt": {"message": "„Bitwarden“ neprašys išsaugoti prisijungimo detalių šiems domenams, visose prisijungusiose paskyrose. Turite atnaujinti puslapį, kad pokyčiai pradėtų galioti."}, "blockedDomainsDesc": {"message": "Autofill and other related features will not be offered for these websites. You must refresh the page for changes to take effect."}, "autofillBlockedNoticeV2": {"message": "Autofill is blocked for this website."}, "autofillBlockedNoticeGuidance": {"message": "Change this in settings"}, "change": {"message": "Change"}, "changePassword": {"message": "Change password", "description": "Change password button for browser at risk notification on login."}, "changeButtonTitle": {"message": "Change password - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "atRiskPassword": {"message": "At-risk password"}, "atRiskPasswords": {"message": "At-risk passwords"}, "atRiskPasswordDescSingleOrg": {"message": "$ORGANIZATION$ is requesting you change one password because it is at-risk.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}}, "atRiskPasswordsDescSingleOrgPlural": {"message": "$ORGANIZATION$ is requesting you change the $COUNT$ passwords because they are at-risk.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}, "count": {"content": "$2", "example": "2"}}}, "atRiskPasswordsDescMultiOrgPlural": {"message": "Your organizations are requesting you change the $COUNT$ passwords because they are at-risk.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "atRiskChangePrompt": {"message": "Your password for this site is at-risk. $ORGANIZATION$ has requested that you change it.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}, "description": "Notification body when a login triggers an at-risk password change request and the change password domain is known."}, "atRiskNavigatePrompt": {"message": "$ORGANIZATION$ wants you to change this password because it is at-risk. Navigate to your account settings to change the password.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}, "description": "Notification body when a login triggers an at-risk password change request and no change password domain is provided."}, "reviewAndChangeAtRiskPassword": {"message": "Review and change one at-risk password"}, "reviewAndChangeAtRiskPasswordsPlural": {"message": "Review and change $COUNT$ at-risk passwords", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "changeAtRiskPasswordsFaster": {"message": "Change at-risk passwords faster"}, "changeAtRiskPasswordsFasterDesc": {"message": "Update your settings so you can quickly autofill your passwords and generate new ones"}, "reviewAtRiskLogins": {"message": "Review at-risk logins"}, "reviewAtRiskPasswords": {"message": "Review at-risk passwords"}, "reviewAtRiskLoginsSlideDesc": {"message": "Your organization passwords are at-risk because they are weak, reused, and/or exposed.", "description": "Description of the review at-risk login slide on the at-risk password page carousel"}, "reviewAtRiskLoginSlideImgAltPeriod": {"message": "Illustration of a list of logins that are at-risk."}, "generatePasswordSlideDesc": {"message": "Quickly generate a strong, unique password with the Bitwarden autofill menu on the at-risk site.", "description": "Description of the generate password slide on the at-risk password page carousel"}, "generatePasswordSlideImgAltPeriod": {"message": "Illustration of the Bitwarden autofill menu displaying a generated password."}, "updateInBitwarden": {"message": "Update in Bitwarden"}, "updateInBitwardenSlideDesc": {"message": "Bitwarden will then prompt you to update the password in the password manager.", "description": "Description of the update in Bitwarden slide on the at-risk password page carousel"}, "updateInBitwardenSlideImgAltPeriod": {"message": "Illustration of a Bitwarden’s notification prompting the user to update the login."}, "turnOnAutofill": {"message": "Turn on autofill"}, "turnedOnAutofill": {"message": "Turned on autofill"}, "dismiss": {"message": "<PERSON><PERSON><PERSON>"}, "websiteItemLabel": {"message": "Website $number$ (URI)", "placeholders": {"number": {"content": "$1", "example": "3"}}}, "excludedDomainsInvalidDomain": {"message": "$DOMAIN$ yra klaidingas domenas", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "blockedDomainsSavedSuccess": {"message": "Blocked domain changes saved"}, "excludedDomainsSavedSuccess": {"message": "Excluded domain changes saved"}, "limitSendViews": {"message": "Riboti <PERSON>ž<PERSON>ū<PERSON>ų"}, "limitSendViewsHint": {"message": "<PERSON><PERSON><PERSON>, niekas negal<PERSON> šį „Send“.", "description": "Displayed under the limit views field on Send"}, "limitSendViewsCount": {"message": "Liko $ACCESSCOUNT$ peržiūrų", "description": "Displayed under the limit views field on Send", "placeholders": {"accessCount": {"content": "$1", "example": "2"}}}, "send": {"message": "Si<PERSON>sti", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDetails": {"message": "Send details", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendTypeText": {"message": "Tekstas"}, "sendTypeTextToShare": {"message": "Text to share"}, "sendTypeFile": {"message": "<PERSON><PERSON>"}, "allSends": {"message": "Visi siuntimai", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "maxAccessCountReached": {"message": "Max access count reached", "description": "This text will be displayed after a Send has been accessed the maximum amount of times."}, "hideTextByDefault": {"message": "Hide text by default"}, "expired": {"message": "Nebegalioja"}, "passwordProtected": {"message": "Apsaugota slaptažodžiu"}, "copyLink": {"message": "Copy link"}, "copySendLink": {"message": "Kopijuoti siuntimo nuorodą", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "removePassword": {"message": "<PERSON><PERSON><PERSON> slaptažodį"}, "delete": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "removedPassword": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "deletedSend": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLink": {"message": "Siųsti nuorodą", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "disabled": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "removePasswordConfirmation": {"message": "Ar tikrai norite pa<PERSON>linti slaptažodį?"}, "deleteSend": {"message": "<PERSON><PERSON><PERSON><PERSON> siuntinį", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendConfirmation": {"message": "Ar tikrai norite ištrinti šį „Send“?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendPermanentConfirmation": {"message": "Are you sure you want to permanently delete this Send?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editSend": {"message": "<PERSON><PERSON><PERSON><PERSON> „Send“", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deletionDate": {"message": "Ištrynimo data"}, "deletionDateDescV2": {"message": "The Send will be permanently deleted on this date.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "expirationDate": {"message": "Galiojimo data"}, "oneDay": {"message": "1 d"}, "days": {"message": "$DAYS$ dienos", "placeholders": {"days": {"content": "$1", "example": "2"}}}, "custom": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sendPasswordDescV3": {"message": "Add an optional password for recipients to access this Send.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createSend": {"message": "<PERSON><PERSON><PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "newPassword": {"message": "<PERSON><PERSON><PERSON>"}, "sendDisabled": {"message": "Siuntimas <PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDisabledWarning": {"message": "Dėl įmonės politikos galite ištrinti tik esamą „Send“.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSend": {"message": "<PERSON><PERSON><PERSON><PERSON> sukurta<PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSendSuccessfully": {"message": "Send created successfully!", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHoursSingle": {"message": "The Send will be available to anyone with the link for the next 1 hour.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHours": {"message": "The Send will be available to anyone with the link for the next $HOURS$ hours.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"hours": {"content": "$1", "example": "5"}}}, "sendExpiresInDaysSingle": {"message": "The Send will be available to anyone with the link for the next 1 day.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInDays": {"message": "The Send will be available to anyone with the link for the next $DAYS$ days.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"days": {"content": "$1", "example": "5"}}}, "sendLinkCopied": {"message": "Send link copied", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editedSend": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogText": {"message": "Pop out extension?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogDesc": {"message": "To create a file Send, you need to pop out the extension to a new window.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLinuxChromiumFileWarning": {"message": "Norėdami pasirinkti failą, atidarykite plėtinį šoninėje juo<PERSON> (jei įmanoma) arba iššokkite į naują langą spustelėdami šią reklamjuostę."}, "sendFirefoxFileWarning": {"message": "Norėdami pasirinkti failą, na<PERSON><PERSON><PERSON>Fox, atidarykite plėtinį šonin<PERSON><PERSON> juo<PERSON> (jei įmanoma) arba iššokkite į naują langą spustelėdami šią reklamjuostę."}, "sendSafariFileWarning": {"message": "Norėdami pasirinkti failą naudodami „Safari“, iššokkite į naują langą spustelėdami šią reklamju<PERSON>ę."}, "popOut": {"message": "Pop out"}, "sendFileCalloutHeader": {"message": "<PERSON><PERSON><PERSON> p<PERSON>"}, "expirationDateIsInvalid": {"message": "Nurodytas galiojimo laikas negalioja."}, "deletionDateIsInvalid": {"message": "Nurodyta ištrynimo data negalioja."}, "expirationDateAndTimeRequired": {"message": "Ištrynimo data ir laikas yra privalomi."}, "deletionDateAndTimeRequired": {"message": "Ištrynimo data ir laikas yra privalomi."}, "dateParsingError": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> jū<PERSON><PERSON> ištryni<PERSON> ir galiojimo datas įvyko klaida."}, "hideYourEmail": {"message": "Slėpkite savo el. pašto adresą nuo žiūrėtojų."}, "passwordPrompt": {"message": "<PERSON><PERSON> naujo praš<PERSON> pagrindinio slaptažodžio"}, "passwordConfirmation": {"message": "Pagrindinio <PERSON>žodž<PERSON>"}, "passwordConfirmationDesc": {"message": "Negalite šio ve<PERSON> padaryti neįvedus savo pagrindinio slaptažodžio ir patvirtinę tapatybę."}, "emailVerificationRequired": {"message": "Reikalingas elektroninio pa<PERSON><PERSON>"}, "emailVerifiedV2": {"message": "Patvirtintas el. paštas"}, "emailVerificationRequiredDesc": {"message": "Turite patvirtinti savo el. pa<PERSON>, kad gal<PERSON>te naudotis šia funkcija. Savo el. pašto adresą galite patvirtinti ž<PERSON>."}, "updatedMasterPassword": {"message": "<PERSON><PERSON><PERSON><PERSON> pag<PERSON>"}, "updateMasterPassword": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> pagrindinį slaptažodį"}, "updateMasterPasswordWarning": {"message": "Jūsų pagrindinis slaptažodis neseniai pakeistas Jūsų organizacijos administratorius. Privalote atnaujinti pagrindinį slaptažodį, kad galėtumėte pasiekti saugyklą. Tęsdami būsite atjungtas nuo dabartinės savo sesijos, todėl turėsite vėl prisijungti. Aktyvios se<PERSON>, kituose įrenginiuose, gali išlikti aktyvios iki vienos valandos."}, "updateWeakMasterPasswordWarning": {"message": "Jūsų pagrindinis slaptažodis neatitinka vieno ar kelių organizacijos slaptažodžiui keliamų reikalavimų. Privalote atnaujinti pagrindinį slaptažodį, kad galėtumėte pasiekti saugyklą. Tęsdami būsite atjungtas nuo dabartinės savo sesijos, todėl turėsite vėl prisijungti. Aktyvios se<PERSON>, kituose įrenginiuose, gali išlikti aktyvios iki vienos valandos."}, "tdeDisabledMasterPasswordRequired": {"message": "Your organization has disabled trusted device encryption. Please set a master password to access your vault."}, "resetPasswordPolicyAutoEnroll": {"message": "Automatinis registravimas"}, "resetPasswordAutoEnrollInviteWarning": {"message": "Ši organizacija turi įmonės politiką, kuri automatiškai įtrauks Jus į slaptažodžio nustatymą iš naujo. Registravimas leis organizacijos administratoriams pakeisti Jūsų pagrindinį slaptažodį."}, "selectFolder": {"message": "Pasirinkti aplankalą..."}, "noFoldersFound": {"message": "Nerasta aplankų.", "description": "Used as a message within the notification bar when no folders are found"}, "orgPermissionsUpdatedMustSetPassword": {"message": "Dėl organizacijos le<PERSON>, Jums reikia nustatyti pagrindinį slaptažodį.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "orgRequiresYouToSetPassword": {"message": "Jūsų organizacija reikalauja Jus nustatyti pagrindinį slaptažodį.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "cardMetrics": {"message": "out of $TOTAL$", "placeholders": {"total": {"content": "$1", "example": "5"}}}, "verificationRequired": {"message": "<PERSON><PERSON><PERSON><PERSON> pat<PERSON>", "description": "Default title for the user verification dialog."}, "hours": {"message": "Valandų"}, "minutes": {"message": "Minučių"}, "vaultTimeoutPolicyAffectingOptions": {"message": "Enterprise policy requirements have been applied to your timeout options"}, "vaultTimeoutPolicyInEffect": {"message": "Jūsų organizacijos politika nustatė Jū<PERSON><PERSON> didžiausią leidžiamą saugyklos laiko limitą į $HOURS$ valandas(-ų) ir $MINUTES$ minutes(-čių).", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyInEffect1": {"message": "$HOURS$ hour(s) and $MINUTES$ minute(s) maximum.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyMaximumError": {"message": "Timeout exceeds the restriction set by your organization: $HOURS$ hour(s) and $MINUTES$ minute(s) maximum", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyWithActionInEffect": {"message": "Jūsų organizacijos politika apriboja Jūsų saugyklos neaktyvumo laiko nustatymus. <PERSON><PERSON><PERSON><PERSON> le<PERSON>as saugyklos neaktyvumo laikas yra $HOURS$ valanda(-os) ir $MINUTES$ minutė(s). Jūsų saugyklos neaktyvumo laiko veiksmas yra nustatytas $ACTION$.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}, "action": {"content": "$3", "example": "Lock"}}}, "vaultTimeoutActionPolicyInEffect": {"message": "Jūsų organizacijos politika nustatė J<PERSON><PERSON><PERSON> saugyklos neaktyvumo veiksmą į $ACTION$.", "placeholders": {"action": {"content": "$1", "example": "Lock"}}}, "vaultTimeoutTooLarge": {"message": "<PERSON><PERSON><PERSON><PERSON> saugyklos neaktyvumo laikas viršija <PERSON>ų organizacijos nustatytus apribojimus."}, "vaultExportDisabled": {"message": "Saug<PERSON>los eksportavimas <PERSON>"}, "personalVaultExportPolicyInEffect": {"message": "Viena ar daugiau organizacijos strategijų neleidžia Jums eksportuoti asmeninės <PERSON>."}, "copyCustomFieldNameInvalidElement": {"message": "Nepavyko nustatyti tinkamo formos elemento. Vietoj to pabandykite patikrinti HTML."}, "copyCustomFieldNameNotUnique": {"message": "Unikalus identifikatorius ne<PERSON>."}, "removeMasterPasswordForOrganizationUserKeyConnector": {"message": "A master password is no longer required for members of the following organization. Please confirm the domain below with your organization administrator."}, "organizationName": {"message": "Organization name"}, "keyConnectorDomain": {"message": "Key Connector domain"}, "leaveOrganization": {"message": "Palikti organizaciją"}, "removeMasterPassword": {"message": "Ištrinti pagrindinį slaptažodį"}, "removedMasterPassword": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "leaveOrganizationConfirmation": {"message": "Ar tikrai norite palikti šią organizaciją?"}, "leftOrganization": {"message": "<PERSON><PERSON><PERSON> pali<PERSON>e organizaciją."}, "toggleCharacterCount": {"message": "Perjungti simbolių skaičių"}, "sessionTimeout": {"message": "Jūsų sesija pasibaigė. Prašome grįžti atgal ir prisijungti iš naujo."}, "exportingPersonalVaultTitle": {"message": "Eksportuojama individuali saugykla"}, "exportingIndividualVaultDescription": {"message": "Bus eksportuoti tik individualūs saugyklos elementai, kurie susiję su $EMAIL$. Organizacijos saugyklos elementai nebus įtraukti. Bus eksportuota tik saugyklos elementų informacija, susiję priedai nebus įtraukti.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingIndividualVaultWithAttachmentsDescription": {"message": "Only the individual vault items including attachments associated with $EMAIL$ will be exported. Organization vault items will not be included", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingOrganizationVaultTitle": {"message": "Exporting organization vault"}, "exportingOrganizationVaultDesc": {"message": "Only the organization vault associated with $ORGANIZATION$ will be exported. Items in individual vaults or other organizations will not be included.", "placeholders": {"organization": {"content": "$1", "example": "ACME Moving Co."}}}, "error": {"message": "<PERSON><PERSON><PERSON>"}, "decryptionError": {"message": "Decryption error"}, "couldNotDecryptVaultItemsBelow": {"message": "<PERSON><PERSON><PERSON> could not decrypt the vault item(s) listed below."}, "contactCSToAvoidDataLossPart1": {"message": "Contact customer success", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "contactCSToAvoidDataLossPart2": {"message": "to avoid additional data loss.", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "generateUsername": {"message": "<PERSON><PERSON><PERSON><PERSON> var<PERSON><PERSON> vard<PERSON>"}, "generateEmail": {"message": "Generate email"}, "spinboxBoundariesHint": {"message": "Value must be between $MIN$ and $MAX$.", "description": "Explains spin box minimum and maximum values to the user", "placeholders": {"min": {"content": "$1", "example": "8"}, "max": {"content": "$2", "example": "128"}}}, "passwordLengthRecommendationHint": {"message": " Use $RECOMMENDED$ characters or more to generate a strong password.", "description": "Appended to `spinboxBoundariesHint` to recommend a length to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "14"}}}, "passphraseNumWordsRecommendationHint": {"message": " Use $RECOMMENDED$ words or more to generate a strong passphrase.", "description": "Appended to `spinboxBoundariesHint` to recommend a number of words to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "6"}}}, "plusAddressedEmail": {"message": "<PERSON><PERSON> adresuotas el. pa<PERSON>", "description": "Username generator option that appends a random sub-address to the username. For example: <EMAIL>"}, "plusAddressedEmailDesc": {"message": "Pasinaudokite savo el. pašto teikėjo antrinio adreso galim<PERSON>bėmis."}, "catchallEmail": {"message": "Viską palaikantis el. paštas"}, "catchallEmailDesc": {"message": "Naudokite savo domeno sukonfigūruotą viską palaikančią pašto <PERSON>."}, "random": {"message": "Atsitiktinis"}, "randomWord": {"message": "Atsiti<PERSON><PERSON><PERSON>"}, "websiteName": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>"}, "service": {"message": "Paslauga"}, "forwardedEmail": {"message": "Persiunčiamas el. p<PERSON><PERSON><PERSON>"}, "forwardedEmailDesc": {"message": "Sugeneruoti el. pašto slapyvardį su išorine persiuntimo paslauga."}, "forwarderDomainName": {"message": "Email domain", "description": "Labels the domain name email forwarder service option"}, "forwarderDomainNameHint": {"message": "Choose a domain that is supported by the selected service", "description": "Guidance provided for email forwarding services that support multiple email domains."}, "forwarderError": {"message": "„$SERVICENAME$“ klaida: $ERRORMESSAGE$.", "description": "Reports an error returned by a forwarding service to the user.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Invalid characters in domain name."}}}, "forwarderGeneratedBy": {"message": "Sugeneravo „Bitwarden“.", "description": "Displayed with the address on the forwarding service's configuration screen."}, "forwarderGeneratedByWithWebsite": {"message": "Svetainė: $WEBSITE$. Sugeneravo „Bitwarden“.", "description": "Displayed with the address on the forwarding service's configuration screen.", "placeholders": {"WEBSITE": {"content": "$1", "example": "www.example.com"}}}, "forwaderInvalidToken": {"message": "Netinkamas „$SERVICENAME$“ API prieigos raktas.", "description": "Displayed when the user's API token is empty or rejected by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidTokenWithMessage": {"message": "Netinkamas „$SERVICENAME$“ API prieigos raktas: $ERRORMESSAGE$.", "description": "Displayed when the user's API token is rejected by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwaderInvalidOperation": {"message": "$SERVICENAME$ refused your request. Please contact your service provider for assistance.", "description": "Displayed when the user is forbidden from using the API by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidOperationWithMessage": {"message": "$SERVICENAME$ refused your request: $ERRORMESSAGE$", "description": "Displayed when the user is forbidden from using the API by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwarderNoAccountId": {"message": "Nepavyksta gauti „$SERVICENAME$“ užmaskuoto el. pašto paskyros ID.", "description": "Displayed when the forwarding service fails to return an account ID.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoDomain": {"message": "Netinkamas „$SERVICENAME$“ domenas.", "description": "Displayed when the domain is empty or domain authorization failed at the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoUrl": {"message": "Netinkamas „$SERVICENAME$“ URL.", "description": "Displayed when the url of the forwarding service wasn't supplied.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownError": {"message": "Įvyko nežinoma „$SERVICENAME$“ klaida.", "description": "Displayed when the forwarding service failed due to an unknown error.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownForwarder": {"message": "Nežinomas persiuntėjas: „$SERVICENAME$“.", "description": "Displayed when the forwarding service is not supported.", "placeholders": {"servicename": {"content": "$1", "example": "JustTrust.us"}}}, "hostname": {"message": "Pagrindinio kompiuterio vardas", "description": "Part of a URL."}, "apiAccessToken": {"message": "API Prieigos Raktas"}, "apiKey": {"message": "API raktas"}, "ssoKeyConnectorError": {"message": "Rakto jungties klaida: įsitikinkite, kad rakto jungtis yra prieinama ir veikia tinkamai."}, "premiumSubcriptionRequired": {"message": "Reikalingas Premium abonementas"}, "organizationIsDisabled": {"message": "Organizacija suspenduota."}, "disabledOrganizationFilterError": {"message": "Elementai esantys suspenduotose Organizacijose yra neprieinami. Susisiekite su savo Organizacijos savininku."}, "loggingInTo": {"message": "Prisijungiama prie $DOMAIN$", "placeholders": {"domain": {"content": "$1", "example": "example.com"}}}, "serverVersion": {"message": "Serverio versija"}, "selfHostedServer": {"message": "savarankiškai sukurtas"}, "thirdParty": {"message": "Treč<PERSON><PERSON>"}, "thirdPartyServerMessage": {"message": "Prisijungta prie trečiosios šalies serverio diegimo $SERVERNAME$. Patikrinkite klaidas naudodami oficialų serverį arba praneškite apie jas trečiosios šalies serveriui.", "placeholders": {"servername": {"content": "$1", "example": "ThirdPartyServerName"}}}, "lastSeenOn": {"message": "paskutinį kartą matytas: $DATE$", "placeholders": {"date": {"content": "$1", "example": "Jun 15, 2015"}}}, "loginWithMasterPassword": {"message": "Prisijungti su pagrindiniu slaptažodžiu"}, "newAroundHere": {"message": "Ar jūs na<PERSON>?"}, "rememberEmail": {"message": "Prisiminti el. pa<PERSON>ą"}, "loginWithDevice": {"message": "Prisijunkite naudodami įrenginį"}, "fingerprintPhraseHeader": {"message": "Pirštų atspaudų frazė"}, "fingerprintMatchInfo": {"message": "Please make sure your vault is unlocked and the Fingerprint phrase matches on the other device."}, "resendNotification": {"message": "<PERSON><PERSON>jo <PERSON> p<PERSON>"}, "viewAllLogInOptions": {"message": "View all log in options"}, "notificationSentDevice": {"message": "A notification has been sent to your device."}, "notificationSentDevicePart1": {"message": "Unlock Bitwarden on your device or on the"}, "notificationSentDeviceAnchor": {"message": "web app"}, "notificationSentDevicePart2": {"message": "Make sure the Fingerprint phrase matches the one below before approving."}, "aNotificationWasSentToYourDevice": {"message": "A notification was sent to your device"}, "youWillBeNotifiedOnceTheRequestIsApproved": {"message": "You will be notified once the request is approved"}, "needAnotherOptionV1": {"message": "Need another option?"}, "loginInitiated": {"message": "Pradėtas prisijungimas"}, "logInRequestSent": {"message": "Request sent"}, "exposedMasterPassword": {"message": "Exposed Master Password"}, "exposedMasterPasswordDesc": {"message": "Password found in a data breach. Use a unique password to protect your account. Are you sure you want to use an exposed password?"}, "weakAndExposedMasterPassword": {"message": "Weak and Exposed Master Password"}, "weakAndBreachedMasterPasswordDesc": {"message": "Weak password identified and found in a data breach. Use a strong and unique password to protect your account. Are you sure you want to use this password?"}, "checkForBreaches": {"message": "Check known data breaches for this password"}, "important": {"message": "Svarbu:"}, "masterPasswordHint": {"message": "Your master password cannot be recovered if you forget it!"}, "characterMinimum": {"message": "$LENGTH$ character minimum", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "autofillPageLoadPolicyActivated": {"message": "Your organization policies have turned on autofill on page load."}, "howToAutofill": {"message": "Kaip automatiškai užpildyti"}, "autofillSelectInfoWithCommand": {"message": "Select an item from this screen, use the shortcut $COMMAND$, or explore other options in settings.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillSelectInfoWithoutCommand": {"message": "Select an item from this screen, or explore other options in settings."}, "gotIt": {"message": "Got it"}, "autofillSettings": {"message": "Autofill settings"}, "autofillKeyboardShortcutSectionTitle": {"message": "Autofill shortcut"}, "autofillKeyboardShortcutUpdateLabel": {"message": "Change shortcut"}, "autofillKeyboardManagerShortcutsLabel": {"message": "Manage shortcuts"}, "autofillShortcut": {"message": "Autofill keyboard shortcut"}, "autofillLoginShortcutNotSet": {"message": "The autofill login shortcut is not set. Change this in the browser's settings."}, "autofillLoginShortcutText": {"message": "The autofill login shortcut is $COMMAND$. Manage all shortcuts in the browser's settings.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillShortcutTextSafari": {"message": "Default autofill shortcut: $COMMAND$.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "opensInANewWindow": {"message": "Opens in a new window"}, "rememberThisDeviceToMakeFutureLoginsSeamless": {"message": "Remember this device to make future logins seamless"}, "deviceApprovalRequired": {"message": "Įrenginio patvir<PERSON><PERSON><PERSON> re<PERSON>. <PERSON><PERSON><PERSON> pat<PERSON><PERSON><PERSON> būd<PERSON> to<PERSON>:"}, "deviceApprovalRequiredV2": {"message": "Device approval required"}, "selectAnApprovalOptionBelow": {"message": "Select an approval option below"}, "rememberThisDevice": {"message": "Prisiminti šį įrenginį"}, "uncheckIfPublicDevice": {"message": "Panaikink <PERSON>, jei na<PERSON><PERSON><PERSON> v<PERSON> įrenginys"}, "approveFromYourOtherDevice": {"message": "<PERSON><PERSON><PERSON><PERSON> iš tavo kito įrenginio"}, "requestAdminApproval": {"message": "Prašyti administrator<PERSON>us pat<PERSON>"}, "ssoIdentifierRequired": {"message": "Organizacijos SSO identifikatorius yra re<PERSON>."}, "creatingAccountOn": {"message": "Creating account on"}, "checkYourEmail": {"message": "Check your email"}, "followTheLinkInTheEmailSentTo": {"message": "Follow the link in the email sent to"}, "andContinueCreatingYourAccount": {"message": "and continue creating your account."}, "noEmail": {"message": "No email?"}, "goBack": {"message": "Go back"}, "toEditYourEmailAddress": {"message": "to edit your email address."}, "eu": {"message": "ES", "description": "European Union"}, "accessDenied": {"message": "Prieiga uždrausta. Neturi te<PERSON> šį puslapį."}, "general": {"message": "Bendra"}, "display": {"message": "<PERSON><PERSON><PERSON>"}, "accountSuccessfullyCreated": {"message": "Paskyra sėkmingai sukurta!"}, "adminApprovalRequested": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> pat<PERSON>"}, "adminApprovalRequestSentToAdmins": {"message": "Tavo prašymas išsiųstas administratoriui."}, "troubleLoggingIn": {"message": "Problemos prisijungiant?"}, "loginApproved": {"message": "Patvirtintas prisijungi<PERSON>"}, "userEmailMissing": {"message": "Trūksta naudotojo el. paš<PERSON>"}, "activeUserEmailNotFoundLoggingYouOut": {"message": "Active user email not found. Logging you out."}, "deviceTrusted": {"message": "Pat<PERSON><PERSON> įrenginys"}, "trustOrganization": {"message": "Trust organization"}, "trust": {"message": "Trust"}, "doNotTrust": {"message": "Do not trust"}, "organizationNotTrusted": {"message": "Organization is not trusted"}, "emergencyAccessTrustWarning": {"message": "For the security of your account, only confirm if you have granted emergency access to this user and their fingerprint matches what is displayed in their account"}, "orgTrustWarning": {"message": "For the security of your account, only proceed if you are a member of this organization, have account recovery enabled, and the fingerprint displayed below matches the organization's fingerprint."}, "orgTrustWarning1": {"message": "This organization has an Enterprise policy that will enroll you in account recovery. Enrollment will allow organization administrators to change your password. Only proceed if you recognize this organization and the fingerprint phrase displayed below matches the organization's fingerprint."}, "trustUser": {"message": "Trust user"}, "sendsTitleNoItems": {"message": "Send sensitive information safely", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendsBodyNoItems": {"message": "Share files and data securely with anyone, on any platform. Your information will remain end-to-end encrypted while limiting exposure.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "inputRequired": {"message": "Input is required."}, "required": {"message": "required"}, "search": {"message": "Search"}, "inputMinLength": {"message": "Input must be at least $COUNT$ characters long.", "placeholders": {"count": {"content": "$1", "example": "8"}}}, "inputMaxLength": {"message": "Input must not exceed $COUNT$ characters in length.", "placeholders": {"count": {"content": "$1", "example": "20"}}}, "inputForbiddenCharacters": {"message": "The following characters are not allowed: $CHARACTERS$", "placeholders": {"characters": {"content": "$1", "example": "@, #, $, %"}}}, "inputMinValue": {"message": "Input value must be at least $MIN$.", "placeholders": {"min": {"content": "$1", "example": "8"}}}, "inputMaxValue": {"message": "Input value must not exceed $MAX$.", "placeholders": {"max": {"content": "$1", "example": "100"}}}, "multipleInputEmails": {"message": "1 or more emails are invalid"}, "inputTrimValidator": {"message": "Input must not contain only whitespace.", "description": "Notification to inform the user that a form's input can't contain only whitespace."}, "inputEmail": {"message": "Input is not an email address."}, "fieldsNeedAttention": {"message": "$COUNT$ field(s) above need your attention.", "placeholders": {"count": {"content": "$1", "example": "4"}}}, "singleFieldNeedsAttention": {"message": "1 field needs your attention."}, "multipleFieldsNeedAttention": {"message": "$COUNT$ fields need your attention.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "selectPlaceholder": {"message": "-- Select --"}, "multiSelectPlaceholder": {"message": "-- Type to filter --"}, "multiSelectLoading": {"message": "Retrieving options..."}, "multiSelectNotFound": {"message": "Elementai nerasti"}, "multiSelectClearAll": {"message": "Išvalyti viską"}, "plusNMore": {"message": "+$QUANTITY$ daugiau", "placeholders": {"quantity": {"content": "$1", "example": "5"}}}, "submenu": {"message": "Submeniu"}, "toggleCollapse": {"message": "<PERSON><PERSON><PERSON><PERSON> sutrumpini<PERSON>", "description": "Toggling an expand/collapse state."}, "aliasDomain": {"message": "<PERSON><PERSON>"}, "passwordRepromptDisabledAutofillOnPageLoad": {"message": "Elementai su pagrindinio slaptažodžio raginimu negali būti automatiškai užpildyti įkeliant puslapį. Automatinis pildymas, įkeliant puslapį, i<PERSON><PERSON><PERSON><PERSON>.", "description": "Toast message for describing that master password re-prompt cannot be autofilled on page load."}, "autofillOnPageLoadSetToDefault": {"message": "Automatinis pildymas įkeliant puslapį nustatytas naudoti numatytąjį nustatymą.", "description": "Toast message for informing the user that autofill on page load has been set to the default setting."}, "turnOffMasterPasswordPromptToEditField": {"message": "Išjunkite pagrindinio slaptažodž<PERSON>, jei norite redaguoti šį lauką", "description": "Message appearing below the autofill on load message when master password reprompt is set for a vault item."}, "toggleSideNavigation": {"message": "Perjungti šoninę naršymą"}, "skipToContent": {"message": "Pereiti prie turinio"}, "bitwardenOverlayButton": {"message": "„Bitwarden“ automatinio pildymo meniu mygtukas", "description": "Page title for the iframe containing the overlay button"}, "toggleBitwardenVaultOverlay": {"message": "<PERSON><PERSON><PERSON><PERSON> „Bitwarden“ automatinio pildymo meniu", "description": "Screen reader and tool tip label for the overlay button"}, "bitwardenVault": {"message": "„Bitwarden“ automatinio pildymo meniu", "description": "Page title in overlay"}, "unlockYourAccountToViewMatchingLogins": {"message": "Atrakinkite savo paskyrą, kad pamatytumėte atitinkamus prisijungimus", "description": "Text to display in overlay when the account is locked."}, "unlockYourAccountToViewAutofillSuggestions": {"message": "Unlock your account to view autofill suggestions", "description": "Text to display in overlay when the account is locked."}, "unlockAccount": {"message": "Atrakinti paskyrą", "description": "Button text to display in overlay when the account is locked."}, "unlockAccountAria": {"message": "Atrakinti savo paskyrą, atidaromas naujame lange", "description": "Screen reader text (aria-label) for unlock account button in overlay"}, "totpCodeAria": {"message": "Time-based One-Time Password Verification Code", "description": "Aria label for the totp code displayed in the inline menu for autofill"}, "totpSecondsSpanAria": {"message": "Time remaining before current TOTP expires", "description": "Aria label for the totp seconds displayed in the inline menu for autofill"}, "fillCredentialsFor": {"message": "Užpildykite prisijungimo duomenis", "description": "Screen reader text for when overlay item is in focused"}, "partialUsername": {"message": "<PERSON><PERSON><PERSON> var<PERSON>", "description": "Screen reader text for when a login item is focused where a partial username is displayed. SR will announce this phrase before reading the text of the partial username"}, "noItemsToShow": {"message": "Nėra elementų, kuriuos būtų galima parodyti", "description": "Text to show in overlay if there are no matching items"}, "newItem": {"message": "<PERSON><PERSON><PERSON>as", "description": "Button text to display in overlay when there are no matching items"}, "addNewVaultItem": {"message": "Pridėti naują sa<PERSON>", "description": "Screen reader text (aria-label) for new item button in overlay"}, "newLogin": {"message": "New login", "description": "Button text to display within inline menu when there are no matching items on a login field"}, "addNewLoginItemAria": {"message": "Pridėti naują saugyklos prisijungimo elementą, atidaromas naujame lange", "description": "Screen reader text (aria-label) for new login button within inline menu"}, "newCard": {"message": "New card", "description": "Button text to display within inline menu when there are no matching items on a credit card field"}, "addNewCardItemAria": {"message": "Pridėti naują saugyk<PERSON> kortel<PERSON> element<PERSON>, atidaromas naujame lange", "description": "Screen reader text (aria-label) for new card button within inline menu"}, "newIdentity": {"message": "<PERSON><PERSON><PERSON>", "description": "Button text to display within inline menu when there are no matching items on an identity field"}, "addNewIdentityItemAria": {"message": "Pridėti naują saugyklos tapatybės elementą, atidaromas naujame lange", "description": "Screen reader text (aria-label) for new identity button within inline menu"}, "bitwardenOverlayMenuAvailable": {"message": "<PERSON><PERSON><PERSON> „Bitwarden“ automatinio pildymo meniu. Norėdami pasirinkti, paspauskite rodyklės žemyn klaviš<PERSON>.", "description": "Screen reader text for announcing when the overlay opens on the page"}, "turnOn": {"message": "Įjungti"}, "ignore": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "importData": {"message": "Importuoti duomenis", "description": "Used for the header of the import dialog, the import button and within the file-password-prompt"}, "importError": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "importErrorDesc": {"message": "<PERSON><PERSON> problema su duomenimis, k<PERSON><PERSON><PERSON> bandėte importuoti. Praš<PERSON> pat<PERSON> k<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, es<PERSON><PERSON><PERSON> pradiniame faile ir pabandyti iš naujo."}, "resolveTheErrorsBelowAndTryAgain": {"message": "Ištaisykite klaidas ir pabandykite iš naujo."}, "description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "importSuccess": {"message": "<PERSON><PERSON><PERSON>"}, "importSuccessNumberOfItems": {"message": "Iš viso importuoti $AMOUNT$ elementai.", "placeholders": {"amount": {"content": "$1", "example": "2"}}}, "tryAgain": {"message": "Bandyti dar kartą"}, "verificationRequiredForActionSetPinToContinue": {"message": "<PERSON><PERSON> ve<PERSON> re<PERSON> patik<PERSON>. Norėdamas (-a) tęsti, nustatyk PIN."}, "setPin": {"message": "Nustatyti PIN"}, "verifyWithBiometrics": {"message": "Patikrininti su biometrinius duomenis"}, "awaitingConfirmation": {"message": "<PERSON><PERSON><PERSON>"}, "couldNotCompleteBiometrics": {"message": "Nepavyko atlikti biometrinių duomenų."}, "needADifferentMethod": {"message": "<PERSON><PERSON>a kito metodo?"}, "useMasterPassword": {"message": "<PERSON><PERSON><PERSON> pagrindinį slaptažodį"}, "usePin": {"message": "<PERSON><PERSON>ti P<PERSON>"}, "useBiometrics": {"message": "Na<PERSON>ti biometrinius duomenis"}, "enterVerificationCodeSentToEmail": {"message": "Įvesk į el. paštą atsiųstą patikrinimo kodą."}, "resendCode": {"message": "Siųsti kodą dar kartą"}, "total": {"message": "<PERSON><PERSON> viso"}, "importWarning": {"message": "Jūs importuojate duomenis į $ORGANIZATION$. Jūsų duomenimis gali būti pasidalinta tarp šios organizacijos narių. Ar norite tęsti?", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "duoHealthCheckResultsInNullAuthUrlError": {"message": "<PERSON><PERSON><PERSON> prijungiant su „Duo“ paslauga. Naudokite kitą dvigubo prisijungimo būdą arba susisiekite su „Duo“ dėl pagalbos."}, "duoRequiredForAccount": {"message": "Tavo paskyrai reikalingas Duo dviejų veiksmų prisijungimas."}, "popoutExtension": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>"}, "launchDuo": {"message": "Paleisti DUO"}, "importFormatError": {"message": "Duomenys neteisingai suformatuoti. Prašome patikrinti savo importuojamą failą ir pabandyti iš naujo."}, "importNothingError": {"message": "<PERSON><PERSON><PERSON> nebuvo importuota."}, "importEncKeyError": {"message": "<PERSON><PERSON><PERSON>ruojant eksportuotą failą. Jūsų šifravimo raktas nesutampa su šifravimo raktu, naudotu eksportuoti duomenis."}, "invalidFilePassword": {"message": "Netinkamas failo <PERSON>, pra<PERSON><PERSON> naudoti slaptažodį, kurį įvedėte kurdami eksportuojamą failą."}, "destination": {"message": "Destination"}, "learnAboutImportOptions": {"message": "<PERSON><PERSON><PERSON>ti apie <PERSON>"}, "selectImportFolder": {"message": "Pasirinkti aplankalą"}, "selectImportCollection": {"message": "Pa<PERSON>ink<PERSON> rinkinį"}, "importTargetHint": {"message": "Pasirinkite šį p<PERSON><PERSON><PERSON>, jei norite, jog <PERSON><PERSON><PERSON>, bū<PERSON><PERSON> perkeltas į $DESTINATION$", "description": "Located as a hint under the import target. Will be appended by either folder or collection, depending if the user is importing into an individual or an organizational vault.", "placeholders": {"destination": {"content": "$1", "example": "folder or collection"}}}, "importUnassignedItemsError": {"message": "Faile yra nepriskirtų elementų."}, "selectFormat": {"message": "Pasirinkti importuojamo failo formatą"}, "selectImportFile": {"message": "Pasirinkti importuojamą failą"}, "chooseFile": {"message": "Pasirink<PERSON> failą"}, "noFileChosen": {"message": "Nepasirinktas joks failas"}, "orCopyPasteFileContents": {"message": "arba kopiju<PERSON>te/įklijuokite importuojamo failo turinį"}, "instructionsFor": {"message": "$NAME$ Instrukcijos", "description": "The title for the import tool instructions.", "placeholders": {"name": {"content": "$1", "example": "LastPass (csv)"}}}, "confirmVaultImport": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "confirmVaultImportDesc": {"message": "Failas yra apsaugotas slap<PERSON>ž<PERSON>žiu. <PERSON><PERSON>ti duomen<PERSON>, įveskite failo slaptažodį."}, "confirmFilePassword": {"message": "<PERSON><PERSON><PERSON><PERSON> failo slaptažodį"}, "exportSuccess": {"message": "Eksportuoti saugyklos duomenys"}, "typePasskey": {"message": "Prieigos raktas"}, "accessing": {"message": "Accessing"}, "loggedInExclamation": {"message": "Logged in!"}, "passkeyNotCopied": {"message": "Passkey will not be copied"}, "passkeyNotCopiedAlert": {"message": "The passkey will not be copied to the cloned item. Do you want to continue cloning this item?"}, "passkeyFeatureIsNotImplementedForAccountsWithoutMasterPassword": {"message": "Verification required by the initiating site. This feature is not yet implemented for accounts without master password."}, "logInWithPasskeyQuestion": {"message": "Log in with passkey?"}, "passkeyAlreadyExists": {"message": "A passkey already exists for this application."}, "noPasskeysFoundForThisApplication": {"message": "No passkeys found for this application."}, "noMatchingPasskeyLogin": {"message": "You do not have a matching login for this site."}, "noMatchingLoginsForSite": {"message": "No matching logins for this site"}, "searchSavePasskeyNewLogin": {"message": "Search or save passkey as new login"}, "confirm": {"message": "Confirm"}, "savePasskey": {"message": "Save passkey"}, "savePasskeyNewLogin": {"message": "Save passkey as new login"}, "chooseCipherForPasskeySave": {"message": "Choose a login to save this passkey to"}, "chooseCipherForPasskeyAuth": {"message": "Choose a passkey to log in with"}, "passkeyItem": {"message": "<PERSON><PERSON> Item"}, "overwritePasskey": {"message": "Overwrite passkey?"}, "overwritePasskeyAlert": {"message": "This item already contains a passkey. Are you sure you want to overwrite the current passkey?"}, "featureNotSupported": {"message": "Feature not yet supported"}, "yourPasskeyIsLocked": {"message": "Authentication required to use passkey. Verify your identity to continue."}, "multifactorAuthenticationCancelled": {"message": "Multifactor authentication cancelled"}, "noLastPassDataFound": {"message": "No LastPass data found"}, "incorrectUsernameOrPassword": {"message": "Incorrect username or password"}, "incorrectPassword": {"message": "<PERSON>ei<PERSON><PERSON>"}, "incorrectCode": {"message": "Neteisingas kodas"}, "incorrectPin": {"message": "Neteisingas PIN"}, "multifactorAuthenticationFailed": {"message": "Multifactor authentication failed"}, "includeSharedFolders": {"message": "Include shared folders"}, "lastPassEmail": {"message": "LastPass Email"}, "importingYourAccount": {"message": "Importing your account..."}, "lastPassMFARequired": {"message": "LastPass multifactor authentication required"}, "lastPassMFADesc": {"message": "Enter your one-time passcode from your authentication app"}, "lastPassOOBDesc": {"message": "Approve the login request in your authentication app or enter a one-time passcode."}, "passcode": {"message": "Passcode"}, "lastPassMasterPassword": {"message": "LastPass master password"}, "lastPassAuthRequired": {"message": "LastPass authentication required"}, "awaitingSSO": {"message": "Awaiting SSO authentication"}, "awaitingSSODesc": {"message": "Please continue to log in using your company credentials."}, "seeDetailedInstructions": {"message": "See detailed instructions on our help site at", "description": "This is followed a by a hyperlink to the help website."}, "importDirectlyFromLastPass": {"message": "Import directly from LastPass"}, "importFromCSV": {"message": "Import from CSV"}, "lastPassTryAgainCheckEmail": {"message": "Try again or look for an email from LastPass to verify it's you."}, "collection": {"message": "Collection"}, "lastPassYubikeyDesc": {"message": "Su LastPass paskyra susietą YubiKey įkišk į kompiuterio USB jungtį, tada paliesk jo mygtuką."}, "switchAccount": {"message": "Switch account"}, "switchAccounts": {"message": "Switch accounts"}, "switchToAccount": {"message": "Switch to account"}, "activeAccount": {"message": "Active account"}, "bitwardenAccount": {"message": "Bitwarden account"}, "availableAccounts": {"message": "Pasiekiamos paskyros"}, "accountLimitReached": {"message": "Account limit reached. Log out of an account to add another."}, "active": {"message": "active"}, "locked": {"message": "locked"}, "unlocked": {"message": "unlocked"}, "server": {"message": "server"}, "hostedAt": {"message": "hosted at"}, "useDeviceOrHardwareKey": {"message": "Use your device or hardware key"}, "justOnce": {"message": "Tik kartą"}, "alwaysForThisSite": {"message": "Visada šiam s<PERSON>"}, "domainAddedToExcludedDomains": {"message": "$DOMAIN$ pridėta prie neįtraukiamų domenų.", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "commonImportFormats": {"message": "Common formats", "description": "Label indicating the most common import formats"}, "confirmContinueToBrowserSettingsTitle": {"message": "Continue to browser settings?", "description": "Title for dialog which asks if the user wants to proceed to a relevant browser settings page"}, "confirmContinueToHelpCenter": {"message": "Continue to Help Center?", "description": "Title for dialog which asks if the user wants to proceed to a relevant Help Center page"}, "confirmContinueToHelpCenterPasswordManagementContent": {"message": "Change your browser's autofill and password management settings.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser password management settings"}, "confirmContinueToHelpCenterKeyboardShortcutsContent": {"message": "You can view and set extension shortcuts in your browser's settings.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser keyboard shortcut settings"}, "confirmContinueToBrowserPasswordManagementSettingsContent": {"message": "Change your browser's autofill and password management settings.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's password management settings page"}, "confirmContinueToBrowserKeyboardShortcutSettingsContent": {"message": "You can view and set extension shortcuts in your browser's settings.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's keyboard shortcut settings page"}, "overrideDefaultBrowserAutofillTitle": {"message": "Padaryti Bitwarden savo numatytuoju slaptažodžių tvarkytuvu?", "description": "Dialog title facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutofillDescription": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ši<PERSON> parinktį, gali kilti konfliktų tarp Bitwarden automatinio užpildymo meniu ir narš<PERSON>.", "description": "Dialog message facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutoFillSettings": {"message": "Padaryti Bitwarden savo numatytuoju slaptažodžių tvarkytuvu", "description": "Label for the setting that allows overriding the default browser autofill settings"}, "privacyPermissionAdditionNotGrantedTitle": {"message": "Nepavyksta nustatyti Bitwarden kaip numatytosios <PERSON>žių tvarkyklės", "description": "Title for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "privacyPermissionAdditionNotGrantedDescription": {"message": "Norėdamas (-a) nustatyti Bitwarden kaip numatytąj<PERSON> slaptažodžių tvarkyklę, turi suteikti Bitwarden naršyklės privatumo leidimus.", "description": "Description for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "makeDefault": {"message": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>", "description": "Button text for the setting that allows overriding the default browser autofill settings"}, "saveCipherAttemptSuccess": {"message": "Kredencialai išsaugoti sėkmingai.", "description": "Notification message for when saving credentials has succeeded."}, "passwordSaved": {"message": "Password saved!", "description": "Notification message for when saving credentials has succeeded."}, "updateCipherAttemptSuccess": {"message": "Kredencialai atnaujinti s<PERSON>.", "description": "Notification message for when updating credentials has succeeded."}, "passwordUpdated": {"message": "Password updated!", "description": "Notification message for when updating credentials has succeeded."}, "saveCipherAttemptFailed": {"message": "<PERSON><PERSON><PERSON><PERSON> kreden<PERSON>. Išsamesnės informacijos patikrink konsolėje.", "description": "Notification message for when saving credentials has failed."}, "success": {"message": "Sėkmė"}, "removePasskey": {"message": "<PERSON><PERSON><PERSON><PERSON> slaptaraktį"}, "passkeyRemoved": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "autofillSuggestions": {"message": "Autofill suggestions"}, "itemSuggestions": {"message": "Suggested items"}, "autofillSuggestionsTip": {"message": "Save a login item for this site to autofill"}, "yourVaultIsEmpty": {"message": "Your vault is empty"}, "noItemsMatchSearch": {"message": "No items match your search"}, "clearFiltersOrTryAnother": {"message": "Clear filters or try another search term"}, "copyInfoTitle": {"message": "Copy info - $ITEMNAME$", "description": "Title for a button that opens a menu with options to copy information from an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "copyNoteTitle": {"message": "Copy Note - $ITEMNAME$", "description": "Title for a button copies a note to the clipboard.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Note Item"}}}, "moreOptionsLabel": {"message": "More options, $ITEMNAME$", "description": "Aria label for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "moreOptionsTitle": {"message": "More options - $ITEMNAME$", "description": "Title for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitle": {"message": "View item - $ITEMNAME$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitleWithField": {"message": "View item - $ITEMNAME$ - $FIELD$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "autofillTitle": {"message": "Autofill - $ITEMNAME$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "autofillTitleWithField": {"message": "Autofill - $ITEMNAME$ - $FIELD$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "copyFieldValue": {"message": "Copy $FIELD$, $VALUE$", "description": "Title for a button that copies a field value to the clipboard.", "placeholders": {"field": {"content": "$1", "example": "Username"}, "value": {"content": "$2", "example": "Foo"}}}, "noValuesToCopy": {"message": "No values to copy"}, "assignToCollections": {"message": "Priskirti į kolekcijas"}, "copyEmail": {"message": "Copy email"}, "copyPhone": {"message": "Copy phone"}, "copyAddress": {"message": "Copy address"}, "adminConsole": {"message": "<PERSON><PERSON><PERSON>"}, "accountSecurity": {"message": "<PERSON><PERSON><PERSON>"}, "notifications": {"message": "Pranešimai"}, "appearance": {"message": "Išvaizda"}, "errorAssigningTargetCollection": {"message": "<PERSON>laida pris<PERSON>riant tikslinę kolekciją."}, "errorAssigningTargetFolder": {"message": "<PERSON><PERSON><PERSON> p<PERSON>t tikslinį aplanką."}, "viewItemsIn": {"message": "View items in $NAME$", "description": "Button to view the contents of a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "backTo": {"message": "Back to $NAME$", "description": "Navigate back to a previous folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "new": {"message": "<PERSON><PERSON><PERSON>"}, "removeItem": {"message": "Remove $NAME$", "description": "Remove a selected option, such as a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "itemsWithNoFolder": {"message": "Items with no folder"}, "itemDetails": {"message": "Elemento informacija"}, "itemName": {"message": "Elemento pavadinimas"}, "organizationIsDeactivated": {"message": "Organization is deactivated"}, "owner": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "selfOwnershipLabel": {"message": "<PERSON><PERSON><PERSON>", "description": "Used as a label to indicate that the user is the owner of an item."}, "contactYourOrgAdmin": {"message": "Items in deactivated organizations cannot be accessed. Contact your organization owner for assistance."}, "additionalInformation": {"message": "Papildoma informacija"}, "itemHistory": {"message": "Elemento istorija"}, "lastEdited": {"message": "Paskutinį kartą redaguota"}, "ownerYou": {"message": "Savininkas: <PERSON><PERSON><PERSON>"}, "linked": {"message": "<PERSON><PERSON>"}, "copySuccessful": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "upload": {"message": "Įkelti"}, "addAttachment": {"message": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>"}, "maxFileSizeSansPunctuation": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> failo dyd<PERSON> – 500 MB"}, "deleteAttachmentName": {"message": "Ištrinti priedą $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadAttachmentName": {"message": "Atsisiųsti $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadBitwarden": {"message": "Download Bitwarden"}, "downloadBitwardenOnAllDevices": {"message": "Download Bitwarden on all devices"}, "getTheMobileApp": {"message": "Get the mobile app"}, "getTheMobileAppDesc": {"message": "Access your passwords on the go with the Bitwarden mobile app."}, "getTheDesktopApp": {"message": "Get the desktop app"}, "getTheDesktopAppDesc": {"message": "Access your vault without a browser, then set up unlock with biometrics to expedite unlocking in both the desktop app and browser extension."}, "downloadFromBitwardenNow": {"message": "Download from bitwarden.com now"}, "getItOnGooglePlay": {"message": "Get it on Google Play"}, "downloadOnTheAppStore": {"message": "Download on the App Store"}, "permanentlyDeleteAttachmentConfirmation": {"message": "Ar tikrai norite negrįžtamai ištrinti šį priedą?"}, "premium": {"message": "„Premium“"}, "freeOrgsCannotUseAttachments": {"message": "Nemokamos organizacijos negali naudoti priedų"}, "filters": {"message": "Filtrai"}, "filterVault": {"message": "Filter vault"}, "filterApplied": {"message": "One filter applied"}, "filterAppliedPlural": {"message": "$COUNT$ filters applied", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "personalDetails": {"message": "Asmeniniai duomenys"}, "identification": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "contactInfo": {"message": "Kontaktinė informacija"}, "downloadAttachment": {"message": "Atsisiųsti – $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Your File"}}}, "cardNumberEndsWith": {"message": "k<PERSON><PERSON><PERSON><PERSON> numeris baigiasi su", "description": "Used within the inline menu to provide an aria description when users are attempting to fill a card cipher."}, "loginCredentials": {"message": "Prisijungi<PERSON> k<PERSON>"}, "authenticatorKey": {"message": "Autentifikat<PERSON><PERSON> r<PERSON>"}, "autofillOptions": {"message": "Autofill options"}, "websiteUri": {"message": "Website (URI)"}, "websiteUriCount": {"message": "Website (URI) $COUNT$", "description": "Label for an input field that contains a website URI. The input field is part of a list of fields, and the count indicates the position of the field in the list.", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "websiteAdded": {"message": "Website added"}, "addWebsite": {"message": "Add website"}, "deleteWebsite": {"message": "Delete website"}, "defaultLabel": {"message": "Default ($VALUE$)", "description": "A label that indicates the default value for a field with the current default value in parentheses.", "placeholders": {"value": {"content": "$1", "example": "Base domain"}}}, "showMatchDetection": {"message": "Show match detection $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "hideMatchDetection": {"message": "Hide match detection $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "autoFillOnPageLoad": {"message": "Autofill on page load?"}, "cardExpiredTitle": {"message": "Expired card"}, "cardExpiredMessage": {"message": "If you've renewed it, update the card's information"}, "cardDetails": {"message": "Kortelės duomenys"}, "cardBrandDetails": {"message": "„$BRAND$“ duomenys", "placeholders": {"brand": {"content": "$1", "example": "Visa"}}}, "enableAnimations": {"message": "Enable animations"}, "showAnimations": {"message": "Show animations"}, "addAccount": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "loading": {"message": "Įkeliama"}, "data": {"message": "<PERSON><PERSON><PERSON>"}, "passkeys": {"message": "Passkeys", "description": "A section header for a list of passkeys."}, "passwords": {"message": "Passwords", "description": "A section header for a list of passwords."}, "logInWithPasskeyAriaLabel": {"message": "Log in with passkey", "description": "ARIA label for the inline menu button that logs in with a passkey."}, "assign": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "bulkCollectionAssignmentDialogDescriptionSingular": {"message": "Only organization members with access to these collections will be able to see the item."}, "bulkCollectionAssignmentDialogDescriptionPlural": {"message": "Only organization members with access to these collections will be able to see the items."}, "bulkCollectionAssignmentWarning": {"message": "You have selected $TOTAL_COUNT$ items. You cannot update $READONLY_COUNT$ of the items because you do not have edit permissions.", "placeholders": {"total_count": {"content": "$1", "example": "10"}, "readonly_count": {"content": "$2"}}}, "addField": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "add": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "fieldType": {"message": "<PERSON><PERSON>"}, "fieldLabel": {"message": "<PERSON><PERSON>"}, "textHelpText": {"message": "Naudokite teks<PERSON>ius laukus duo<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, saug<PERSON><PERSON> klaus<PERSON>"}, "hiddenHelpText": {"message": "Naudokite paslėptus laukus slaptiems duomenims, pav<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s"}, "checkBoxHelpText": {"message": "Naudokite žym<PERSON><PERSON><PERSON>, jei norite automatiškai užpildyti formos žymimąjį langelį, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, prisim<PERSON>i el. paštą"}, "linkedHelpText": {"message": "Use a linked field when you are experiencing autofill issues for a specific website."}, "linkedLabelHelpText": {"message": "Enter the the field's html id, name, aria-label, or placeholder."}, "editField": {"message": "Redaguot<PERSON> la<PERSON>"}, "editFieldLabel": {"message": "Redaguoti $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "deleteCustomField": {"message": "Ištrinti $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "fieldAdded": {"message": "Pridėtas $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderToggleButton": {"message": "Reorder $LABEL$. Use arrow key to move item up or down.", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderWebsiteUriButton": {"message": "Reorder website URI. Use arrow key to move item up or down."}, "reorderFieldUp": {"message": "$LABEL$ moved up, position $INDEX$ of $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "selectCollectionsToAssign": {"message": "Select collections to assign"}, "personalItemTransferWarningSingular": {"message": "1 item will be permanently transferred to the selected organization. You will no longer own this item."}, "personalItemsTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ items will be permanently transferred to the selected organization. You will no longer own these items.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}}}, "personalItemWithOrgTransferWarningSingular": {"message": "1 item will be permanently transferred to $ORG$. You will no longer own this item.", "placeholders": {"org": {"content": "$1", "example": "Organization name"}}}, "personalItemsWithOrgTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ items will be permanently transferred to $ORG$. You will no longer own these items.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}, "org": {"content": "$2", "example": "Organization name"}}}, "successfullyAssignedCollections": {"message": "Successfully assigned collections"}, "nothingSelected": {"message": "You have not selected anything."}, "itemsMovedToOrg": {"message": "Items moved to $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "itemMovedToOrg": {"message": "Item moved to $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "reorderFieldDown": {"message": "$LABEL$ moved down, position $INDEX$ of $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "itemLocation": {"message": "Elemento vieta"}, "fileSend": {"message": "File Send"}, "fileSends": {"message": "File Sends"}, "textSend": {"message": "Text Send"}, "textSends": {"message": "Text Sends"}, "accountActions": {"message": "Account actions"}, "showNumberOfAutofillSuggestions": {"message": "Show number of login autofill suggestions on extension icon"}, "showQuickCopyActions": {"message": "Show quick copy actions on Vault"}, "systemDefault": {"message": "System default"}, "enterprisePolicyRequirementsApplied": {"message": "Enterprise policy requirements have been applied to this setting"}, "sshPrivateKey": {"message": "Private key"}, "sshPublicKey": {"message": "Public key"}, "sshFingerprint": {"message": "Fingerprint"}, "sshKeyAlgorithm": {"message": "Key type"}, "sshKeyAlgorithmED25519": {"message": "ED25519"}, "sshKeyAlgorithmRSA2048": {"message": "RSA 2048-Bit"}, "sshKeyAlgorithmRSA3072": {"message": "RSA 3072-Bit"}, "sshKeyAlgorithmRSA4096": {"message": "RSA 4096-Bit"}, "retry": {"message": "Retry"}, "vaultCustomTimeoutMinimum": {"message": "Minimum custom timeout is 1 minute."}, "additionalContentAvailable": {"message": "Additional content is available"}, "fileSavedToDevice": {"message": "File saved to device. Manage from your device downloads."}, "showCharacterCount": {"message": "Show character count"}, "hideCharacterCount": {"message": "Hide character count"}, "itemsInTrash": {"message": "Items in trash"}, "noItemsInTrash": {"message": "No items in trash"}, "noItemsInTrashDesc": {"message": "Items you delete will appear here and be permanently deleted after 30 days"}, "trashWarning": {"message": "Items that have been in trash more than 30 days will automatically be deleted"}, "restore": {"message": "Rest<PERSON>"}, "deleteForever": {"message": "Delete forever"}, "noEditPermissions": {"message": "You don't have permission to edit this item"}, "biometricsStatusHelptextUnlockNeeded": {"message": "Biometric unlock is unavailable because PIN or password unlock is required first."}, "biometricsStatusHelptextHardwareUnavailable": {"message": "Biometric unlock is currently unavailable."}, "biometricsStatusHelptextAutoSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextManualSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextDesktopDisconnected": {"message": "Biometric unlock is unavailable because the Bitwarden desktop app is closed."}, "biometricsStatusHelptextNotEnabledInDesktop": {"message": "Biometric unlock is unavailable because it is not enabled for $EMAIL$ in the Bitwarden desktop app.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "biometricsStatusHelptextUnavailableReasonUnknown": {"message": "Biometric unlock is currently unavailable for an unknown reason."}, "unlockVault": {"message": "Unlock your vault in seconds"}, "unlockVaultDesc": {"message": "You can customize your unlock and timeout settings to more quickly access your vault."}, "unlockPinSet": {"message": "Unlock PIN set"}, "authenticating": {"message": "Authenticating"}, "fillGeneratedPassword": {"message": "Fill generated password", "description": "Heading for the password generator within the inline menu"}, "passwordRegenerated": {"message": "Password regenerated", "description": "Notification message for when a password has been regenerated"}, "saveToBitwarden": {"message": "Save to Bitwarden", "description": "Confirmation message for saving a login to Bitwarden"}, "spaceCharacterDescriptor": {"message": "Space", "description": "Represents the space key in screen reader content as a readable word"}, "tildeCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ~ key in screen reader content as a readable word"}, "backtickCharacterDescriptor": {"message": "Backtick", "description": "Represents the ` key in screen reader content as a readable word"}, "exclamationCharacterDescriptor": {"message": "Exclamation mark", "description": "Represents the ! key in screen reader content as a readable word"}, "atSignCharacterDescriptor": {"message": "At sign", "description": "Represents the @ key in screen reader content as a readable word"}, "hashSignCharacterDescriptor": {"message": "Hash sign", "description": "Represents the # key in screen reader content as a readable word"}, "dollarSignCharacterDescriptor": {"message": "Dollar sign", "description": "Represents the $ key in screen reader content as a readable word"}, "percentSignCharacterDescriptor": {"message": "Percent sign", "description": "Represents the % key in screen reader content as a readable word"}, "caretCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ^ key in screen reader content as a readable word"}, "ampersandCharacterDescriptor": {"message": "Ampersand", "description": "Represents the & key in screen reader content as a readable word"}, "asteriskCharacterDescriptor": {"message": "Asterisk", "description": "Represents the * key in screen reader content as a readable word"}, "parenLeftCharacterDescriptor": {"message": "Left parenthesis", "description": "Represents the ( key in screen reader content as a readable word"}, "parenRightCharacterDescriptor": {"message": "Right parenthesis", "description": "Represents the ) key in screen reader content as a readable word"}, "hyphenCharacterDescriptor": {"message": "Underscore", "description": "Represents the _ key in screen reader content as a readable word"}, "underscoreCharacterDescriptor": {"message": "Hyphen", "description": "Represents the - key in screen reader content as a readable word"}, "plusCharacterDescriptor": {"message": "Plus", "description": "Represents the + key in screen reader content as a readable word"}, "equalsCharacterDescriptor": {"message": "Equals", "description": "Represents the = key in screen reader content as a readable word"}, "braceLeftCharacterDescriptor": {"message": "Left brace", "description": "Represents the { key in screen reader content as a readable word"}, "braceRightCharacterDescriptor": {"message": "Right brace", "description": "Represents the } key in screen reader content as a readable word"}, "bracketLeftCharacterDescriptor": {"message": "Left bracket", "description": "Represents the [ key in screen reader content as a readable word"}, "bracketRightCharacterDescriptor": {"message": "Right bracket", "description": "Represents the ] key in screen reader content as a readable word"}, "pipeCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the | key in screen reader content as a readable word"}, "backSlashCharacterDescriptor": {"message": "Back slash", "description": "Represents the back slash key in screen reader content as a readable word"}, "colonCharacterDescriptor": {"message": "Colon", "description": "Represents the : key in screen reader content as a readable word"}, "semicolonCharacterDescriptor": {"message": "Semicolon", "description": "Represents the ; key in screen reader content as a readable word"}, "doubleQuoteCharacterDescriptor": {"message": "Double quote", "description": "Represents the double quote key in screen reader content as a readable word"}, "singleQuoteCharacterDescriptor": {"message": "Single quote", "description": "Represents the ' key in screen reader content as a readable word"}, "lessThanCharacterDescriptor": {"message": "Less than", "description": "Represents the < key in screen reader content as a readable word"}, "greaterThanCharacterDescriptor": {"message": "Greater than", "description": "Represents the > key in screen reader content as a readable word"}, "commaCharacterDescriptor": {"message": "Comma", "description": "Represents the , key in screen reader content as a readable word"}, "periodCharacterDescriptor": {"message": "Period", "description": "Represents the . key in screen reader content as a readable word"}, "questionCharacterDescriptor": {"message": "Question mark", "description": "Represents the ? key in screen reader content as a readable word"}, "forwardSlashCharacterDescriptor": {"message": "Forward slash", "description": "Represents the / key in screen reader content as a readable word"}, "lowercaseAriaLabel": {"message": "Lowercase"}, "uppercaseAriaLabel": {"message": "Uppercase"}, "generatedPassword": {"message": "Generated password"}, "compactMode": {"message": "Compact mode"}, "beta": {"message": "Beta"}, "extensionWidth": {"message": "Extension width"}, "wide": {"message": "Wide"}, "extraWide": {"message": "Extra wide"}, "sshKeyWrongPassword": {"message": "The password you entered is incorrect."}, "importSshKey": {"message": "Import"}, "confirmSshKeyPassword": {"message": "Confirm password"}, "enterSshKeyPasswordDesc": {"message": "Enter the password for the SSH key."}, "enterSshKeyPassword": {"message": "Enter password"}, "invalidSshKey": {"message": "The SSH key is invalid"}, "sshKeyTypeUnsupported": {"message": "The SSH key type is not supported"}, "importSshKeyFromClipboard": {"message": "Import key from clipboard"}, "sshKeyImported": {"message": "SSH key imported successfully"}, "cannotRemoveViewOnlyCollections": {"message": "Negalite pašalinti kolekcijų su Peržiūrėti tik leidimus: $COLLECTIONS$", "placeholders": {"collections": {"content": "$1", "example": "Work, Personal"}}}, "updateDesktopAppOrDisableFingerprintDialogTitle": {"message": "Please update your desktop application"}, "updateDesktopAppOrDisableFingerprintDialogMessage": {"message": "To use biometric unlock, please update your desktop application, or disable fingerprint unlock in the desktop settings."}, "changeAtRiskPassword": {"message": "Change at-risk password"}, "settingsVaultOptions": {"message": "Vault options"}, "emptyVaultDescription": {"message": "The vault protects more than just your passwords. Store secure logins, IDs, cards and notes securely here."}, "introCarouselLabel": {"message": "Welcome to Bitwarden"}, "securityPrioritized": {"message": "Security, prioritized"}, "securityPrioritizedBody": {"message": "Save logins, cards, and identities to your secure vault. Bitwarden uses zero-knowledge, end-to-end encryption to protect what’s important to you."}, "quickLogin": {"message": "Quick and easy login"}, "quickLoginBody": {"message": "Set up biometric unlock and autofill to log into your accounts without typing a single letter."}, "secureUser": {"message": "Level up your logins"}, "secureUserBody": {"message": "Use the generator to create and save strong, unique passwords for all your accounts."}, "secureDevices": {"message": "Your data, when and where you need it"}, "secureDevicesBody": {"message": "Save unlimited passwords across unlimited devices with Bitwarden mobile, browser, and desktop apps."}, "nudgeBadgeAria": {"message": "1 notification"}, "emptyVaultNudgeTitle": {"message": "Import existing passwords"}, "emptyVaultNudgeBody": {"message": "Use the importer to quickly transfer logins to Bitwarden without manually adding them."}, "emptyVaultNudgeButton": {"message": "Import now"}, "hasItemsVaultNudgeTitle": {"message": "Welcome to your vault!"}, "hasItemsVaultNudgeBodyOne": {"message": "Autofill items for the current page"}, "hasItemsVaultNudgeBodyTwo": {"message": "Favorite items for easy access"}, "hasItemsVaultNudgeBodyThree": {"message": "Search your vault for something else"}, "newLoginNudgeTitle": {"message": "Save time with autofill"}, "newLoginNudgeBodyOne": {"message": "Include a", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyBold": {"message": "Website", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyTwo": {"message": "so this login appears as an autofill suggestion.", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newCardNudgeTitle": {"message": "Seamless online checkout"}, "newCardNudgeBody": {"message": "With cards, easily autofill payment forms securely and accurately."}, "newIdentityNudgeTitle": {"message": "Simplify creating accounts"}, "newIdentityNudgeBody": {"message": "With identities, quickly autofill long registration or contact forms."}, "newNoteNudgeTitle": {"message": "Keep your sensitive data safe"}, "newNoteNudgeBody": {"message": "With notes, securely store sensitive data like banking or insurance details."}, "newSshNudgeTitle": {"message": "Developer-friendly SSH access"}, "newSshNudgeBodyOne": {"message": "Store your keys and connect with the SSH agent for fast, encrypted authentication.", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "newSshNudgeBodyTwo": {"message": "Learn more about SSH agent", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "generatorNudgeTitle": {"message": "Quickly create passwords"}, "generatorNudgeBodyOne": {"message": "Easily create strong and unique passwords by clicking on", "description": "Two part message", "example": "Easily create strong and unique passwords by clicking on {icon} to help you keep your logins secure."}, "generatorNudgeBodyTwo": {"message": "to help you keep your logins secure.", "description": "Two part message", "example": "Easily create strong and unique passwords by clicking on {icon} to help you keep your logins secure."}, "generatorNudgeBodyAria": {"message": "Easily create strong and unique passwords by clicking on the Generate password button to help you keep your logins secure.", "description": "Aria label for the body content of the generator nudge"}, "noPermissionsViewPage": {"message": "You do not have permissions to view this page. Try logging in with a different account."}}