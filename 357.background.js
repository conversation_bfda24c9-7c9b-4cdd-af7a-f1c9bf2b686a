(()=>{var t,e,i={2763:(t,e,i)=>{"use strict";var r;!function(t){t[t.SignalR=0]="SignalR",t[t.WebPush=1]="WebPush"}(r||(r={}));class n{constructor(t){var e,i;this.featureStates={},this.version=t.version,this.gitHash=t.gitHash,this.server=t.server,this.utcDate=new Date(t.utcDate),this.environment=t.environment,this.featureStates=t.featureStates,this.push=null==t.push?{pushTechnology:r.SignalR}:{pushTechnology:t.push.pushTechnology,vapidPublicKey:t.push.vapidPublicKey},this.settings=t.settings,null==(null===(e=this.server)||void 0===e?void 0:e.name)&&null==(null===(i=this.server)||void 0===i?void 0:i.url)&&(this.server=null)}getAgeInMilliseconds(){var t;return(new Date).getTime()-(null===(t=this.utcDate)||void 0===t?void 0:t.getTime())}isValid(){return this.getAgeInMilliseconds()<=864e5}static fromJSON(t){return null==t?null:new n(t)}}var s=i(9942),o=i(1048),a=i(5118),l=i(8737),u=i(3005);const c="undefined"==typeof self?i(5442):null;class d{static init(){d.inited||(d.inited=!0,d.isNode="undefined"!=typeof process&&null!=process.release&&"node"===process.release.name,d.isBrowser="undefined"!=typeof window,d.isMobileBrowser=d.isBrowser&&this.isMobile(window),d.isAppleMobileBrowser=d.isBrowser&&this.isAppleMobile(window),d.isNode?d.global=i.g:d.isBrowser?d.global=window:d.global=self)}static fromB64ToArray(t){if(null==t)return null;if(d.isNode)return new Uint8Array(Buffer.from(t,"base64"));{const e=d.global.atob(t),i=new Uint8Array(e.length);for(let t=0;t<e.length;t++)i[t]=e.charCodeAt(t);return i}}static fromUrlB64ToArray(t){return d.fromB64ToArray(d.fromUrlB64ToB64(t))}static fromHexToArray(t){if(d.isNode)return new Uint8Array(Buffer.from(t,"hex"));{const e=new Uint8Array(t.length/2);for(let i=0;i<t.length;i+=2)e[i/2]=parseInt(t.substr(i,2),16);return e}}static fromUtf8ToArray(t){if(d.isNode)return new Uint8Array(Buffer.from(t,"utf8"));{const e=unescape(encodeURIComponent(t)),i=new Uint8Array(e.length);for(let t=0;t<e.length;t++)i[t]=e.charCodeAt(t);return i}}static fromByteStringToArray(t){if(null==t)return null;const e=new Uint8Array(t.length);for(let i=0;i<t.length;i++)e[i]=t.charCodeAt(i);return e}static fromBufferToB64(t){if(null==t)return null;if(d.isNode)return Buffer.from(t).toString("base64");{let e="";const i=new Uint8Array(t);for(let t=0;t<i.byteLength;t++)e+=String.fromCharCode(i[t]);return d.global.btoa(e)}}static fromBufferToUrlB64(t){return d.fromB64toUrlB64(d.fromBufferToB64(t))}static fromB64toUrlB64(t){return t.replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}static fromBufferToUtf8(t){return o.hp.from(t).toString("utf8")}static fromBufferToByteString(t){return String.fromCharCode.apply(null,new Uint8Array(t))}static fromBufferToHex(t){if(d.isNode)return Buffer.from(t).toString("hex");{const e=new Uint8Array(t);return Array.prototype.map.call(e,(t=>("00"+t.toString(16)).slice(-2))).join("")}}static hexStringToArrayBuffer(t){if(t.length%2!=0)throw"HexString has to be an even length";const e=new ArrayBuffer(t.length/2),i=new Uint8Array(e);for(let e=0;e<i.length;e++){const r=t.substr(2*e,2),n=parseInt(r,16);i[e]=n}return e}static fromUrlB64ToB64(t){let e=t.replace(/-/g,"+").replace(/_/g,"/");switch(e.length%4){case 0:break;case 2:e+="==";break;case 3:e+="=";break;default:throw new Error("Illegal base64url string!")}return e}static fromUrlB64ToUtf8(t){return d.fromB64ToUtf8(d.fromUrlB64ToB64(t))}static fromUtf8ToB64(t){return d.isNode?Buffer.from(t,"utf8").toString("base64"):o.hp.from(t,"utf8").toString("base64")}static fromUtf8ToUrlB64(t){return d.fromBufferToUrlB64(d.fromUtf8ToArray(t))}static fromB64ToUtf8(t){return d.isNode?Buffer.from(t,"base64").toString("utf8"):o.hp.from(t,"base64").toString("utf8")}static newGuid(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(t=>{const e=16*Math.random()|0;return("x"===t?e:3&e|8).toString(16)}))}static isGuid(t){return RegExp(d.guidRegex,"i").test(t)}static getHostname(t){if(d.isNullOrWhitespace(t))return null;if((t=t.trim()).startsWith("data:"))return null;if(t.startsWith("about:"))return null;if(t.startsWith("file:"))return null;if(t.indexOf("!")>0)return null;try{const e=(0,u.EW)(t,{validHosts:this.validHosts});if(null!=e)return e}catch(t){return null}return null}static getHost(t){const e=d.getUrl(t);try{return null!=e&&""!==e.host?e.host:null}catch(t){return null}}static getDomain(t){if(d.isNullOrWhitespace(t))return null;if((t=t.trim()).startsWith("data:"))return null;if(t.startsWith("about:"))return null;try{const e=(0,u.qg)(t,{validHosts:this.validHosts,allowPrivateDomains:!0});if(null!=e&&null!=e.hostname)return"localhost"===e.hostname||e.isIp?e.hostname:null!=e.domain?e.domain:null}catch(t){return null}return null}static getQueryParams(t){const e=d.getUrl(t);if(null==e||null==e.search||""===e.search)return null;const i=new Map;return("?"===e.search[0]?e.search.substr(1):e.search).split("&").forEach((t=>{const e=t.split("=");e.length<1||i.set(decodeURIComponent(e[0]).toLowerCase(),null==e[1]?"":decodeURIComponent(e[1]))})),i}static getSortFunction(t,e){return(i,r)=>null==i[e]&&null!=r[e]?-1:null!=i[e]&&null==r[e]?1:null==i[e]&&null==r[e]?0:t.collator?t.collator.compare(i[e],r[e]):i[e].localeCompare(r[e])}static isNullOrWhitespace(t){return null==t||"string"!=typeof t||""===t.trim()}static isNullOrEmpty(t){return null==t||"string"!=typeof t||""==t}static isPromise(t){return null!=t&&"function"==typeof t.then&&"function"==typeof t.catch}static nameOf(t){return t}static assign(t,e){return Object.assign(t,e)}static iterateEnum(t){return Object.keys(t).filter((t=>Number.isNaN(+t))).map((e=>t[e]))}static getUrl(t){return this.isNullOrWhitespace(t)?null:(t=t.trim(),d.getUrlObject(t))}static camelToPascalCase(t){return t.charAt(0).toUpperCase()+t.slice(1)}static pickTextColorBasedOnBgColor(t,e=186,i=!1){const r="#"===t.charAt(0)?t.substring(1,7):t;return.299*parseInt(r.substring(0,2),16)+.587*parseInt(r.substring(2,4),16)+.114*parseInt(r.substring(4,6),16)>e?i?"black":"black !important":i?"white":"white !important"}static stringToColor(t){let e=0;for(let i=0;i<t.length;i++)e=t.charCodeAt(i)+((e<<5)-e);let i="#";for(let t=0;t<3;t++)i+=("00"+(e>>8*t&255).toString(16)).substr(-2);return i}static getContainerService(){if(null==this.global.bitwardenContainerService)throw new Error("global bitwardenContainerService not initialized.");return this.global.bitwardenContainerService}static validateHexColor(t){return/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(t)}static mapToRecord(t){return null==t?null:t instanceof Map?Object.fromEntries(t):t}static recordToMap(t){if(null==t)return null;if(t instanceof Map)return t;const e=Object.entries(t);return 0===e.length?new Map:isNaN(Number(e[0][0]))?new Map(e):new Map(e.map((t=>[Number(t[0]),t[1]])))}static merge(t,e){return Object.assign(t,e)}static encodeRFC3986URIComponent(t){return encodeURIComponent(t).replace(/[!'()*]/g,(t=>`%${t.charCodeAt(0).toString(16).toUpperCase()}`))}static normalizePath(t){return s.normalize(decodeURIComponent(t)).replace(/^(\.\.(\/|\\|$))+/,"")}static isMobile(t){let e=!1;var i;return i=t.navigator.userAgent||t.navigator.vendor||t.opera,(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(i)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(i.substr(0,4)))&&(e=!0),e||null!=t.navigator.userAgent.match(/iPad/i)}static delay(t){return new Promise((e=>setTimeout(e,t)))}static asyncToObservable(t){return(0,a.of)(void 0).pipe((0,l.n)((()=>t())))}static daysRemaining(t){const e=t.getTime()-Date.now();return Math.max(0,Math.floor(e/864e5))}static isAppleMobile(t){return null!=t.navigator.userAgent.match(/iPhone/i)||null!=t.navigator.userAgent.match(/iPad/i)}static getUrlObject(t){const e=t.indexOf("://")>-1;if(!e&&t.indexOf(".")>-1)t="http://"+t;else if(!e)return null;try{return null!=c?new c.URL(t):new URL(t)}catch(t){}return null}}var h;function p(t){return t in h?h[t]:"Unknown encryption type "+t}d.inited=!1,d.isNode=!1,d.isBrowser=!0,d.isMobileBrowser=!1,d.isAppleMobileBrowser=!1,d.global=null,d.regexpEmojiPresentation=/(?:[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u270A\u270B\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF93\uDFA0-\uDFCA\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF4\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC3E\uDC40\uDC42-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDD7A\uDD95\uDD96\uDDA4\uDDFB-\uDE4F\uDE80-\uDEC5\uDECC\uDED0-\uDED2\uDED5-\uDED7\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDD78\uDD7A-\uDDCB\uDDCD-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6])/g,d.validHosts=["localhost"],d.originalMinimumPasswordLength=8,d.minimumPasswordLength=12,d.DomainMatchBlacklist=new Map([["google.com",new Set(["script.google.com"])]]),d.guidRegex=/^[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$/,d.init(),function(t){t[t.AesCbc256_B64=0]="AesCbc256_B64",t[t.AesCbc256_HmacSha256_B64=2]="AesCbc256_HmacSha256_B64",t[t.CoseEncrypt0=7]="CoseEncrypt0",t[t.Rsa2048_OaepSha256_B64=3]="Rsa2048_OaepSha256_B64",t[t.Rsa2048_OaepSha1_B64=4]="Rsa2048_OaepSha1_B64",t[t.Rsa2048_OaepSha256_HmacSha256_B64=5]="Rsa2048_OaepSha256_HmacSha256_B64",t[t.Rsa2048_OaepSha1_HmacSha256_B64=6]="Rsa2048_OaepSha1_HmacSha256_B64"}(h||(h={}));const y={[h.AesCbc256_B64]:2,[h.AesCbc256_HmacSha256_B64]:3,[h.Rsa2048_OaepSha256_B64]:1,[h.Rsa2048_OaepSha1_B64]:1,[h.Rsa2048_OaepSha256_HmacSha256_B64]:2,[h.Rsa2048_OaepSha1_HmacSha256_B64]:2,[h.CoseEncrypt0]:1};var m,f,v,g,w,b,S;!function(t){t[t.Direct=0]="Direct",t[t.Azure=1]="Azure"}(m||(m={})),function(t){t[t.ServerAuthorization=1]="ServerAuthorization",t[t.LocalAuthorization=2]="LocalAuthorization"}(f||(f={})),function(t){t.Local="local",t.Memory="memory",t.Session="session"}(v||(v={})),function(t){t.Auto="auto",t.Pin="pin"}(g||(g={})),function(t){t[t.Debug=0]="Debug",t[t.Info=1]="Info",t[t.Warning=2]="Warning",t[t.Error=3]="Error"}(w||(w={})),function(t){t.Both="both",t.Disk="disk",t.Memory="memory"}(b||(b={})),function(t){t.System="system",t.Light="light",t.Dark="dark"}(S||(S={}));class D{constructor(t){if(null==t)throw new Error("Must provide key");if(32===t.byteLength)this.innerKey={type:h.AesCbc256_B64,encryptionKey:t},this.keyB64=this.toBase64();else if(64===t.byteLength)this.innerKey={type:h.AesCbc256_HmacSha256_B64,encryptionKey:t.slice(0,32),authenticationKey:t.slice(32)},this.keyB64=this.toBase64();else{if(!(t.byteLength>64))throw new Error(`Unsupported encType/key length ${t.byteLength}`);this.innerKey={type:h.CoseEncrypt0,encryptionKey:t},this.keyB64=this.toBase64()}}toJSON(){return{keyB64:this.keyB64}}inner(){return this.innerKey}toBase64(){return d.fromBufferToB64(this.toEncoded())}toEncoded(){if(this.innerKey.type===h.AesCbc256_B64)return this.innerKey.encryptionKey;if(this.innerKey.type===h.AesCbc256_HmacSha256_B64){const t=new Uint8Array(64);return t.set(this.innerKey.encryptionKey,0),t.set(this.innerKey.authenticationKey,32),t}if(this.innerKey.type===h.CoseEncrypt0)return this.innerKey.encryptionKey;throw new Error("Unsupported encryption type.")}static fromString(t){if(null==t)return null;const e=d.fromB64ToArray(t);return new D(e)}static fromJSON(t){return D.fromString(null==t?void 0:t.keyB64)}}class N{constructor(t,e=null){this.isDev=t,this.filter=e,this.timersMap=new Map}debug(t,...e){this.isDev&&this.write(w.Debug,t,...e)}info(t,...e){this.write(w.Info,t,...e)}warning(t,...e){this.write(w.Warning,t,...e)}error(t,...e){this.write(w.Error,t,...e)}write(t,e,...i){if(null==this.filter||!this.filter(t))switch(t){case w.Debug:case w.Info:console.log(e,...i);break;case w.Warning:console.warn(e,...i);break;case w.Error:console.error(e,...i)}}}class k{constructor(t,e){this.keyService=t,this.encryptService=e}attachToGlobal(t){t.bitwardenContainerService||(t.bitwardenContainerService=this)}getKeyService(){if(null==this.keyService)throw new Error("ContainerService.keyService not initialized.");return this.keyService}getEncryptService(){if(null==this.encryptService)throw new Error("ContainerService.encryptService not initialized.");return this.encryptService}}var O=function(t,e,i,r){return new(i||(i=Promise))((function(n,s){function o(t){try{l(r.next(t))}catch(t){s(t)}}function a(t){try{l(r.throw(t))}catch(t){s(t)}}function l(t){var e;t.done?n(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(o,a)}l((r=r.apply(t,e||[])).next())}))};const C="[error: cannot decrypt]";class B{constructor(t,e,i,r){null!=e?this.initFromData(t,e,i,r):this.initFromEncryptedString(t)}get ivBytes(){return null==this.iv?null:d.fromB64ToArray(this.iv)}get macBytes(){return null==this.mac?null:d.fromB64ToArray(this.mac)}get dataBytes(){return null==this.data?null:d.fromB64ToArray(this.data)}toJSON(){return this.encryptedString}static fromJSON(t){return null==t?null:new B(t)}initFromData(t,e,i,r){this.encryptedString=null!=i?t+"."+i+"|"+e:t+"."+e,null!=r&&(this.encryptedString=this.encryptedString+"|"+r),this.encryptionType=t,this.data=e,this.iv=i,this.mac=r}initFromEncryptedString(t){if(this.encryptedString=t,!this.encryptedString)return;const{encType:e,encPieces:i}=B.parseEncryptedString(this.encryptedString);if(this.encryptionType=e,i.length===y[e])switch(e){case h.AesCbc256_HmacSha256_B64:this.iv=i[0],this.data=i[1],this.mac=i[2];break;case h.AesCbc256_B64:this.iv=i[0],this.data=i[1];break;case h.Rsa2048_OaepSha256_B64:case h.Rsa2048_OaepSha1_B64:this.data=i[0];break;case h.Rsa2048_OaepSha256_HmacSha256_B64:case h.Rsa2048_OaepSha1_HmacSha256_B64:this.data=i[0],this.mac=i[1];break;default:return}}static parseEncryptedString(t){const e=t.split(".");let i,r=null;if(2===e.length)try{i=parseInt(e[0],null),r=e[1].split("|")}catch(t){return{encType:NaN,encPieces:[]}}else r=t.split("|"),i=h.AesCbc256_B64;return{encType:i,encPieces:r}}static isSerializedEncString(t){if(null==t)return!1;const{encType:e,encPieces:i}=this.parseEncryptedString(t);return!isNaN(e)&&0!==i.length&&y[e]===i.length}decrypt(t){return O(this,arguments,void 0,(function*(t,e=null,i){if(null!=this.decryptedValue)return this.decryptedValue;try{if(null==e&&(e=yield this.getKeyForDecryption(t)),null==e)throw new Error("No key to decrypt EncString with orgId "+t);const i=d.getContainerService().getEncryptService();this.decryptedValue=yield i.decryptString(this,e)}catch(t){this.decryptedValue=C}return this.decryptedValue}))}decryptWithKey(t,e){return O(this,arguments,void 0,(function*(t,e,i="domain-withkey"){try{if(null==t)throw new Error("No key to decrypt EncString");this.decryptedValue=yield e.decryptString(this,t)}catch(t){this.decryptedValue=C}return this.decryptedValue}))}getKeyForDecryption(t){return O(this,void 0,void 0,(function*(){const e=d.getContainerService().getKeyService();return null!=t?yield e.getOrgKey(t):yield e.getUserKeyWithLegacySupport()}))}}var A,E=function(t,e,i,r){return new(i||(i=Promise))((function(n,s){function o(t){try{l(r.next(t))}catch(t){s(t)}}function a(t){try{l(r.throw(t))}catch(t){s(t)}}function l(t){var e;t.done?n(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(o,a)}l((r=r.apply(t,e||[])).next())}))};class x{buildDomainModel(t,e,i,r=[]){for(const n in i){if(!i.hasOwnProperty(n))continue;const s=e[i[n]||n];r.indexOf(n)>-1?t[n]=s||null:t[n]=s?new B(s):null}}buildDataModel(t,e,i,r=[]){for(const n in i){if(!i.hasOwnProperty(n))continue;const s=t[i[n]||n];r.indexOf(n)>-1?e[n]=null!=s?s:null:e[n]=null!=s?s.encryptedString:null}}decryptObj(t,e,i,r){return E(this,arguments,void 0,(function*(t,e,i,r,n=null,s="No Domain Context"){var o,a;for(const l of i)e[l]=null!==(a=yield null===(o=t[l])||void 0===o?void 0:o.decrypt(r,n,`Property: ${l}; ObjectContext: ${s}`))&&void 0!==a?a:null;return e}))}decryptObjWithKey(t,e,i){return E(this,arguments,void 0,(function*(t,e,i,r=this.constructor,n="No Domain Context"){const s=[];for(const r of t){const t=this[r],o=yield this.decryptProperty(r,t,e,i,`Property: ${r.toString()}; ObjectContext: ${n}`);s.push(o)}return s.reduce(((t,e)=>Object.assign(Object.assign({},t),e)),Object.assign({},this))}))}decryptProperty(t,e,i,r,n){return E(this,void 0,void 0,(function*(){let s=null;return e&&(s=yield e.decryptWithKey(i,r,n)),{[t]:s}}))}}!function(t){t[t.Cipher=0]="Cipher",t[t.CipherView=1]="CipherView"}(A||(A={}));const _=Object.freeze({Login:1,SecureNote:2,Card:3,Identity:4,SshKey:5});Object.freeze(Object.fromEntries(Object.entries(_).map((([t,e])=>[e,t]))));class P{constructor(t){this.response=t}getResponseProperty(t,e=null,i=!1){if(null==t||""===t)throw new Error("propertyName must not be null/empty.");if(null==e&&null!=this.response&&(e=this.response),null==e)return null;if(!i&&void 0===e[t]){let i=null;i=t.charAt(0)===t.charAt(0).toUpperCase()?t.charAt(0).toLowerCase():t.charAt(0).toUpperCase(),t.length>1&&(i+=t.slice(1)),void 0===e[t=i]&&(t=t.toLowerCase()),void 0===e[t]&&(t=t.toUpperCase())}return e[t]}}class K extends P{constructor(t=null){super(t),this.delete=!1,this.restore=!1,null!=t&&(this.delete=this.getResponseProperty("Delete"),this.restore=this.getResponseProperty("Restore"))}static fromJSON(t){return Object.assign(new K,t)}static fromSdkCipherPermissions(t){if(!t)return;const e=new K;return e.delete=t.delete,e.restore=t.restore,e}}class U{constructor(t){null!=t&&(this.id=t.id,this.url=t.url,this.fileName=t.fileName,this.key=t.key,this.size=t.size,this.sizeName=t.sizeName)}}class I{constructor(t){null!=t&&(this.cardholderName=t.cardholderName,this.brand=t.brand,this.number=t.number,this.expMonth=t.expMonth,this.expYear=t.expYear,this.code=t.code)}}class T{constructor(t){null!=t&&(this.type=t.type,this.name=t.name,this.value=t.value,this.linkedId=t.linkedId)}}class F{constructor(t){null!=t&&(this.title=t.title,this.firstName=t.firstName,this.middleName=t.middleName,this.lastName=t.lastName,this.address1=t.address1,this.address2=t.address2,this.address3=t.address3,this.city=t.city,this.state=t.state,this.postalCode=t.postalCode,this.country=t.country,this.company=t.company,this.email=t.email,this.phone=t.phone,this.ssn=t.ssn,this.username=t.username,this.passportNumber=t.passportNumber,this.licenseNumber=t.licenseNumber)}}class J{constructor(t){null!=t&&(this.credentialId=t.credentialId,this.keyType=t.keyType,this.keyAlgorithm=t.keyAlgorithm,this.keyCurve=t.keyCurve,this.keyValue=t.keyValue,this.rpId=t.rpId,this.userHandle=t.userHandle,this.userName=t.userName,this.counter=t.counter,this.rpName=t.rpName,this.userDisplayName=t.userDisplayName,this.discoverable=t.discoverable,this.creationDate=t.creationDate)}}class R{constructor(t){this.match=null,null!=t&&(this.uri=t.uri,this.uriChecksum=t.uriChecksum,this.match=t.match)}}class M{constructor(t){var e;null!=t&&(this.username=t.username,this.password=t.password,this.passwordRevisionDate=t.passwordRevisionDate,this.totp=t.totp,this.autofillOnPageLoad=t.autofillOnPageLoad,t.uris&&(this.uris=t.uris.map((t=>new R(t)))),t.fido2Credentials&&(this.fido2Credentials=null===(e=t.fido2Credentials)||void 0===e?void 0:e.map((t=>new J(t)))))}}class L{constructor(t){null!=t&&(this.password=t.password,this.lastUsedDate=t.lastUsedDate)}}class z{constructor(t){null!=t&&(this.type=t.type)}}class j{constructor(t){null!=t&&(this.privateKey=t.privateKey,this.publicKey=t.publicKey,this.keyFingerprint=t.keyFingerprint)}}class H{constructor(t,e){if(null!=t){switch(this.id=t.id,this.organizationId=t.organizationId,this.folderId=t.folderId,this.edit=t.edit,this.viewPassword=t.viewPassword,this.permissions=t.permissions,this.organizationUseTotp=t.organizationUseTotp,this.favorite=t.favorite,this.revisionDate=t.revisionDate,this.type=t.type,this.name=t.name,this.notes=t.notes,this.collectionIds=null!=e?e:t.collectionIds,this.creationDate=t.creationDate,this.deletedDate=t.deletedDate,this.reprompt=t.reprompt,this.key=t.key,this.type){case _.Login:this.login=new M(t.login);break;case _.SecureNote:this.secureNote=new z(t.secureNote);break;case _.Card:this.card=new I(t.card);break;case _.Identity:this.identity=new F(t.identity);break;case _.SshKey:this.sshKey=new j(t.sshKey)}null!=t.fields&&(this.fields=t.fields.map((t=>new T(t)))),null!=t.attachments&&(this.attachments=t.attachments.map((t=>new U(t)))),null!=t.passwordHistory&&(this.passwordHistory=t.passwordHistory.map((t=>new L(t))))}}static fromJSON(t){const e=Object.assign(new H,t);return e.permissions=K.fromJSON(t.permissions),e}}const W=(Object.freeze({Text:0,Hidden:1,Boolean:2,Linked:3}),100);class V{constructor(t){this.id=null,this.url=null,this.size=null,this.sizeName=null,this.fileName=null,this.key=null,t&&(this.id=t.id,this.url=t.url,this.size=t.size,this.sizeName=t.sizeName)}get fileSize(){try{if(null!=this.size)return parseInt(this.size,null)}catch(t){}return 0}static fromJSON(t){const e=null==t.key?null:D.fromJSON(t.key);return Object.assign(new V,t,{key:e})}toSdkAttachmentView(){var t;return{id:this.id,url:this.url,size:this.size,sizeName:this.sizeName,fileName:this.fileName,key:null===(t=this.encryptedKey)||void 0===t?void 0:t.toJSON()}}static fromSdkAttachmentView(t){var e,i,r,n,s;if(!t)return;const o=new V;return o.id=null!==(e=t.id)&&void 0!==e?e:null,o.url=null!==(i=t.url)&&void 0!==i?i:null,o.size=null!==(r=t.size)&&void 0!==r?r:null,o.sizeName=null!==(n=t.sizeName)&&void 0!==n?n:null,o.fileName=null!==(s=t.fileName)&&void 0!==s?s:null,o.encryptedKey=new B(t.key),o}}const $="\\"+["/","-","."," "].join("\\").replace(" ","s");new RegExp(`[${$}]`,"g"),new RegExp(`[^\\d${$}]`,"g"),new RegExp("^(([1]{1}[0-2]{1})|(0?[1-9]{1}))$"),new RegExp("^2[0-1]{1}\\d{2}$");class Y{constructor(t,e){this.propertyKey=t,this._i18nKey=null==e?void 0:e.i18nKey,this.sortPosition=e.sortPosition}get i18nKey(){var t;return null!==(t=this._i18nKey)&&void 0!==t?t:this.propertyKey}}function q(t,e){return(i,r)=>{null==i.linkedFieldOptions&&(i.linkedFieldOptions=new Map),i.linkedFieldOptions.set(t,new Y(r,e))}}class G{}var Q=function(t,e,i,r){var n,s=arguments.length,o=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,i):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(t,e,i,r);else for(var a=t.length-1;a>=0;a--)(n=t[a])&&(o=(s<3?n(o):s>3?n(e,i,o):n(e,i))||o);return s>3&&o&&Object.defineProperty(e,i,o),o},X=function(t,e){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,e)};class Z extends G{constructor(){super(...arguments),this.cardholderName=null,this.expMonth=null,this.expYear=null,this.code=null,this._brand=null,this._number=null,this._subTitle=null}get maskedCode(){return null!=this.code?"•".repeat(this.code.length):null}get maskedNumber(){return null!=this.number?"•".repeat(this.number.length):null}get brand(){return this._brand}set brand(t){this._brand=t,this._subTitle=null}get number(){return this._number}set number(t){this._number=t,this._subTitle=null}get subTitle(){if(null==this._subTitle&&(this._subTitle=this.brand,null!=this.number&&this.number.length>=4)){null!=this._subTitle&&""!==this._subTitle?this._subTitle+=", ":this._subTitle="";const t=this.number.length>=5&&null!=this.number.match(new RegExp("^3[47]"))?5:4;this._subTitle+="*"+this.number.substr(this.number.length-t)}return this._subTitle}get expiration(){const t=function(t){const e=null==t||""===t;let i=e?null:`${t}`;if(e||i&&/^[1-9]{1}\d{3}$/.test(i))return i;if(i=(i||"").replace(/[^\d]/g,"").replace(/^[0]+(?=.)/,""),""===i&&(i=null),i&&4!==i.length){const t=("00"+i).slice(-2);i=`${(new Date).getFullYear()}`.slice(0,2)+t}return i}(this.expYear);if(!this.expMonth&&!t)return null;let e=null!=this.expMonth?("0"+this.expMonth).slice(-2):"__";return e+=" / "+(t||"____"),e}static fromJSON(t){return Object.assign(new Z,t)}static getCardBrandByPatterns(t){if(null==t||"string"!=typeof t||""===t.trim())return null;let e=new RegExp("^4");return null!=t.match(e)?"Visa":/^(5[1-5][0-9]{14}|2(22[1-9][0-9]{12}|2[3-9][0-9]{13}|[3-6][0-9]{14}|7[0-1][0-9]{13}|720[0-9]{12}))$/.test(t)?"Mastercard":(e=new RegExp("^3[47]"),null!=t.match(e)?"Amex":(e=new RegExp("^(6011|622(12[6-9]|1[3-9][0-9]|[2-8][0-9]{2}|9[0-1][0-9]|92[0-5]|64[4-9])|65)"),null!=t.match(e)?"Discover":(e=new RegExp("^36"),null!=t.match(e)?"Diners Club":(e=new RegExp("^30[0-5]"),null!=t.match(e)?"Diners Club":(e=new RegExp("^35(2[89]|[3-8][0-9])"),null!=t.match(e)?"JCB":(e=new RegExp("^(4026|417500|4508|4844|491(3|7))"),null!=t.match(e)?"Visa":null))))))}static fromSdkCardView(t){if(null!=t)return Object.assign(new Z,t)}}Q([q(300,{sortPosition:0}),X("design:type",String)],Z.prototype,"cardholderName",void 0),Q([q(301,{sortPosition:3,i18nKey:"expirationMonth"}),X("design:type",String)],Z.prototype,"expMonth",void 0),Q([q(302,{sortPosition:4,i18nKey:"expirationYear"}),X("design:type",String)],Z.prototype,"expYear",void 0),Q([q(303,{sortPosition:5,i18nKey:"securityCode"}),X("design:type",String)],Z.prototype,"code",void 0),Q([q(304,{sortPosition:2}),X("design:type",String),X("design:paramtypes",[String])],Z.prototype,"brand",null),Q([q(305,{sortPosition:1}),X("design:type",String),X("design:paramtypes",[String])],Z.prototype,"number",null);class tt{constructor(t){this.name=null,this.value=null,this.type=null,this.newField=!1,this.showValue=!1,this.showCount=!1,this.linkedId=null,t&&(this.type=t.type,this.linkedId=t.linkedId)}get maskedValue(){return null!=this.value?"••••••••":null}static fromJSON(t){return Object.assign(new tt,t)}static fromSdkFieldView(t){if(!t)return;const e=new tt;return e.name=t.name,e.value=t.value,e.type=t.type,e.linkedId=t.linkedId,e}}var et=function(t,e,i,r){var n,s=arguments.length,o=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,i):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(t,e,i,r);else for(var a=t.length-1;a>=0;a--)(n=t[a])&&(o=(s<3?n(o):s>3?n(e,i,o):n(e,i))||o);return s>3&&o&&Object.defineProperty(e,i,o),o},it=function(t,e){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,e)};class rt extends G{constructor(){super(),this.title=null,this.middleName=null,this.address1=null,this.address2=null,this.address3=null,this.city=null,this.state=null,this.postalCode=null,this.country=null,this.company=null,this.email=null,this.phone=null,this.ssn=null,this.username=null,this.passportNumber=null,this.licenseNumber=null,this._firstName=null,this._lastName=null,this._subTitle=null}get firstName(){return this._firstName}set firstName(t){this._firstName=t,this._subTitle=null}get lastName(){return this._lastName}set lastName(t){this._lastName=t,this._subTitle=null}get subTitle(){return null!=this._subTitle||null==this.firstName&&null==this.lastName||(this._subTitle="",null!=this.firstName&&(this._subTitle=this.firstName),null!=this.lastName&&(""!==this._subTitle&&(this._subTitle+=" "),this._subTitle+=this.lastName)),this._subTitle}get fullName(){if(null!=this.title||null!=this.firstName||null!=this.middleName||null!=this.lastName){let t="";return null!=this.title&&(t+=this.title+" "),null!=this.firstName&&(t+=this.firstName+" "),null!=this.middleName&&(t+=this.middleName+" "),null!=this.lastName&&(t+=this.lastName),t.trim()}return null}get fullAddress(){let t=this.address1;return d.isNullOrWhitespace(this.address2)||(d.isNullOrWhitespace(t)||(t+=", "),t+=this.address2),d.isNullOrWhitespace(this.address3)||(d.isNullOrWhitespace(t)||(t+=", "),t+=this.address3),t}get fullAddressPart2(){if(null==this.city&&null==this.state&&null==this.postalCode)return null;const t=this.city||"-",e=this.state,i=this.postalCode||"-";let r=t;return d.isNullOrWhitespace(e)||(r+=", "+e),r+=", "+i,r}get fullAddressForCopy(){let t=this.fullAddress;return null==this.city&&null==this.state&&null==this.postalCode||(t+="\n"+this.fullAddressPart2),null!=this.country&&(t+="\n"+this.country),t}static fromJSON(t){return Object.assign(new rt,t)}static fromSdkIdentityView(t){if(null!=t)return Object.assign(new rt,t)}}et([q(400,{sortPosition:0}),it("design:type",String)],rt.prototype,"title",void 0),et([q(401,{sortPosition:2}),it("design:type",String)],rt.prototype,"middleName",void 0),et([q(402,{sortPosition:12}),it("design:type",String)],rt.prototype,"address1",void 0),et([q(403,{sortPosition:13}),it("design:type",String)],rt.prototype,"address2",void 0),et([q(404,{sortPosition:14}),it("design:type",String)],rt.prototype,"address3",void 0),et([q(405,{sortPosition:15,i18nKey:"cityTown"}),it("design:type",String)],rt.prototype,"city",void 0),et([q(406,{sortPosition:16,i18nKey:"stateProvince"}),it("design:type",String)],rt.prototype,"state",void 0),et([q(407,{sortPosition:17,i18nKey:"zipPostalCode"}),it("design:type",String)],rt.prototype,"postalCode",void 0),et([q(408,{sortPosition:18}),it("design:type",String)],rt.prototype,"country",void 0),et([q(409,{sortPosition:6}),it("design:type",String)],rt.prototype,"company",void 0),et([q(410,{sortPosition:10}),it("design:type",String)],rt.prototype,"email",void 0),et([q(411,{sortPosition:11}),it("design:type",String)],rt.prototype,"phone",void 0),et([q(412,{sortPosition:7}),it("design:type",String)],rt.prototype,"ssn",void 0),et([q(413,{sortPosition:5}),it("design:type",String)],rt.prototype,"username",void 0),et([q(414,{sortPosition:8}),it("design:type",String)],rt.prototype,"passportNumber",void 0),et([q(415,{sortPosition:9}),it("design:type",String)],rt.prototype,"licenseNumber",void 0),et([q(416,{sortPosition:1}),it("design:type",String),it("design:paramtypes",[String])],rt.prototype,"firstName",null),et([q(417,{sortPosition:4}),it("design:type",String),it("design:paramtypes",[String])],rt.prototype,"lastName",null),et([q(418,{sortPosition:3}),it("design:type",String),it("design:paramtypes",[])],rt.prototype,"fullName",null);class nt extends G{constructor(){super(...arguments),this.creationDate=null}get subTitle(){return this.userDisplayName}static fromJSON(t){const e=null!=t.creationDate?new Date(t.creationDate):null;return Object.assign(new nt,t,{creationDate:e})}static fromSdkFido2CredentialView(t){var e;if(!t)return;const i=new nt;return i.credentialId=t.credentialId,i.keyType=t.keyType,i.keyAlgorithm=t.keyAlgorithm,i.keyCurve=t.keyCurve,i.rpId=t.rpId,i.userHandle=t.userHandle,i.userName=t.userName,i.counter=parseInt(t.counter),i.rpName=t.rpName,i.userDisplayName=t.userDisplayName,i.discoverable="true"===(null===(e=t.discoverable)||void 0===e?void 0:e.toLowerCase()),i.creationDate=t.creationDate?new Date(t.creationDate):null,i}}const st=["https://","http://","ssh://","ftp://","sftp://","irc://","vnc://","rdp://","ms-rd:","chrome://","iosapp://","androidapp://"];class ot{static canLaunch(t){if(d.isNullOrWhitespace(t))return!1;for(let e=0;e<st.length;e++)if(0===t.indexOf(st[e]))return!0;return!1}}class at{constructor(t){this.match=null,this._uri=null,this._domain=null,this._hostname=null,this._host=null,this._canLaunch=null,t&&(this.match=t.match)}get uri(){return this._uri}set uri(t){this._uri=t,this._domain=null,this._canLaunch=null}get domain(){return null==this._domain&&null!=this.uri&&(this._domain=d.getDomain(this.uri),""===this._domain&&(this._domain=null)),this._domain}get hostname(){return 4===this.match?null:(null==this._hostname&&null!=this.uri&&(this._hostname=d.getHostname(this.uri),""===this._hostname&&(this._hostname=null)),this._hostname)}get host(){return 4===this.match?null:(null==this._host&&null!=this.uri&&(this._host=d.getHost(this.uri),""===this._host&&(this._host=null)),this._host)}get hostnameOrUri(){return null!=this.hostname?this.hostname:this.uri}get hostOrUri(){return null!=this.host?this.host:this.uri}get isWebsite(){return null!=this.uri&&(0===this.uri.indexOf("http://")||0===this.uri.indexOf("https://")||this.uri.indexOf("://")<0&&!d.isNullOrWhitespace(d.getDomain(this.uri)))}get canLaunch(){return null!=this._canLaunch||(null!=this.uri&&4!==this.match?this._canLaunch=ot.canLaunch(this.launchUri):this._canLaunch=!1),this._canLaunch}get launchUri(){return this.uri.indexOf("://")<0&&!d.isNullOrWhitespace(d.getDomain(this.uri))?"http://"+this.uri:this.uri}static fromJSON(t){return Object.assign(new at,t)}static fromSdkLoginUriView(t){if(null==t)return;const e=new at;return e.uri=t.uri,e.match=t.match,e}matchesUri(t,e,i=null){var r;if(!this.uri||!t)return!1;let n=null!==(r=this.match)&&void 0!==r?r:i;null!=n||(n=0);const s=d.getDomain(t),o=e.add(s);switch(n){case 0:return this.matchesDomain(t,o);case 1:{const e=d.getHost(t);return null!=e&&e===d.getHost(this.uri)}case 3:return t===this.uri;case 2:return t.startsWith(this.uri);case 4:try{return new RegExp(this.uri,"i").test(t)}catch(t){return!1}case 5:return!1}return!1}matchesDomain(t,e){if(null==t||null==this.domain||!e.has(this.domain))return!1;if(d.DomainMatchBlacklist.has(this.domain)){const e=d.getHost(t);return!d.DomainMatchBlacklist.get(this.domain).has(e)}return!0}}var lt=function(t,e,i,r){var n,s=arguments.length,o=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,i):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(t,e,i,r);else for(var a=t.length-1;a>=0;a--)(n=t[a])&&(o=(s<3?n(o):s>3?n(e,i,o):n(e,i))||o);return s>3&&o&&Object.defineProperty(e,i,o),o},ut=function(t,e){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,e)};class ct extends G{constructor(t){super(),this.username=null,this.password=null,this.passwordRevisionDate=null,this.totp=null,this.uris=[],this.autofillOnPageLoad=null,this.fido2Credentials=null,t&&(this.passwordRevisionDate=t.passwordRevisionDate,this.autofillOnPageLoad=t.autofillOnPageLoad)}get uri(){return this.hasUris?this.uris[0].uri:null}get maskedPassword(){return null!=this.password?"••••••••":null}get subTitle(){var t;return d.isNullOrEmpty(this.username)&&(null===(t=this.fido2Credentials)||void 0===t?void 0:t.length)>0?this.fido2Credentials[0].userName:this.username}get canLaunch(){return this.hasUris&&this.uris.some((t=>t.canLaunch))}get hasTotp(){return!d.isNullOrWhitespace(this.totp)}get launchUri(){if(this.hasUris){const t=this.uris.find((t=>t.canLaunch));if(null!=t)return t.launchUri}return null}get hasUris(){return null!=this.uris&&this.uris.length>0}get hasFido2Credentials(){return null!=this.fido2Credentials&&this.fido2Credentials.length>0}matchesUri(t,e,i=null){return null!=this.uris&&this.uris.some((r=>r.matchesUri(t,e,i)))}static fromJSON(t){var e;const i=null==t.passwordRevisionDate?null:new Date(t.passwordRevisionDate),r=t.uris.map((t=>at.fromJSON(t))),n=null===(e=t.fido2Credentials)||void 0===e?void 0:e.map((t=>nt.fromJSON(t)));return Object.assign(new ct,t,{passwordRevisionDate:i,uris:r,fido2Credentials:n})}static fromSdkLoginView(t){var e;if(null==t)return;const i=null==t.passwordRevisionDate?null:new Date(t.passwordRevisionDate),r=(null===(e=t.uris)||void 0===e?void 0:e.map((t=>at.fromSdkLoginUriView(t))))||[];return Object.assign(new ct,t,{passwordRevisionDate:i,uris:r})}}lt([q(W,{sortPosition:0}),ut("design:type",String)],ct.prototype,"username",void 0),lt([q(101,{sortPosition:1}),ut("design:type",String)],ct.prototype,"password",void 0);class dt{constructor(t){this.password=null,this.lastUsedDate=null,t&&(this.lastUsedDate=t.lastUsedDate)}static fromJSON(t){const e=null==t.lastUsedDate?null:new Date(t.lastUsedDate);return Object.assign(new dt,t,{lastUsedDate:e})}static fromSdkPasswordHistoryView(t){if(!t)return;const e=new dt;return e.password=t.password,e.lastUsedDate=null==t.lastUsedDate?null:new Date(t.lastUsedDate),e}}class ht extends G{constructor(t){super(),this.type=null,t&&(this.type=t.type)}get subTitle(){return null}static fromJSON(t){return Object.assign(new ht,t)}static fromSdkSecureNoteView(t){if(t)return Object.assign(new ht,t)}}class pt extends G{constructor(t){super(),this.privateKey=null,this.publicKey=null,this.keyFingerprint=null}get maskedPrivateKey(){if(!this.privateKey||0===this.privateKey.length)return"";let t=this.privateKey.split("\n").filter((t=>""!==t.trim()));return t=t.map(((e,i)=>0===i||i===t.length-1?e:this.maskLine(e))),t.join("\n")}maskLine(t){return"•".repeat(32)}get subTitle(){return this.keyFingerprint}static fromJSON(t){return Object.assign(new pt,t)}static fromSdkSshKeyView(t){if(!t)return;const e=t.fingerprint;return Object.assign(new pt,t,{keyFingerprint:e})}}class yt{constructor(t){var e;this.initializerKey=A.CipherView,this.id=null,this.organizationId=null,this.folderId=null,this.name=null,this.notes=null,this.type=null,this.favorite=!1,this.organizationUseTotp=!1,this.permissions=new K,this.edit=!1,this.viewPassword=!0,this.login=new ct,this.identity=new rt,this.card=new Z,this.secureNote=new ht,this.sshKey=new pt,this.attachments=null,this.fields=null,this.passwordHistory=null,this.collectionIds=null,this.revisionDate=null,this.creationDate=null,this.deletedDate=null,this.reprompt=0,this.decryptionFailure=!1,t&&(this.id=t.id,this.organizationId=t.organizationId,this.folderId=t.folderId,this.favorite=t.favorite,this.organizationUseTotp=t.organizationUseTotp,this.edit=t.edit,this.viewPassword=t.viewPassword,this.permissions=t.permissions,this.type=t.type,this.localData=t.localData,this.collectionIds=t.collectionIds,this.revisionDate=t.revisionDate,this.creationDate=t.creationDate,this.deletedDate=t.deletedDate,this.reprompt=null!==(e=t.reprompt)&&void 0!==e?e:0)}get item(){switch(this.type){case _.Login:return this.login;case _.SecureNote:return this.secureNote;case _.Card:return this.card;case _.Identity:return this.identity;case _.SshKey:return this.sshKey}return null}get subTitle(){var t;return null===(t=this.item)||void 0===t?void 0:t.subTitle}get hasPasswordHistory(){return this.passwordHistory&&this.passwordHistory.length>0}get hasAttachments(){return this.attachments&&this.attachments.length>0}get hasOldAttachments(){if(this.hasAttachments)for(let t=0;t<this.attachments.length;t++)if(null==this.attachments[t].key&&null==this.attachments[t].encryptedKey)return!0;return!1}get hasFields(){return this.fields&&this.fields.length>0}get passwordRevisionDisplayDate(){return this.type!==_.Login||null==this.login||null==this.login.password||""===this.login.password?null:this.login.passwordRevisionDate}get isDeleted(){return null!=this.deletedDate}get linkedFieldOptions(){var t;return null===(t=this.item)||void 0===t?void 0:t.linkedFieldOptions}get isUnassigned(){return null!=this.organizationId&&(null==this.collectionIds||0===this.collectionIds.length)}get canAssignToCollections(){return null==this.organizationId||this.edit&&this.viewPassword}get canLaunch(){return this.type===_.Login&&this.login.canLaunch}linkedFieldValue(t){var e;const i=null===(e=this.linkedFieldOptions)||void 0===e?void 0:e.get(t);return null==i?null:(this.item,this.item[i.propertyKey])}linkedFieldI18nKey(t){var e;return null===(e=this.linkedFieldOptions.get(t))||void 0===e?void 0:e.i18nKey}toJSON(){return this}static fromJSON(t){var e,i,r;if(null==t)return null;const n=new yt,s=null==t.creationDate?null:new Date(t.creationDate),o=null==t.revisionDate?null:new Date(t.revisionDate),a=null==t.deletedDate?null:new Date(t.deletedDate),l=null===(e=t.attachments)||void 0===e?void 0:e.map((t=>V.fromJSON(t))),u=null===(i=t.fields)||void 0===i?void 0:i.map((t=>tt.fromJSON(t))),c=null===(r=t.passwordHistory)||void 0===r?void 0:r.map((t=>dt.fromJSON(t)));switch(Object.assign(n,t,{creationDate:s,revisionDate:o,deletedDate:a,attachments:l,fields:u,passwordHistory:c}),t.type){case _.Card:n.card=Z.fromJSON(t.card);break;case _.Identity:n.identity=rt.fromJSON(t.identity);break;case _.Login:n.login=ct.fromJSON(t.login);break;case _.SecureNote:n.secureNote=ht.fromJSON(t.secureNote);break;case _.SshKey:n.sshKey=pt.fromJSON(t.sshKey)}return n}static fromSdkCipherView(t){var e,i,r,n,s,o,a,l,u,c,d,h;if(null==t)return;const p=new yt;switch(p.id=null!==(e=t.id)&&void 0!==e?e:null,p.organizationId=null!==(i=t.organizationId)&&void 0!==i?i:null,p.folderId=null!==(r=t.folderId)&&void 0!==r?r:null,p.name=t.name,p.notes=null!==(n=t.notes)&&void 0!==n?n:null,p.type=t.type,p.favorite=t.favorite,p.organizationUseTotp=t.organizationUseTotp,p.permissions=K.fromSdkCipherPermissions(t.permissions),p.edit=t.edit,p.viewPassword=t.viewPassword,p.localData=t.localData?{lastUsedDate:t.localData.lastUsedDate?new Date(t.localData.lastUsedDate).getTime():void 0,lastLaunched:t.localData.lastLaunched?new Date(t.localData.lastLaunched).getTime():void 0}:void 0,p.attachments=null!==(o=null===(s=t.attachments)||void 0===s?void 0:s.map((t=>V.fromSdkAttachmentView(t))))&&void 0!==o?o:null,p.fields=null!==(l=null===(a=t.fields)||void 0===a?void 0:a.map((t=>tt.fromSdkFieldView(t))))&&void 0!==l?l:null,p.passwordHistory=null!==(c=null===(u=t.passwordHistory)||void 0===u?void 0:u.map((t=>dt.fromSdkPasswordHistoryView(t))))&&void 0!==c?c:null,p.collectionIds=null!==(d=t.collectionIds)&&void 0!==d?d:null,p.revisionDate=null==t.revisionDate?null:new Date(t.revisionDate),p.creationDate=null==t.creationDate?null:new Date(t.creationDate),p.deletedDate=null==t.deletedDate?null:new Date(t.deletedDate),p.reprompt=null!==(h=t.reprompt)&&void 0!==h?h:0,t.type){case _.Card:p.card=Z.fromSdkCardView(t.card);break;case _.Identity:p.identity=rt.fromSdkIdentityView(t.identity);break;case _.Login:p.login=ct.fromSdkLoginView(t.login);break;case _.SecureNote:p.secureNote=ht.fromSdkSecureNoteView(t.secureNote);break;case _.SshKey:p.sshKey=pt.fromSdkSshKeyView(t.sshKey)}return p}}var mt=function(t,e,i,r){return new(i||(i=Promise))((function(n,s){function o(t){try{l(r.next(t))}catch(t){s(t)}}function a(t){try{l(r.throw(t))}catch(t){s(t)}}function l(t){var e;t.done?n(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(o,a)}l((r=r.apply(t,e||[])).next())}))};class ft extends x{constructor(t){super(),null!=t&&(this.size=t.size,this.buildDomainModel(this,t,{id:null,url:null,sizeName:null,fileName:null,key:null},["id","url","sizeName"]))}decrypt(t){return mt(this,arguments,void 0,(function*(t,e="No Cipher Context",i){const r=yield this.decryptObj(this,new V(this),["fileName"],t,i,"DomainType: Attachment; "+e);return null!=this.key&&(r.key=yield this.decryptAttachmentKey(t,i)),r}))}decryptAttachmentKey(t,e){return mt(this,void 0,void 0,(function*(){try{null==e&&(e=yield this.getKeyForDecryption(t));const i=d.getContainerService().getEncryptService();return yield i.unwrapSymmetricKey(this.key,e)}catch(t){}}))}getKeyForDecryption(t){return mt(this,void 0,void 0,(function*(){const e=d.getContainerService().getKeyService();return null!=t?yield e.getOrgKey(t):yield e.getUserKeyWithLegacySupport()}))}toAttachmentData(){const t=new U;return t.size=this.size,this.buildDataModel(this,t,{id:null,url:null,sizeName:null,fileName:null,key:null},["id","url","sizeName"]),t}static fromJSON(t){if(null==t)return null;const e=B.fromJSON(t.key),i=B.fromJSON(t.fileName);return Object.assign(new ft,t,{key:e,fileName:i})}toSdkAttachment(){var t,e;return{id:this.id,url:this.url,size:this.size,sizeName:this.sizeName,fileName:null===(t=this.fileName)||void 0===t?void 0:t.toJSON(),key:null===(e=this.key)||void 0===e?void 0:e.toJSON()}}}class vt extends x{constructor(t){super(),null!=t&&this.buildDomainModel(this,t,{cardholderName:null,brand:null,number:null,expMonth:null,expYear:null,code:null},[])}decrypt(t){return e=this,i=arguments,n=function*(t,e="No Cipher Context",i){return this.decryptObj(this,new Z,["cardholderName","brand","number","expMonth","expYear","code"],t,i,"DomainType: Card; "+e)},new((r=void 0)||(r=Promise))((function(t,s){function o(t){try{l(n.next(t))}catch(t){s(t)}}function a(t){try{l(n.throw(t))}catch(t){s(t)}}function l(e){var i;e.done?t(e.value):(i=e.value,i instanceof r?i:new r((function(t){t(i)}))).then(o,a)}l((n=n.apply(e,i||[])).next())}));var e,i,r,n}toCardData(){const t=new I;return this.buildDataModel(this,t,{cardholderName:null,brand:null,number:null,expMonth:null,expYear:null,code:null}),t}static fromJSON(t){if(null==t)return null;const e=B.fromJSON(t.cardholderName),i=B.fromJSON(t.brand),r=B.fromJSON(t.number),n=B.fromJSON(t.expMonth),s=B.fromJSON(t.expYear),o=B.fromJSON(t.code);return Object.assign(new vt,t,{cardholderName:e,brand:i,number:r,expMonth:n,expYear:s,code:o})}toSdkCard(){var t,e,i,r,n,s;return{cardholderName:null===(t=this.cardholderName)||void 0===t?void 0:t.toJSON(),brand:null===(e=this.brand)||void 0===e?void 0:e.toJSON(),number:null===(i=this.number)||void 0===i?void 0:i.toJSON(),expMonth:null===(r=this.expMonth)||void 0===r?void 0:r.toJSON(),expYear:null===(n=this.expYear)||void 0===n?void 0:n.toJSON(),code:null===(s=this.code)||void 0===s?void 0:s.toJSON()}}}class gt extends x{constructor(t){super(),null!=t&&(this.type=t.type,this.linkedId=t.linkedId,this.buildDomainModel(this,t,{name:null,value:null},[]))}decrypt(t,e){return this.decryptObj(this,new tt(this),["name","value"],t,e)}toFieldData(){const t=new T;return this.buildDataModel(this,t,{name:null,value:null,type:null,linkedId:null},["type","linkedId"]),t}static fromJSON(t){if(null==t)return null;const e=B.fromJSON(t.name),i=B.fromJSON(t.value);return Object.assign(new gt,t,{name:e,value:i})}toSdkField(){var t,e;return{name:null===(t=this.name)||void 0===t?void 0:t.toJSON(),value:null===(e=this.value)||void 0===e?void 0:e.toJSON(),type:this.type,linkedId:this.linkedId}}}class wt extends x{constructor(t){super(),null!=t&&this.buildDomainModel(this,t,{title:null,firstName:null,middleName:null,lastName:null,address1:null,address2:null,address3:null,city:null,state:null,postalCode:null,country:null,company:null,email:null,phone:null,ssn:null,username:null,passportNumber:null,licenseNumber:null},[])}decrypt(t,e="No Cipher Context",i){return this.decryptObj(this,new rt,["title","firstName","middleName","lastName","address1","address2","address3","city","state","postalCode","country","company","email","phone","ssn","username","passportNumber","licenseNumber"],t,i,"DomainType: Identity; "+e)}toIdentityData(){const t=new F;return this.buildDataModel(this,t,{title:null,firstName:null,middleName:null,lastName:null,address1:null,address2:null,address3:null,city:null,state:null,postalCode:null,country:null,company:null,email:null,phone:null,ssn:null,username:null,passportNumber:null,licenseNumber:null}),t}static fromJSON(t){if(null==t)return null;const e=B.fromJSON(t.title),i=B.fromJSON(t.firstName),r=B.fromJSON(t.middleName),n=B.fromJSON(t.lastName),s=B.fromJSON(t.address1),o=B.fromJSON(t.address2),a=B.fromJSON(t.address3),l=B.fromJSON(t.city),u=B.fromJSON(t.state),c=B.fromJSON(t.postalCode),d=B.fromJSON(t.country),h=B.fromJSON(t.company),p=B.fromJSON(t.email),y=B.fromJSON(t.phone),m=B.fromJSON(t.ssn),f=B.fromJSON(t.username),v=B.fromJSON(t.passportNumber),g=B.fromJSON(t.licenseNumber);return Object.assign(new wt,t,{title:e,firstName:i,middleName:r,lastName:n,address1:s,address2:o,address3:a,city:l,state:u,postalCode:c,country:d,company:h,email:p,phone:y,ssn:m,username:f,passportNumber:v,licenseNumber:g})}toSdkIdentity(){var t,e,i,r,n,s,o,a,l,u,c,d,h,p,y,m,f,v;return{title:null===(t=this.title)||void 0===t?void 0:t.toJSON(),firstName:null===(e=this.firstName)||void 0===e?void 0:e.toJSON(),middleName:null===(i=this.middleName)||void 0===i?void 0:i.toJSON(),lastName:null===(r=this.lastName)||void 0===r?void 0:r.toJSON(),address1:null===(n=this.address1)||void 0===n?void 0:n.toJSON(),address2:null===(s=this.address2)||void 0===s?void 0:s.toJSON(),address3:null===(o=this.address3)||void 0===o?void 0:o.toJSON(),city:null===(a=this.city)||void 0===a?void 0:a.toJSON(),state:null===(l=this.state)||void 0===l?void 0:l.toJSON(),postalCode:null===(u=this.postalCode)||void 0===u?void 0:u.toJSON(),country:null===(c=this.country)||void 0===c?void 0:c.toJSON(),company:null===(d=this.company)||void 0===d?void 0:d.toJSON(),email:null===(h=this.email)||void 0===h?void 0:h.toJSON(),phone:null===(p=this.phone)||void 0===p?void 0:p.toJSON(),ssn:null===(y=this.ssn)||void 0===y?void 0:y.toJSON(),username:null===(m=this.username)||void 0===m?void 0:m.toJSON(),passportNumber:null===(f=this.passportNumber)||void 0===f?void 0:f.toJSON(),licenseNumber:null===(v=this.licenseNumber)||void 0===v?void 0:v.toJSON()}}}class bt extends x{constructor(t){super(),this.credentialId=null,null!=t&&(this.buildDomainModel(this,t,{credentialId:null,keyType:null,keyAlgorithm:null,keyCurve:null,keyValue:null,rpId:null,userHandle:null,userName:null,counter:null,rpName:null,userDisplayName:null,discoverable:null},[]),this.creationDate=null!=t.creationDate?new Date(t.creationDate):null)}decrypt(t,e){return i=this,r=void 0,s=function*(){const i=yield this.decryptObj(this,new nt,["credentialId","keyType","keyAlgorithm","keyCurve","keyValue","rpId","userHandle","userName","rpName","userDisplayName"],t,e),{counter:r}=yield this.decryptObj(this,{counter:""},["counter"],t,e);i.counter=parseInt(r);const{discoverable:n}=yield this.decryptObj(this,{discoverable:""},["discoverable"],t,e);return i.discoverable="true"===n,i.creationDate=this.creationDate,i},new((n=void 0)||(n=Promise))((function(t,e){function o(t){try{l(s.next(t))}catch(t){e(t)}}function a(t){try{l(s.throw(t))}catch(t){e(t)}}function l(e){var i;e.done?t(e.value):(i=e.value,i instanceof n?i:new n((function(t){t(i)}))).then(o,a)}l((s=s.apply(i,r||[])).next())}));var i,r,n,s}toFido2CredentialData(){const t=new J;return t.creationDate=this.creationDate.toISOString(),this.buildDataModel(this,t,{credentialId:null,keyType:null,keyAlgorithm:null,keyCurve:null,keyValue:null,rpId:null,userHandle:null,userName:null,counter:null,rpName:null,userDisplayName:null,discoverable:null}),t}static fromJSON(t){if(null==t)return null;const e=B.fromJSON(t.credentialId),i=B.fromJSON(t.keyType),r=B.fromJSON(t.keyAlgorithm),n=B.fromJSON(t.keyCurve),s=B.fromJSON(t.keyValue),o=B.fromJSON(t.rpId),a=B.fromJSON(t.userHandle),l=B.fromJSON(t.userName),u=B.fromJSON(t.counter),c=B.fromJSON(t.rpName),d=B.fromJSON(t.userDisplayName),h=B.fromJSON(t.discoverable),p=null!=t.creationDate?new Date(t.creationDate):null;return Object.assign(new bt,t,{credentialId:e,keyType:i,keyAlgorithm:r,keyCurve:n,keyValue:s,rpId:o,userHandle:a,userName:l,counter:u,rpName:c,userDisplayName:d,discoverable:h,creationDate:p})}toSdkFido2Credential(){var t,e,i,r,n,s;return{credentialId:null===(t=this.credentialId)||void 0===t?void 0:t.toJSON(),keyType:this.keyType.toJSON(),keyAlgorithm:this.keyAlgorithm.toJSON(),keyCurve:this.keyCurve.toJSON(),keyValue:this.keyValue.toJSON(),rpId:this.rpId.toJSON(),userHandle:null===(e=this.userHandle)||void 0===e?void 0:e.toJSON(),userName:null===(i=this.userName)||void 0===i?void 0:i.toJSON(),counter:this.counter.toJSON(),rpName:null===(r=this.rpName)||void 0===r?void 0:r.toJSON(),userDisplayName:null===(n=this.userDisplayName)||void 0===n?void 0:n.toJSON(),discoverable:null===(s=this.discoverable)||void 0===s?void 0:s.toJSON(),creationDate:this.creationDate.toISOString()}}}class St extends x{constructor(t){super(),null!=t&&(this.match=t.match,this.buildDomainModel(this,t,{uri:null,uriChecksum:null},[]))}decrypt(t,e="No Cipher Context",i){return this.decryptObj(this,new at(this),["uri"],t,i,e)}validateChecksum(t,e,i){return r=this,n=void 0,o=function*(){if(null==this.uriChecksum)return!1;const r=d.getContainerService().getEncryptService(),n=yield r.hash(t,"sha256");return(yield this.uriChecksum.decrypt(e,i))===n},new((s=void 0)||(s=Promise))((function(t,e){function i(t){try{l(o.next(t))}catch(t){e(t)}}function a(t){try{l(o.throw(t))}catch(t){e(t)}}function l(e){var r;e.done?t(e.value):(r=e.value,r instanceof s?r:new s((function(t){t(r)}))).then(i,a)}l((o=o.apply(r,n||[])).next())}));var r,n,s,o}toLoginUriData(){const t=new R;return this.buildDataModel(this,t,{uri:null,uriChecksum:null,match:null},["match"]),t}static fromJSON(t){if(null==t)return null;const e=B.fromJSON(t.uri),i=B.fromJSON(t.uriChecksum);return Object.assign(new St,t,{uri:e,uriChecksum:i})}toSdkLoginUri(){return{uri:this.uri.toJSON(),uriChecksum:this.uriChecksum.toJSON(),match:this.match}}}class Dt extends x{constructor(t){super(),null!=t&&(this.passwordRevisionDate=null!=t.passwordRevisionDate?new Date(t.passwordRevisionDate):null,this.autofillOnPageLoad=t.autofillOnPageLoad,this.buildDomainModel(this,t,{username:null,password:null,totp:null},[]),t.uris&&(this.uris=[],t.uris.forEach((t=>{this.uris.push(new St(t))}))),t.fido2Credentials&&(this.fido2Credentials=t.fido2Credentials.map((t=>new bt(t)))))}decrypt(t,e){return i=this,r=arguments,s=function*(t,e,i="No Cipher Context",r){const n=yield this.decryptObj(this,new ct(this),["username","password","totp"],t,r,`DomainType: Login; ${i}`);if(null!=this.uris){n.uris=[];for(let s=0;s<this.uris.length;s++){if(null==this.uris[s].uri)continue;const o=yield this.uris[s].decrypt(t,i,r);(e||(yield this.uris[s].validateChecksum(o.uri,t,r)))&&n.uris.push(o)}}return null!=this.fido2Credentials&&(n.fido2Credentials=yield Promise.all(this.fido2Credentials.map((e=>e.decrypt(t,r))))),n},new((n=void 0)||(n=Promise))((function(t,e){function o(t){try{l(s.next(t))}catch(t){e(t)}}function a(t){try{l(s.throw(t))}catch(t){e(t)}}function l(e){var i;e.done?t(e.value):(i=e.value,i instanceof n?i:new n((function(t){t(i)}))).then(o,a)}l((s=s.apply(i,r||[])).next())}));var i,r,n,s}toLoginData(){const t=new M;return t.passwordRevisionDate=null!=this.passwordRevisionDate?this.passwordRevisionDate.toISOString():null,t.autofillOnPageLoad=this.autofillOnPageLoad,this.buildDataModel(this,t,{username:null,password:null,totp:null}),null!=this.uris&&this.uris.length>0&&(t.uris=[],this.uris.forEach((e=>{t.uris.push(e.toLoginUriData())}))),null!=this.fido2Credentials&&this.fido2Credentials.length>0&&(t.fido2Credentials=this.fido2Credentials.map((t=>t.toFido2CredentialData()))),t}static fromJSON(t){var e,i,r;if(null==t)return null;const n=B.fromJSON(t.username),s=B.fromJSON(t.password),o=B.fromJSON(t.totp),a=null==t.passwordRevisionDate?null:new Date(t.passwordRevisionDate),l=null===(e=t.uris)||void 0===e?void 0:e.map((t=>St.fromJSON(t))),u=null!==(r=null===(i=t.fido2Credentials)||void 0===i?void 0:i.map((t=>bt.fromJSON(t))))&&void 0!==r?r:[];return Object.assign(new Dt,t,{username:n,password:s,totp:o,passwordRevisionDate:a,uris:l,fido2Credentials:u})}toSdkLogin(){var t,e,i,r,n,s,o;return{uris:null===(t=this.uris)||void 0===t?void 0:t.map((t=>t.toSdkLoginUri())),username:null===(e=this.username)||void 0===e?void 0:e.toJSON(),password:null===(i=this.password)||void 0===i?void 0:i.toJSON(),passwordRevisionDate:null===(r=this.passwordRevisionDate)||void 0===r?void 0:r.toISOString(),totp:null===(n=this.totp)||void 0===n?void 0:n.toJSON(),autofillOnPageLoad:null!==(s=this.autofillOnPageLoad)&&void 0!==s?s:void 0,fido2Credentials:null===(o=this.fido2Credentials)||void 0===o?void 0:o.map((t=>t.toSdkFido2Credential()))}}}class Nt extends x{constructor(t){super(),null!=t&&(this.buildDomainModel(this,t,{password:null}),this.lastUsedDate=new Date(t.lastUsedDate))}decrypt(t,e){return this.decryptObj(this,new dt(this),["password"],t,e,"DomainType: PasswordHistory")}toPasswordHistoryData(){const t=new L;return t.lastUsedDate=this.lastUsedDate.toISOString(),this.buildDataModel(this,t,{password:null}),t}static fromJSON(t){if(null==t)return null;const e=B.fromJSON(t.password),i=null==t.lastUsedDate?null:new Date(t.lastUsedDate);return Object.assign(new Nt,t,{password:e,lastUsedDate:i})}toSdkPasswordHistory(){return{password:this.password.toJSON(),lastUsedDate:this.lastUsedDate.toISOString()}}}class kt extends x{constructor(t){super(),null!=t&&(this.type=t.type)}decrypt(t){return e=this,i=arguments,n=function*(t,e="No Cipher Context",i){return new ht(this)},new((r=void 0)||(r=Promise))((function(t,s){function o(t){try{l(n.next(t))}catch(t){s(t)}}function a(t){try{l(n.throw(t))}catch(t){s(t)}}function l(e){var i;e.done?t(e.value):(i=e.value,i instanceof r?i:new r((function(t){t(i)}))).then(o,a)}l((n=n.apply(e,i||[])).next())}));var e,i,r,n}toSecureNoteData(){const t=new z;return t.type=this.type,t}static fromJSON(t){return null==t?null:Object.assign(new kt,t)}toSdkSecureNote(){return{type:this.type}}}class Ot extends x{constructor(t){super(),null!=t&&this.buildDomainModel(this,t,{privateKey:null,publicKey:null,keyFingerprint:null},[])}decrypt(t,e="No Cipher Context",i){return this.decryptObj(this,new pt,["privateKey","publicKey","keyFingerprint"],t,i,"DomainType: SshKey; "+e)}toSshKeyData(){const t=new j;return this.buildDataModel(this,t,{privateKey:null,publicKey:null,keyFingerprint:null}),t}static fromJSON(t){if(null==t)return null;const e=B.fromJSON(t.privateKey),i=B.fromJSON(t.publicKey),r=B.fromJSON(t.keyFingerprint);return Object.assign(new Ot,t,{privateKey:e,publicKey:i,keyFingerprint:r})}toSdkSshKey(){return{privateKey:this.privateKey.toJSON(),publicKey:this.publicKey.toJSON(),fingerprint:this.keyFingerprint.toJSON()}}}class Ct extends x{constructor(t,e=null){if(super(),this.initializerKey=A.Cipher,null!=t){switch(this.buildDomainModel(this,t,{id:null,organizationId:null,folderId:null,name:null,notes:null,key:null},["id","organizationId","folderId"]),this.type=t.type,this.favorite=t.favorite,this.organizationUseTotp=t.organizationUseTotp,this.edit=t.edit,null!=t.viewPassword?this.viewPassword=t.viewPassword:this.viewPassword=!0,this.permissions=t.permissions,this.revisionDate=null!=t.revisionDate?new Date(t.revisionDate):null,this.collectionIds=t.collectionIds,this.localData=e,this.creationDate=null!=t.creationDate?new Date(t.creationDate):null,this.deletedDate=null!=t.deletedDate?new Date(t.deletedDate):null,this.reprompt=t.reprompt,this.type){case _.Login:this.login=new Dt(t.login);break;case _.SecureNote:this.secureNote=new kt(t.secureNote);break;case _.Card:this.card=new vt(t.card);break;case _.Identity:this.identity=new wt(t.identity);break;case _.SshKey:this.sshKey=new Ot(t.sshKey)}null!=t.attachments?this.attachments=t.attachments.map((t=>new ft(t))):this.attachments=null,null!=t.fields?this.fields=t.fields.map((t=>new gt(t))):this.fields=null,null!=t.passwordHistory?this.passwordHistory=t.passwordHistory.map((t=>new Nt(t))):this.passwordHistory=null}}decrypt(t){return e=this,i=void 0,n=function*(){const e=new yt(this);let i=!0;if(null!=this.key){const r=d.getContainerService().getEncryptService(),n=yield r.unwrapSymmetricKey(this.key,t);if(null==n)return e.name="[error: cannot decrypt]",e.decryptionFailure=!0,e;t=n,i=!1}switch(yield this.decryptObj(this,e,["name","notes"],this.organizationId,t),this.type){case _.Login:e.login=yield this.login.decrypt(this.organizationId,i,`Cipher Id: ${this.id}`,t);break;case _.SecureNote:e.secureNote=yield this.secureNote.decrypt(this.organizationId,`Cipher Id: ${this.id}`,t);break;case _.Card:e.card=yield this.card.decrypt(this.organizationId,`Cipher Id: ${this.id}`,t);break;case _.Identity:e.identity=yield this.identity.decrypt(this.organizationId,`Cipher Id: ${this.id}`,t);break;case _.SshKey:e.sshKey=yield this.sshKey.decrypt(this.organizationId,`Cipher Id: ${this.id}`,t)}if(null!=this.attachments&&this.attachments.length>0){const i=[];for(const e of this.attachments)i.push(yield e.decrypt(this.organizationId,`Cipher Id: ${this.id}`,t));e.attachments=i}if(null!=this.fields&&this.fields.length>0){const i=[];for(const e of this.fields)i.push(yield e.decrypt(this.organizationId,t));e.fields=i}if(null!=this.passwordHistory&&this.passwordHistory.length>0){const i=[];for(const e of this.passwordHistory)i.push(yield e.decrypt(this.organizationId,t));e.passwordHistory=i}return e},new((r=void 0)||(r=Promise))((function(t,s){function o(t){try{l(n.next(t))}catch(t){s(t)}}function a(t){try{l(n.throw(t))}catch(t){s(t)}}function l(e){var i;e.done?t(e.value):(i=e.value,i instanceof r?i:new r((function(t){t(i)}))).then(o,a)}l((n=n.apply(e,i||[])).next())}));var e,i,r,n}toCipherData(){var t;const e=new H;switch(e.id=this.id,e.organizationId=this.organizationId,e.folderId=this.folderId,e.edit=this.edit,e.viewPassword=this.viewPassword,e.organizationUseTotp=this.organizationUseTotp,e.favorite=this.favorite,e.revisionDate=null!=this.revisionDate?this.revisionDate.toISOString():null,e.type=this.type,e.collectionIds=this.collectionIds,e.creationDate=null!=this.creationDate?this.creationDate.toISOString():null,e.deletedDate=null!=this.deletedDate?this.deletedDate.toISOString():null,e.reprompt=this.reprompt,e.key=null===(t=this.key)||void 0===t?void 0:t.encryptedString,e.permissions=this.permissions,this.buildDataModel(this,e,{name:null,notes:null}),e.type){case _.Login:e.login=this.login.toLoginData();break;case _.SecureNote:e.secureNote=this.secureNote.toSecureNoteData();break;case _.Card:e.card=this.card.toCardData();break;case _.Identity:e.identity=this.identity.toIdentityData();break;case _.SshKey:e.sshKey=this.sshKey.toSshKeyData()}return null!=this.fields&&(e.fields=this.fields.map((t=>t.toFieldData()))),null!=this.attachments&&(e.attachments=this.attachments.map((t=>t.toAttachmentData()))),null!=this.passwordHistory&&(e.passwordHistory=this.passwordHistory.map((t=>t.toPasswordHistoryData()))),e}static fromJSON(t){var e,i,r;if(null==t)return null;const n=new Ct,s=B.fromJSON(t.name),o=B.fromJSON(t.notes),a=null==t.creationDate?null:new Date(t.creationDate),l=null==t.revisionDate?null:new Date(t.revisionDate),u=null==t.deletedDate?null:new Date(t.deletedDate),c=null===(e=t.attachments)||void 0===e?void 0:e.map((t=>ft.fromJSON(t))),d=null===(i=t.fields)||void 0===i?void 0:i.map((t=>gt.fromJSON(t))),h=null===(r=t.passwordHistory)||void 0===r?void 0:r.map((t=>Nt.fromJSON(t))),p=B.fromJSON(t.key);switch(Object.assign(n,t,{name:s,notes:o,creationDate:a,revisionDate:l,deletedDate:u,attachments:c,fields:d,passwordHistory:h,key:p}),t.type){case _.Card:n.card=vt.fromJSON(t.card);break;case _.Identity:n.identity=wt.fromJSON(t.identity);break;case _.Login:n.login=Dt.fromJSON(t.login);break;case _.SecureNote:n.secureNote=kt.fromJSON(t.secureNote);break;case _.SshKey:n.sshKey=Ot.fromJSON(t.sshKey)}return n}toSdkCipher(){var t,e,i,r,n,s,o,a,l,u,c,d,h;const p={id:this.id,organizationId:null!==(t=this.organizationId)&&void 0!==t?t:void 0,folderId:null!==(e=this.folderId)&&void 0!==e?e:void 0,collectionIds:null!==(i=this.collectionIds)&&void 0!==i?i:[],key:null===(r=this.key)||void 0===r?void 0:r.toJSON(),name:this.name.toJSON(),notes:null===(n=this.notes)||void 0===n?void 0:n.toJSON(),type:this.type,favorite:null!==(s=this.favorite)&&void 0!==s&&s,organizationUseTotp:null!==(o=this.organizationUseTotp)&&void 0!==o&&o,edit:this.edit,permissions:this.permissions?{delete:this.permissions.delete,restore:this.permissions.restore}:void 0,viewPassword:this.viewPassword,localData:this.localData?{lastUsedDate:this.localData.lastUsedDate?new Date(this.localData.lastUsedDate).toISOString():void 0,lastLaunched:this.localData.lastLaunched?new Date(this.localData.lastLaunched).toISOString():void 0}:void 0,attachments:null===(a=this.attachments)||void 0===a?void 0:a.map((t=>t.toSdkAttachment())),fields:null===(l=this.fields)||void 0===l?void 0:l.map((t=>t.toSdkField())),passwordHistory:null===(u=this.passwordHistory)||void 0===u?void 0:u.map((t=>t.toSdkPasswordHistory())),revisionDate:null===(c=this.revisionDate)||void 0===c?void 0:c.toISOString(),creationDate:null===(d=this.creationDate)||void 0===d?void 0:d.toISOString(),deletedDate:null===(h=this.deletedDate)||void 0===h?void 0:h.toISOString(),reprompt:this.reprompt,login:void 0,identity:void 0,card:void 0,secureNote:void 0,sshKey:void 0};switch(this.type){case _.Login:p.login=this.login.toSdkLogin();break;case _.SecureNote:p.secureNote=this.secureNote.toSdkSecureNote();break;case _.Card:p.card=this.card.toSdkCard();break;case _.Identity:p.identity=this.identity.toSdkIdentity();break;case _.SshKey:p.sshKey=this.sshKey.toSdkSshKey()}return p}}const Bt={[A.Cipher]:Ct.fromJSON,[A.CipherView]:yt.fromJSON};class At{constructor(t){this.buffer=t,this.encryptionType=null,this.dataBytes=null,this.ivBytes=null,this.macBytes=null;const e=t,i=e[0];switch(i){case h.AesCbc256_HmacSha256_B64:{const t=50;e.length<t&&this.throwDecryptionError(),this.ivBytes=e.slice(1,17),this.macBytes=e.slice(17,49),this.dataBytes=e.slice(49);break}case h.AesCbc256_B64:{const t=18;e.length<t&&this.throwDecryptionError(),this.ivBytes=e.slice(1,17),this.dataBytes=e.slice(17);break}default:this.throwDecryptionError()}this.encryptionType=i}throwDecryptionError(){throw new Error("Error parsing encrypted ArrayBuffer: data is corrupted or has an invalid format.")}static fromParts(t,e,i,r){if(null==t||null==e||null==i)throw new Error("encryptionType, iv, and data must be provided");switch(t){case h.AesCbc256_B64:case h.AesCbc256_HmacSha256_B64:At.validateIvLength(e),At.validateMacLength(t,r);break;default:throw new Error(`Unknown EncryptionType ${t} for EncArrayBuffer.fromParts`)}let n=0;null!=r&&(n=r.length);const s=new Uint8Array(1+e.byteLength+n+i.byteLength);return s.set([t],0),s.set(e,1),null!=r&&s.set(r,1+e.byteLength),s.set(i,1+e.byteLength+n),new At(s)}static fromResponse(t){return e=this,i=void 0,n=function*(){const e=yield t.arrayBuffer();if(null==e)throw new Error("Cannot create EncArrayBuffer from Response - Response is empty");return new At(new Uint8Array(e))},new((r=void 0)||(r=Promise))((function(t,s){function o(t){try{l(n.next(t))}catch(t){s(t)}}function a(t){try{l(n.throw(t))}catch(t){s(t)}}function l(e){var i;e.done?t(e.value):(i=e.value,i instanceof r?i:new r((function(t){t(i)}))).then(o,a)}l((n=n.apply(e,i||[])).next())}));var e,i,r,n}static fromB64(t){const e=d.fromB64ToArray(t);return new At(e)}static validateIvLength(t){if(null==t||16!==t.length)throw new Error("Invalid IV length")}static validateMacLength(t,e){switch(t){case h.AesCbc256_B64:if(null!=e)throw new Error("mac must not be provided for AesCbc256_B64");break;case h.AesCbc256_HmacSha256_B64:if(null==e||32!==e.length)throw new Error("Invalid MAC length");break;default:throw new Error("Invalid encryption type and mac combination")}}}class Et{}var xt,_t=i(6414);!function(t){t.SeparateCustomRolePermissions="pm-19917-separate-custom-role-permissions",t.OptimizeNestedTraverseTypescript="pm-21695-optimize-nested-traverse-typescript",t.PM16117_ChangeExistingPasswordRefactor="pm-16117-change-existing-password-refactor",t.PM9115_TwoFactorExtensionDataPersistence="pm-9115-two-factor-extension-data-persistence",t.BlockBrowserInjectionsByDomain="block-browser-injections-by-domain",t.EnableNewCardCombinedExpiryAutofill="enable-new-card-combined-expiry-autofill",t.NotificationRefresh="notification-refresh",t.UseTreeWalkerApiForPageDetailsCollection="use-tree-walker-api-for-page-details-collection",t.MacOsNativeCredentialSync="macos-native-credential-sync",t.TrialPaymentOptional="PM-8163-trial-payment",t.PM12276_BreadcrumbEventLogs="pm-12276-breadcrumbing-for-business-features",t.PM17772_AdminInitiatedSponsorships="pm-17772-admin-initiated-sponsorships",t.PM19956_RequireProviderPaymentMethodDuringSetup="pm-19956-require-provider-payment-method-during-setup",t.UseOrganizationWarningsService="use-organization-warnings-service",t.EnableRiskInsightsNotifications="enable-risk-insights-notifications",t.PrivateKeyRegeneration="pm-12241-private-key-regeneration",t.PM4154_BulkEncryptionService="PM-4154-bulk-encryption-service",t.UseSDKForDecryption="use-sdk-for-decryption",t.PM17987_BlockType0="pm-17987-block-type-0",t.EnrollAeadOnKeyRotation="enroll-aead-on-key-rotation",t.ItemShare="item-share",t.DesktopSendUIRefresh="desktop-send-ui-refresh",t.PM8851_BrowserOnboardingNudge="pm-8851-browser-onboarding-nudge",t.PM9111ExtensionPersistAddEditForm="pm-9111-extension-persist-add-edit-form",t.PM19941MigrateCipherDomainToSdk="pm-19941-migrate-cipher-domain-to-sdk",t.CipherKeyEncryption="cipher-key-encryption",t.PM18520_UpdateDesktopCipherForm="pm-18520-desktop-cipher-forms",t.EndUserNotifications="pm-10609-end-user-notifications",t.RemoveCardItemTypePolicy="pm-16442-remove-card-item-type-policy",t.IpcChannelFramework="ipc-channel-framework"}(xt||(xt={}));const Pt=!1,Kt={[xt.SeparateCustomRolePermissions]:Pt,[xt.OptimizeNestedTraverseTypescript]:Pt,[xt.BlockBrowserInjectionsByDomain]:Pt,[xt.EnableNewCardCombinedExpiryAutofill]:Pt,[xt.NotificationRefresh]:Pt,[xt.UseTreeWalkerApiForPageDetailsCollection]:Pt,[xt.MacOsNativeCredentialSync]:Pt,[xt.EnableRiskInsightsNotifications]:Pt,[xt.ItemShare]:Pt,[xt.DesktopSendUIRefresh]:Pt,[xt.PM8851_BrowserOnboardingNudge]:Pt,[xt.PM9111ExtensionPersistAddEditForm]:Pt,[xt.CipherKeyEncryption]:Pt,[xt.PM18520_UpdateDesktopCipherForm]:Pt,[xt.EndUserNotifications]:Pt,[xt.PM19941MigrateCipherDomainToSdk]:Pt,[xt.RemoveCardItemTypePolicy]:Pt,[xt.PM16117_ChangeExistingPasswordRefactor]:Pt,[xt.PM9115_TwoFactorExtensionDataPersistence]:Pt,[xt.TrialPaymentOptional]:Pt,[xt.PM12276_BreadcrumbEventLogs]:Pt,[xt.PM17772_AdminInitiatedSponsorships]:Pt,[xt.PM19956_RequireProviderPaymentMethodDuringSetup]:Pt,[xt.UseOrganizationWarningsService]:Pt,[xt.PrivateKeyRegeneration]:Pt,[xt.PM4154_BulkEncryptionService]:Pt,[xt.UseSDKForDecryption]:Pt,[xt.PM17987_BlockType0]:Pt,[xt.EnrollAeadOnKeyRotation]:Pt,[xt.IpcChannelFramework]:Pt};function Ut(t,e){return null==(null==t?void 0:t.featureStates)||null==t.featureStates[e]?Kt[e]:t.featureStates[e]}class It extends Error{constructor(t){super(`SDK loading failed: ${t}`)}}class Tt{loadAndInit(){return t=this,e=void 0,r=function*(){try{yield this.load(),(0,_t.Geh)(),Tt.markAsReady()}catch(t){Tt.markAsFailed(t)}},new((i=void 0)||(i=Promise))((function(n,s){function o(t){try{l(r.next(t))}catch(t){s(t)}}function a(t){try{l(r.throw(t))}catch(t){s(t)}}function l(t){var e;t.done?n(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(o,a)}l((r=r.apply(t,e||[])).next())}));var t,e,i,r}}Tt.Ready=new Promise(((t,e)=>{Tt.markAsReady=t,Tt.markAsFailed=t=>e(new It(t))}));var Ft=function(t,e,i,r){return new(i||(i=Promise))((function(n,s){function o(t){try{l(r.next(t))}catch(t){s(t)}}function a(t){try{l(r.throw(t))}catch(t){s(t)}}function l(t){var e;t.done?n(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(o,a)}l((r=r.apply(t,e||[])).next())}))};class Jt{constructor(t,e,i){this.cryptoFunctionService=t,this.logService=e,this.logMacFailures=i,this.useSDKForDecryption=Kt[xt.UseSDKForDecryption],this.blockType0=Kt[xt.PM17987_BlockType0]}encryptString(t,e){return Ft(this,void 0,void 0,(function*(){return this.encrypt(t,e)}))}encryptBytes(t,e){return Ft(this,void 0,void 0,(function*(){return this.encrypt(t,e)}))}encryptFileData(t,e){return Ft(this,void 0,void 0,(function*(){return this.encryptToBytes(t,e)}))}decryptString(t,e){return Ft(this,void 0,void 0,(function*(){return this.decryptToUtf8(t,e)}))}decryptBytes(t,e){return Ft(this,void 0,void 0,(function*(){return this.decryptToBytes(t,e)}))}decryptFileData(t,e){return Ft(this,void 0,void 0,(function*(){return this.decryptToBytes(t,e)}))}wrapDecapsulationKey(t,e){return Ft(this,void 0,void 0,(function*(){if(null==t)throw new Error("No decapsulation key provided for wrapping.");if(null==e)throw new Error("No wrappingKey provided for wrapping.");return yield this.encryptUint8Array(t,e)}))}wrapEncapsulationKey(t,e){return Ft(this,void 0,void 0,(function*(){if(null==t)throw new Error("No encapsulation key provided for wrapping.");if(null==e)throw new Error("No wrappingKey provided for wrapping.");return yield this.encryptUint8Array(t,e)}))}wrapSymmetricKey(t,e){return Ft(this,void 0,void 0,(function*(){if(null==t)throw new Error("No keyToBeWrapped provided for wrapping.");if(null==e)throw new Error("No wrappingKey provided for wrapping.");return yield this.encryptUint8Array(t.toEncoded(),e)}))}unwrapDecapsulationKey(t,e){return Ft(this,void 0,void 0,(function*(){return this.decryptBytes(t,e)}))}unwrapEncapsulationKey(t,e){return Ft(this,void 0,void 0,(function*(){return this.decryptBytes(t,e)}))}unwrapSymmetricKey(t,e){return Ft(this,void 0,void 0,(function*(){return new D(yield this.decryptBytes(t,e))}))}hash(t,e){return Ft(this,void 0,void 0,(function*(){const i=yield this.cryptoFunctionService.hash(t,e);return d.fromBufferToB64(i)}))}onServerConfigChange(t){const e=this.useSDKForDecryption;this.useSDKForDecryption=Ut(t,xt.UseSDKForDecryption),this.logService.debug("[EncryptService] Updated sdk decryption flag",e,this.useSDKForDecryption),this.blockType0=Ut(t,xt.PM17987_BlockType0)}encrypt(t,e){return Ft(this,void 0,void 0,(function*(){if(null==e)throw new Error("No encryption key provided.");if(this.blockType0&&e.inner().type===h.AesCbc256_B64)throw new Error("Type 0 encryption is not supported.");return null==t?Promise.resolve(null):"string"==typeof t?this.encryptUint8Array(d.fromUtf8ToArray(t),e):this.encryptUint8Array(t,e)}))}encryptUint8Array(t,e){return Ft(this,void 0,void 0,(function*(){if(null==e)throw new Error("No encryption key provided.");if(this.blockType0&&e.inner().type===h.AesCbc256_B64)throw new Error("Type 0 encryption is not supported.");if(null==t)return Promise.resolve(null);const i=e.inner();if(i.type===h.AesCbc256_HmacSha256_B64){const e=yield this.aesEncrypt(t,i),r=d.fromBufferToB64(e.iv),n=d.fromBufferToB64(e.data),s=d.fromBufferToB64(e.mac);return new B(i.type,n,r,s)}if(i.type===h.AesCbc256_B64){const e=yield this.aesEncryptLegacy(t,i),r=d.fromBufferToB64(e.iv),n=d.fromBufferToB64(e.data);return new B(i.type,n,r)}}))}encryptToBytes(t,e){return Ft(this,void 0,void 0,(function*(){if(null==e)throw new Error("No encryption key provided.");if(this.blockType0&&e.inner().type===h.AesCbc256_B64)throw new Error("Type 0 encryption is not supported.");const i=e.inner();if(i.type===h.AesCbc256_HmacSha256_B64){const e=yield this.aesEncrypt(t,i),r=e.mac.length,n=new Uint8Array(1+e.iv.byteLength+r+e.data.byteLength);return n.set([i.type]),n.set(new Uint8Array(e.iv),1),n.set(new Uint8Array(e.mac),1+e.iv.byteLength),n.set(new Uint8Array(e.data),1+e.iv.byteLength+r),new At(n)}if(i.type===h.AesCbc256_B64){const e=yield this.aesEncryptLegacy(t,i),r=new Uint8Array(1+e.iv.byteLength+e.data.byteLength);return r.set([i.type]),r.set(new Uint8Array(e.iv),1),r.set(new Uint8Array(e.data),1+e.iv.byteLength),new At(r)}}))}decryptToUtf8(t,e){return Ft(this,arguments,void 0,(function*(t,e,i="no context"){if(this.useSDKForDecryption){if(this.logService.debug("decrypting with SDK"),null==t||null==t.encryptedString)throw new Error("encString is null or undefined");return yield Tt.Ready,_t.IEs.symmetric_decrypt(t.encryptedString,e.toEncoded())}if(this.logService.debug("decrypting with javascript"),null==e)throw new Error("No key provided for decryption.");const r=e.inner();if(t.encryptionType!==r.type)return this.logDecryptError("Key encryption type does not match payload encryption type",r.type,t.encryptionType,i),null;if(r.type===h.AesCbc256_HmacSha256_B64){const n=this.cryptoFunctionService.aesDecryptFastParameters(t.data,t.iv,t.mac,e),s=yield this.cryptoFunctionService.hmacFast(n.macData,n.macKey,"sha256");return(yield this.cryptoFunctionService.compareFast(n.mac,s))?yield this.cryptoFunctionService.aesDecryptFast({mode:"cbc",parameters:n}):(this.logMacFailed("decryptToUtf8 MAC comparison failed. Key or payload has changed.",r.type,t.encryptionType,i),null)}if(r.type===h.AesCbc256_B64){const i=this.cryptoFunctionService.aesDecryptFastParameters(t.data,t.iv,void 0,e);return yield this.cryptoFunctionService.aesDecryptFast({mode:"cbc",parameters:i})}throw new Error("Unsupported encryption type")}))}decryptToBytes(t,e){return Ft(this,arguments,void 0,(function*(t,e,i="no context"){if(this.useSDKForDecryption){if(this.logService.debug("[EncryptService] Decrypting bytes with SDK"),null==t.encryptionType||null==t.ivBytes||null==t.dataBytes)throw new Error("Cannot decrypt, missing type, IV, or data bytes.");const i=At.fromParts(t.encryptionType,t.ivBytes,t.dataBytes,t.macBytes).buffer;return yield Tt.Ready,_t.IEs.symmetric_decrypt_array_buffer(i,e.toEncoded())}if(this.logService.debug("[EncryptService] Decrypting bytes with javascript"),null==e)throw new Error("No encryption key provided.");if(null==t)throw new Error("Nothing provided for decryption.");const r=e.inner();if(t.encryptionType!==r.type)return this.logDecryptError("Encryption key type mismatch",r.type,t.encryptionType,i),null;if(r.type===h.AesCbc256_HmacSha256_B64){if(null==t.macBytes)return this.logDecryptError("Mac missing",r.type,t.encryptionType,i),null;const e=new Uint8Array(t.ivBytes.byteLength+t.dataBytes.byteLength);e.set(new Uint8Array(t.ivBytes),0),e.set(new Uint8Array(t.dataBytes),t.ivBytes.byteLength);const n=yield this.cryptoFunctionService.hmac(e,r.authenticationKey,"sha256");return(yield this.cryptoFunctionService.compare(t.macBytes,n))?yield this.cryptoFunctionService.aesDecrypt(t.dataBytes,t.ivBytes,r.encryptionKey,"cbc"):(this.logMacFailed("MAC comparison failed. Key or payload has changed.",r.type,t.encryptionType,i),null)}return r.type===h.AesCbc256_B64?yield this.cryptoFunctionService.aesDecrypt(t.dataBytes,t.ivBytes,r.encryptionKey,"cbc"):void 0}))}encapsulateKeyUnsigned(t,e){return Ft(this,void 0,void 0,(function*(){if(null==t)throw new Error("No sharedKey provided for encapsulation");return yield this.rsaEncrypt(t.toEncoded(),e)}))}decapsulateKeyUnsigned(t,e){return Ft(this,void 0,void 0,(function*(){const i=yield this.rsaDecrypt(t,e);return new D(i)}))}decryptItems(t,e){return Ft(this,void 0,void 0,(function*(){if(null==t||t.length<1)return[];const i=[];for(let r=0;r<t.length;r++)i.push(yield t[r].decrypt(e));return i}))}aesEncrypt(t,e){return Ft(this,void 0,void 0,(function*(){const i=new Et;i.iv=yield this.cryptoFunctionService.randomBytes(16),i.data=yield this.cryptoFunctionService.aesEncrypt(t,i.iv,e.encryptionKey);const r=new Uint8Array(i.iv.byteLength+i.data.byteLength);return r.set(new Uint8Array(i.iv),0),r.set(new Uint8Array(i.data),i.iv.byteLength),i.mac=yield this.cryptoFunctionService.hmac(r,e.authenticationKey,"sha256"),i}))}aesEncryptLegacy(t,e){return Ft(this,void 0,void 0,(function*(){const i=new Et;return i.iv=yield this.cryptoFunctionService.randomBytes(16),i.data=yield this.cryptoFunctionService.aesEncrypt(t,i.iv,e.encryptionKey),i}))}logDecryptError(t,e,i,r){this.logService.error(`[Encrypt service] ${t} Key type ${p(e)} Payload type ${p(i)} Decrypt context: ${r}`)}logMacFailed(t,e,i,r){this.logMacFailures&&this.logDecryptError(t,e,i,r)}rsaEncrypt(t,e){return Ft(this,void 0,void 0,(function*(){if(null==t)throw new Error("No data provided for encryption.");if(null==e)throw new Error("No public key provided for encryption.");const i=yield this.cryptoFunctionService.rsaEncrypt(t,e,"sha1");return new B(h.Rsa2048_OaepSha1_B64,d.fromBufferToB64(i))}))}rsaDecrypt(t,e){return Ft(this,void 0,void 0,(function*(){if(null==t)throw new Error("[Encrypt service] rsaDecrypt: No data provided for decryption.");let i;switch(t.encryptionType){case h.Rsa2048_OaepSha1_B64:case h.Rsa2048_OaepSha1_HmacSha256_B64:i="sha1";break;case h.Rsa2048_OaepSha256_B64:case h.Rsa2048_OaepSha256_HmacSha256_B64:i="sha256";break;default:throw new Error("Invalid encryption type.")}if(null==e)throw new Error("[Encrypt service] rsaDecrypt: No private key provided for decryption.");return this.cryptoFunctionService.rsaDecrypt(t.dataBytes,e,i)}))}}var Rt=i(637),Mt=i(1828),Lt=function(t,e,i,r){return new(i||(i=Promise))((function(n,s){function o(t){try{l(r.next(t))}catch(t){s(t)}}function a(t){try{l(r.throw(t))}catch(t){s(t)}}function l(t){var e;t.done?n(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(o,a)}l((r=r.apply(t,e||[])).next())}))};class zt{constructor(t){var e;if(null==(null===(e=null==t?void 0:t.crypto)||void 0===e?void 0:e.subtle))throw new Error("Could not instantiate WebCryptoFunctionService. Could not locate Subtle crypto.");this.crypto=t.crypto,this.subtle=this.crypto.subtle,this.wasmSupported=this.checkIfWasmSupported()}pbkdf2(t,e,i,r){return Lt(this,void 0,void 0,(function*(){const n="sha256"===i?256:512,s=this.toBuf(t),o={name:"PBKDF2",salt:this.toBuf(e),iterations:r,hash:{name:this.toWebCryptoAlgorithm(i)}},a=yield this.subtle.importKey("raw",s,{name:"PBKDF2"},!1,["deriveBits"]),l=yield this.subtle.deriveBits(o,a,n);return new Uint8Array(l)}))}argon2(t,e,i,r,n){return Lt(this,void 0,void 0,(function*(){if(!this.wasmSupported)throw"Webassembly support is required for the Argon2 KDF feature.";const s=new Uint8Array(this.toBuf(t)),o=new Uint8Array(this.toBuf(e)),a=yield Rt.hash({pass:s,salt:o,time:i,mem:r,parallelism:n,hashLen:32,type:Rt.ArgonType.Argon2id});return Rt.unloadRuntime(),a.hash}))}hkdf(t,e,i,r,n){return Lt(this,void 0,void 0,(function*(){const s={name:"HKDF",salt:this.toBuf(e),info:this.toBuf(i),hash:{name:this.toWebCryptoAlgorithm(n)}},o=yield this.subtle.importKey("raw",t,{name:"HKDF"},!1,["deriveBits"]),a=yield this.subtle.deriveBits(s,o,8*r);return new Uint8Array(a)}))}hkdfExpand(t,e,i,r){return Lt(this,void 0,void 0,(function*(){const n="sha256"===r?32:64;if(i>255*n)throw new Error("outputByteSize is too large.");if(new Uint8Array(t).length<n)throw new Error("prk is too small.");const s=this.toBuf(e),o=new Uint8Array(s);let a=0,l=new Uint8Array(0);const u=Math.ceil(i/n),c=new Uint8Array(u*n);for(let e=0;e<u;e++){const n=new Uint8Array(l.length+o.length+1);if(n.set(l),n.set(o,l.length),n.set([e+1],n.length-1),l=new Uint8Array(yield this.hmac(n,t,r)),c.set(l,a),a+=l.length,a>=i)break}return c.slice(0,i)}))}hash(t,e){return Lt(this,void 0,void 0,(function*(){if("md5"===e){const e=Mt.md.md5.create(),i=this.toByteString(t);return e.update(i,"raw"),d.fromByteStringToArray(e.digest().data)}const i=this.toBuf(t),r=yield this.subtle.digest({name:this.toWebCryptoAlgorithm(e)},i);return new Uint8Array(r)}))}hmac(t,e,i){return Lt(this,void 0,void 0,(function*(){const r={name:"HMAC",hash:{name:this.toWebCryptoAlgorithm(i)}},n=yield this.subtle.importKey("raw",e,r,!1,["sign"]),s=yield this.subtle.sign(r,n,t);return new Uint8Array(s)}))}compare(t,e){return Lt(this,void 0,void 0,(function*(){const i=yield this.randomBytes(32),r={name:"HMAC",hash:{name:"SHA-256"}},n=yield this.subtle.importKey("raw",i,r,!1,["sign"]),s=yield this.subtle.sign(r,n,t),o=yield this.subtle.sign(r,n,e);if(s.byteLength!==o.byteLength)return!1;const a=new Uint8Array(s),l=new Uint8Array(o);for(let t=0;t<l.length;t++)if(a[t]!==l[t])return!1;return!0}))}hmacFast(t,e,i){const r=Mt.hmac.create();r.start(i,e),r.update(t);const n=r.digest().getBytes();return Promise.resolve(n)}compareFast(t,e){return Lt(this,void 0,void 0,(function*(){const i=yield this.randomBytes(32),r=new Uint32Array(i),n=Mt.util.createBuffer();for(let t=0;t<r.length;t++)n.putInt32(r[t]);const s=n.getBytes(),o=Mt.hmac.create();o.start("sha256",s),o.update(t);const a=o.digest().getBytes();return o.start("sha256",null),o.update(e),a===o.digest().getBytes()}))}aesEncrypt(t,e,i){return Lt(this,void 0,void 0,(function*(){const r=yield this.subtle.importKey("raw",i,{name:"AES-CBC"},!1,["encrypt"]),n=yield this.subtle.encrypt({name:"AES-CBC",iv:e},r,t);return new Uint8Array(n)}))}aesDecryptFastParameters(t,e,i,r){const n=r.inner();if(n.type===h.AesCbc256_B64)return{iv:Mt.util.decode64(e),data:Mt.util.decode64(t),encKey:Mt.util.createBuffer(n.encryptionKey).getBytes()};if(n.type===h.AesCbc256_HmacSha256_B64){const r=Mt.util.decode64(e)+Mt.util.decode64(t);return{iv:Mt.util.decode64(e),data:Mt.util.decode64(t),encKey:Mt.util.createBuffer(n.encryptionKey).getBytes(),macKey:Mt.util.createBuffer(n.authenticationKey).getBytes(),mac:Mt.util.decode64(i),macData:r}}throw new Error("Unsupported encryption type.")}aesDecryptFast({mode:t,parameters:e}){const i=Mt.cipher.createDecipher(this.toWebCryptoAesMode(t),e.encKey),r={};"cbc"===t&&(r.iv=e.iv);const n=Mt.util.createBuffer(e.data);i.start(r),i.update(n),i.finish();const s=i.output.toString();return Promise.resolve(s)}aesDecrypt(t,e,i,r){return Lt(this,void 0,void 0,(function*(){if("ecb"===r){const e={data:this.toByteString(t),encKey:this.toByteString(i)},r=yield this.aesDecryptFast({mode:"ecb",parameters:e});return d.fromByteStringToArray(r)}const n=yield this.subtle.importKey("raw",i,{name:"AES-CBC"},!1,["decrypt"]);if(null==e)throw new Error("IV is required for CBC mode.");const s=yield this.subtle.decrypt({name:"AES-CBC",iv:e},n,t);return new Uint8Array(s)}))}rsaEncrypt(t,e,i){return Lt(this,void 0,void 0,(function*(){const r={name:"RSA-OAEP",hash:{name:this.toWebCryptoAlgorithm(i)}},n=yield this.subtle.importKey("spki",e,r,!1,["encrypt"]),s=yield this.subtle.encrypt(r,n,t);return new Uint8Array(s)}))}rsaDecrypt(t,e,i){return Lt(this,void 0,void 0,(function*(){const r={name:"RSA-OAEP",hash:{name:this.toWebCryptoAlgorithm(i)}},n=yield this.subtle.importKey("pkcs8",e,r,!1,["decrypt"]),s=yield this.subtle.decrypt(r,n,t);return new Uint8Array(s)}))}rsaExtractPublicKey(t){return Lt(this,void 0,void 0,(function*(){const e={name:"RSA-OAEP",hash:{name:this.toWebCryptoAlgorithm("sha1")}},i=yield this.subtle.importKey("pkcs8",t,e,!0,["decrypt"]),r=yield this.subtle.exportKey("jwk",i),n={kty:"RSA",e:r.e,n:r.n,alg:"RSA-OAEP",ext:!0},s=yield this.subtle.importKey("jwk",n,e,!0,["encrypt"]),o=yield this.subtle.exportKey("spki",s);return new Uint8Array(o)}))}aesGenerateKey(){return Lt(this,arguments,void 0,(function*(t=960){if(512===t){const t=yield this.aesGenerateKey(256),e=yield this.aesGenerateKey(256);return new Uint8Array([...t,...e])}const e={name:"AES-CBC",length:t},i=yield this.subtle.generateKey(e,!0,["encrypt","decrypt"]),r=yield this.subtle.exportKey("raw",i);return new Uint8Array(r)}))}rsaGenerateKeyPair(t){return Lt(this,void 0,void 0,(function*(){const e={name:"RSA-OAEP",modulusLength:t,publicExponent:new Uint8Array([1,0,1]),hash:{name:this.toWebCryptoAlgorithm("sha1")}},i=yield this.subtle.generateKey(e,!0,["encrypt","decrypt"]),r=yield this.subtle.exportKey("spki",i.publicKey),n=yield this.subtle.exportKey("pkcs8",i.privateKey);return[new Uint8Array(r),new Uint8Array(n)]}))}randomBytes(t){const e=new Uint8Array(t);return this.crypto.getRandomValues(e),Promise.resolve(e)}toBuf(t){let e;return e="string"==typeof t?d.fromUtf8ToArray(t):t,e}toByteString(t){let e;return e="string"==typeof t?Mt.util.encodeUtf8(t):d.fromBufferToByteString(t),e}toWebCryptoAlgorithm(t){if("md5"===t)throw new Error("MD5 is not supported in WebCrypto.");return"sha1"===t?"SHA-1":"sha256"===t?"SHA-256":"SHA-512"}toWebCryptoAesMode(t){return"cbc"===t?"AES-CBC":"AES-ECB"}checkIfWasmSupported(){try{if("object"==typeof WebAssembly&&"function"==typeof WebAssembly.instantiate){const t=new WebAssembly.Module(Uint8Array.of(0,97,115,109,1,0,0,0));if(t instanceof WebAssembly.Module)return new WebAssembly.Instance(t)instanceof WebAssembly.Instance}}catch(t){return!1}return!1}}var jt=function(t,e,i,r){return new(i||(i=Promise))((function(n,s){function o(t){try{l(r.next(t))}catch(t){s(t)}}function a(t){try{l(r.throw(t))}catch(t){s(t)}}function l(t){var e;t.done?n(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(o,a)}l((r=r.apply(t,e||[])).next())}))};const Ht=self;let Wt,Vt,$t=!1;Ht.addEventListener("message",(t=>jt(void 0,void 0,void 0,(function*(){$t||function(){const t=new zt(self);Vt=new N(!1),Wt=new Jt(t,Vt,!0),new k(null,Wt).attachToGlobal(self),$t=!0}();const e=JSON.parse(t.data);switch(e.command){case"decrypt":return yield function(t){return jt(this,void 0,void 0,(function*(){const e=D.fromJSON(t.key),i=t.items.map((t=>{var e;return(e=t.initializerKey,Bt[e])(t)})),r=yield Wt.decryptItems(i,e);Ht.postMessage({id:t.id,items:JSON.stringify(r)})}))}(e);case"updateConfig":{const t=e.newConfig;return yield function(t){return jt(this,void 0,void 0,(function*(){Wt.onServerConfigChange(n.fromJSON(t))}))}(t)}default:Vt.error("[EncryptWorker] unknown worker command",e.command,e)}}))))},4836:()=>{},6973:()=>{},9701:()=>{}},r={};function n(t){var e=r[t];if(void 0!==e)return e.exports;var s=r[t]={id:t,loaded:!1,exports:{}};return i[t].call(s.exports,s,s.exports,n),s.loaded=!0,s.exports}n.m=i,n.c=r,n.x=()=>{var t=n.O(void 0,[576],(()=>n(2763)));return n.O(t)},t=[],n.O=(e,i,r,s)=>{if(!i){var o=1/0;for(c=0;c<t.length;c++){for(var[i,r,s]=t[c],a=!0,l=0;l<i.length;l++)(!1&s||o>=s)&&Object.keys(n.O).every((t=>n.O[t](i[l])))?i.splice(l--,1):(a=!1,s<o&&(o=s));if(a){t.splice(c--,1);var u=r();void 0!==u&&(e=u)}}return e}s=s||0;for(var c=t.length;c>0&&t[c-1][2]>s;c--)t[c]=t[c-1];t[c]=[i,r,s]},n.d=(t,e)=>{for(var i in e)n.o(e,i)&&!n.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},n.f={},n.e=t=>Promise.all(Object.keys(n.f).reduce(((e,i)=>(n.f[i](t,e),e)),[])),n.u=t=>t+".background.js",n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),n.hmd=t=>((t=Object.create(t)).children||(t.children=[]),Object.defineProperty(t,"exports",{enumerable:!0,set:()=>{throw new Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+t.id)}}),t),n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},(()=>{var t;n.g.importScripts&&(t=n.g.location+"");var e=n.g.document;if(!t&&e&&(e.currentScript&&"SCRIPT"===e.currentScript.tagName.toUpperCase()&&(t=e.currentScript.src),!t)){var i=e.getElementsByTagName("script");if(i.length)for(var r=i.length-1;r>-1&&(!t||!/^http(s?):/.test(t));)t=i[r--].src}if(!t)throw new Error("Automatic publicPath is not supported in this browser");t=t.replace(/^blob:/,"").replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),n.p=t})(),(()=>{var t={357:1};n.f.i=(e,i)=>{t[e]||importScripts(n.p+n.u(e))};var e=self.webpackChunk_bitwarden_browser=self.webpackChunk_bitwarden_browser||[],i=e.push.bind(e);e.push=e=>{var[r,s,o]=e;for(var a in s)n.o(s,a)&&(n.m[a]=s[a]);for(o&&o(n);r.length;)t[r.pop()]=1;i(e)}})(),e=n.x,n.x=()=>n.e(576).then(e),n.x()})();