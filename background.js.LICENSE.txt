/* @license
Papa Parse
v5.5.3
https://github.com/mholt/PapaParse
License: MIT
*/

/*!

JSZip v3.10.1 - A JavaScript class for generating and reading zip files
<http://stuartk.com/jszip>

(c) 2009-2016 <PERSON> <stuart [at] stuartk.com>
Dual licenced under the MIT license or GPLv3. See https://raw.github.com/Stuk/jszip/main/LICENSE.markdown.

JSZip uses the library pako released under the MIT license :
https://github.com/nodeca/pako/blob/main/LICENSE
*/

/*!
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */

/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> <https://feross.org>
 * @license  MIT
 */

/*!
 * lunr.Builder
 * Copyright (C) 2020 Oliver Nightingale
 */

/*!
 * lunr.Index
 * Copyright (C) 2020 Oliver Nightingale
 */

/*!
 * lunr.Pipeline
 * Copyright (C) 2020 Oliver Nightingale
 */

/*!
 * lunr.Set
 * Copyright (C) 2020 Oliver Nightingale
 */

/*!
 * lunr.TokenSet
 * Copyright (C) 2020 Oliver Nightingale
 */

/*!
 * lunr.Vector
 * Copyright (C) 2020 Oliver Nightingale
 */

/*!
 * lunr.stemmer
 * Copyright (C) 2020 Oliver Nightingale
 * Includes code from - http://tartarus.org/~martin/PorterStemmer/js.txt
 */

/*!
 * lunr.stopWordFilter
 * Copyright (C) 2020 Oliver Nightingale
 */

/*!
 * lunr.tokenizer
 * Copyright (C) 2020 Oliver Nightingale
 */

/*!
 * lunr.trimmer
 * Copyright (C) 2020 Oliver Nightingale
 */

/*!
 * lunr.utils
 * Copyright (C) 2020 Oliver Nightingale
 */

/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */

/**
 * @license Angular v19.2.14
 * (c) 2010-2025 Google LLC. https://angular.io/
 * License: MIT
 */

/**
 * lunr - http://lunrjs.com - A bit like Solr, but much smaller and not as bright - 2.3.9
 * Copyright (C) 2020 Oliver Nightingale
 * @license MIT
 */
