{"version": 3, "file": "offscreen-document/offscreen-document.js", "mappings": "yBAEA,IAAYA,GAAZ,SAAYA,GACV,qBACA,mBACA,yBACA,oBACD,CALD,CAAYA,IAAAA,EAAY,KCGjB,MAAMC,EAGX,WAAAC,CACYC,EACAC,EAA2C,MAD3C,KAAAD,MAAAA,EACA,KAAAC,OAAAA,EAJF,KAAAC,UAA2C,IAAIC,GAKtD,CAEH,KAAAC,CAAMC,KAAkBC,GACjBC,KAAKP,OAGVO,KAAKC,MAAMX,EAAaY,MAAOJ,KAAYC,EAC7C,CAEA,IAAAI,CAAKL,KAAkBC,GACrBC,KAAKC,MAAMX,EAAac,KAAMN,KAAYC,EAC5C,CAEA,OAAAM,CAAQP,KAAkBC,GACxBC,KAAKC,MAAMX,EAAagB,QAASR,KAAYC,EAC/C,CAEA,KAAAQ,CAAMT,KAAkBC,GACtBC,KAAKC,MAAMX,EAAakB,MAAOV,KAAYC,EAC7C,CAEA,KAAAE,CAAMQ,EAAqBX,KAAkBC,GAC3C,GAAmB,MAAfC,KAAKN,SAAkBM,KAAKN,OAAOe,GAIvC,OAAQA,GACN,KAAKnB,EAAaY,MAIlB,KAAKZ,EAAac,KAEhBM,QAAQC,IAAIb,KAAYC,GACxB,MACF,KAAKT,EAAagB,QAEhBI,QAAQE,KAAKd,KAAYC,GACzB,MACF,KAAKT,EAAakB,MAEhBE,QAAQH,MAAMT,KAAYC,GAKhC,ECzCF,IAAIc,EAAgB,SAASC,EAAGC,GAI9B,OAHAF,EAAgBG,OAAOC,gBAClB,CAAEC,UAAW,cAAgBC,OAAS,SAAUL,EAAGC,GAAKD,EAAEI,UAAYH,CAAG,GAC1E,SAAUD,EAAGC,GAAK,IAAK,IAAIK,KAAKL,EAAOC,OAAOK,UAAUC,eAAeC,KAAKR,EAAGK,KAAIN,EAAEM,GAAKL,EAAEK,GAAI,EAC7FP,EAAcC,EAAGC,EAC1B,EAEO,SAASS,EAAUV,EAAGC,GAC3B,GAAiB,mBAANA,GAA0B,OAANA,EAC3B,MAAM,IAAIU,UAAU,uBAAyBC,OAAOX,GAAK,iCAE7D,SAASY,IAAO3B,KAAKR,YAAcsB,CAAG,CADtCD,EAAcC,EAAGC,GAEjBD,EAAEO,UAAkB,OAANN,EAAaC,OAAOY,OAAOb,IAAMY,EAAGN,UAAYN,EAAEM,UAAW,IAAIM,EACjF,CA2H6BX,OAAOY,OAgB7B,SAASC,EAASC,GACvB,IAAIC,EAAsB,mBAAXC,QAAyBA,OAAOC,SAAUC,EAAIH,GAAKD,EAAEC,GAAII,EAAI,EAC5E,GAAID,EAAG,OAAOA,EAAEX,KAAKO,GACrB,GAAIA,GAAyB,iBAAbA,EAAEM,OAAqB,MAAO,CAC1CC,KAAM,WAEF,OADIP,GAAKK,GAAKL,EAAEM,SAAQN,OAAI,GACrB,CAAEQ,MAAOR,GAAKA,EAAEK,KAAMI,MAAOT,EACxC,GAEJ,MAAM,IAAIL,UAAUM,EAAI,0BAA4B,kCACtD,CAEO,SAASS,EAAOV,EAAGW,GACxB,IAAIP,EAAsB,mBAAXF,QAAyBF,EAAEE,OAAOC,UACjD,IAAKC,EAAG,OAAOJ,EACf,IAAmBY,EAAYC,EAA3BR,EAAID,EAAEX,KAAKO,GAAOc,EAAK,GAC3B,IACI,WAAc,IAANH,GAAgBA,KAAM,MAAQC,EAAIP,EAAEE,QAAQE,MAAMK,EAAGC,KAAKH,EAAEJ,MACxE,CACA,MAAO/B,GAASoC,EAAI,CAAEpC,MAAOA,EAAS,CAAC,QAEnC,IACQmC,IAAMA,EAAEH,OAASL,EAAIC,EAAU,SAAID,EAAEX,KAAKY,EAClD,CAAC,QACS,GAAIQ,EAAG,MAAMA,EAAEpC,KAAO,CACpC,CACA,OAAOqC,CACT,CAkBO,SAASE,EAAcC,EAAIC,EAAMC,GACtC,GAAIA,GAA6B,IAArBC,UAAUd,OAAc,IAAK,IAA4BQ,EAAxBT,EAAI,EAAGgB,EAAIH,EAAKZ,OAAYD,EAAIgB,EAAGhB,KACxES,GAAQT,KAAKa,IACRJ,IAAIA,EAAKzB,MAAME,UAAU+B,MAAM7B,KAAKyB,EAAM,EAAGb,IAClDS,EAAGT,GAAKa,EAAKb,IAGrB,OAAOY,EAAGM,OAAOT,GAAMzB,MAAME,UAAU+B,MAAM7B,KAAKyB,GACpD,CAsCyBhC,OAAOY,OAoEkB,mBAApB0B,iBAAiCA,gBCnUzD,SAAUC,EAAWjB,GACzB,MAAwB,mBAAVA,CAChB,CCYO,ICHCkB,EDGKC,ICHLD,EDIN,SAACE,GACC,gBAA4CC,GAC1CD,EAAO,MACP,KAAK5D,QAAU6D,EACRA,EAAOvB,OAAM,4CACxBuB,EAAOC,KAAI,SAACC,EAAK1B,GAAM,OAAGA,EAAI,EAAC,KAAK0B,EAAIC,UAAjB,IAA+BC,KAAK,QACnD,GACJ,KAAKC,KAAO,sBACZ,KAAKL,OAASA,CAChB,CARA,CCLeM,EALF,SAACC,GACd1D,MAAMe,KAAK2C,GACXA,EAASC,OAAQ,IAAI3D,OAAQ2D,KAC/B,KAGS9C,UAAYL,OAAOY,OAAOpB,MAAMa,WACzCmC,EAASnC,UAAU7B,YAAcgE,EAC1BA,GCbH,SAAUY,EAAaC,EAA6BC,GACxD,GAAID,EAAK,CACP,IAAME,EAAQF,EAAIG,QAAQF,GAC1B,GAAKC,GAASF,EAAII,OAAOF,EAAO,EAAE,CAEtC,CCOA,IAAAG,EAAA,WAyBE,SAAAA,EAAoBC,GAAA,KAAAA,gBAAAA,EAdb,KAAAC,QAAS,EAER,KAAAC,WAAmD,KAMnD,KAAAC,YAAqD,IAMV,CAvB5B,IACfC,EAkLV,OApJEL,EAAArD,UAAA2D,YAAA,uBACMrB,EAEJ,IAAK,KAAKiB,OAAQ,CAChB,KAAKA,QAAS,EAGN,IAAAC,EAAe,KAAIA,WAC3B,GAAIA,EAEF,GADA,KAAKA,WAAa,KACd1D,MAAM8D,QAAQJ,GAAa,IAC7B,IAAqB,IAAAK,EAAArD,EAAAgD,GAAUM,EAAAD,EAAA7C,QAAA8C,EAAA5C,KAAA4C,EAAAD,EAAA7C,OAAE,CAAhB8C,EAAA7C,MACR8C,OAAO,KAAK,wGAGrBP,EAAWO,OAAO,MAId,IAAiBC,EAAqB,KAAIV,gBAClD,GAAIpB,EAAW8B,GACb,IACEA,GAAkB,CAClB,MAAO1C,GACPgB,EAAShB,aAAac,EAAsBd,EAAEgB,OAAS,CAAChB,EAAE,CAItD,IAAAmC,EAAgB,KAAIA,YAC5B,GAAIA,EAAa,CACf,KAAKA,YAAc,KAAI,IACvB,IAAwB,IAAAQ,EAAAzD,EAAAiD,GAAWS,EAAAD,EAAAjD,QAAAkD,EAAAhD,KAAAgD,EAAAD,EAAAjD,OAAE,CAAhC,IAAMmD,EAASD,EAAAjD,MAClB,IACEmD,EAAcD,EAAU,CACxB,MAAO3B,GACPF,EAASA,QAAAA,EAAU,GACfE,aAAeJ,EACjBE,EAAMb,EAAAA,EAAA,GAAAN,EAAOmB,IAAMnB,EAAKqB,EAAIF,SAE5BA,EAAOd,KAAKgB,EAAI,qGAMxB,GAAIF,EACF,MAAM,IAAIF,EAAoBE,EAAO,CAG3C,EAoBAe,EAAArD,UAAAqE,IAAA,SAAIC,GAAuB,MAGzB,GAAIA,GAAYA,IAAa,KAC3B,GAAI,KAAKf,OAGPa,EAAcE,OACT,CACL,GAAIA,aAAoBjB,EAAc,CAGpC,GAAIiB,EAASf,QAAUe,EAASC,WAAW,MACzC,OAEFD,EAASE,WAAW,KAAK,EAE1B,KAAKf,YAA8B,QAAhBgB,EAAA,KAAKhB,mBAAW,IAAAgB,EAAAA,EAAI,IAAIjD,KAAK8C,EAAS,CAGhE,EAOQjB,EAAArD,UAAAuE,WAAR,SAAmBG,GACT,IAAAlB,EAAe,KAAIA,WAC3B,OAAOA,IAAekB,GAAW5E,MAAM8D,QAAQJ,IAAeA,EAAWmB,SAASD,EACpF,EASQrB,EAAArD,UAAAwE,WAAR,SAAmBE,GACT,IAAAlB,EAAe,KAAIA,WAC3B,KAAKA,WAAa1D,MAAM8D,QAAQJ,IAAeA,EAAWhC,KAAKkD,GAASlB,GAAcA,EAAa,CAACA,EAAYkB,GAAUA,CAC5H,EAMQrB,EAAArD,UAAA4E,cAAR,SAAsBF,GACZ,IAAAlB,EAAe,KAAIA,WACvBA,IAAekB,EACjB,KAAKlB,WAAa,KACT1D,MAAM8D,QAAQJ,IACvBT,EAAUS,EAAYkB,EAE1B,EAgBArB,EAAArD,UAAA+D,OAAA,SAAOO,GACG,IAAAb,EAAgB,KAAIA,YAC5BA,GAAeV,EAAUU,EAAaa,GAElCA,aAAoBjB,GACtBiB,EAASM,cAAc,KAE3B,EAlLcvB,EAAAwB,QACNnB,EAAQ,IAAIL,GACZE,QAAS,EACRG,GAgLXL,CAAC,CArLD,GAuLkCA,EAAawB,MAEzC,SAAUC,EAAe7D,GAC7B,OACEA,aAAiBoC,GAChBpC,GAAS,WAAYA,GAASiB,EAAWjB,EAAM8C,SAAW7B,EAAWjB,EAAMoD,MAAQnC,EAAWjB,EAAM0C,YAEzG,CAEA,SAASS,EAAcD,GACjBjC,EAAWiC,GACbA,IAEAA,EAAUR,aAEd,CChNO,IAAMoB,EAAuB,CAClCC,iBAAkB,KAClBC,sBAAuB,KACvBC,aAASC,EACTC,uCAAuC,EACvCC,0BAA0B,GCGfC,EAAmC,CAG9CC,WAAA,SAAWC,EAAqBC,GAAkB,QAAAC,EAAA,GAAAC,EAAA,EAAAA,EAAA9D,UAAAd,OAAA4E,IAAAD,EAAAC,EAAA,GAAA9D,UAAA8D,GACxC,IAAAC,EAAaN,EAAeM,SACpC,OAAIA,aAAQ,EAARA,EAAUL,YACLK,EAASL,WAAUM,MAAnBD,EAAQnE,EAAA,CAAY+D,EAASC,GAAOtE,EAAKuE,KAE3CH,WAAUM,WAAA,EAAApE,EAAA,CAAC+D,EAASC,GAAOtE,EAAKuE,IACzC,EACAI,aAAA,SAAaC,GACH,IAAAH,EAAaN,EAAeM,SACpC,QAAQA,aAAQ,EAARA,EAAUE,eAAgBA,cAAcC,EAClD,EACAH,cAAUT,GC5BN,SAAUa,IAAS,CCMlB,IAAMC,EAA+BC,EAAmB,SAAKf,OAAWA,GA0BzE,SAAUe,EAAmBC,EAAuBlF,EAAY/B,GACpE,MAAO,CACLiH,KAAIA,EACJlF,MAAKA,EACL/B,MAAKA,EAET,CCrCA,IAAIkH,EAAuD,KCkB3D,IAAAC,EAAA,SAAAhE,GA6BE,SAAAgE,EAAYC,GAAZ,IAAAC,EACElE,EAAAnC,KAAA,OAAO,KAS4B,OAlB3BqG,EAAAC,WAAqB,EAUzBF,GACFC,EAAKD,YAAcA,EAGfxB,EAAewB,IACjBA,EAAYjC,IAAIkC,IAGlBA,EAAKD,YAAcG,EAAc,CAErC,CA2EF,OApHmCtG,EAAAkG,EAAAhE,GAgB1BgE,EAAA9F,OAAP,SAAiBS,EAAwB9B,EAA2BwH,GAClE,OAAO,IAAIC,EAAe3F,EAAM9B,EAAOwH,EACzC,EAgCAL,EAAArG,UAAAgB,KAAA,SAAKC,GACC,KAAKuF,UACPI,EFjDA,SAA8B3F,GAClC,OAAOiF,EAAmB,IAAKjF,OAAOkE,EACxC,CE+CgC0B,CAAiB5F,GAAQ,MAEnD,KAAK6F,MAAM7F,EAEf,EASAoF,EAAArG,UAAAd,MAAA,SAAMsD,GACA,KAAKgE,UACPI,EFxEGV,EAAmB,SAAKf,EEwEiB3C,GAAM,OAElD,KAAKgE,WAAY,EACjB,KAAKO,OAAOvE,GAEhB,EAQA6D,EAAArG,UAAA0G,SAAA,WACM,KAAKF,UACPI,EAA0BX,EAAuB,OAEjD,KAAKO,WAAY,EACjB,KAAKQ,YAET,EAEAX,EAAArG,UAAA2D,YAAA,WACO,KAAKJ,SACR,KAAKiD,WAAY,EACjBnE,EAAArC,UAAM2D,YAAWzD,KAAA,MACjB,KAAKoG,YAAc,KAEvB,EAEUD,EAAArG,UAAA8G,MAAV,SAAgB7F,GACd,KAAKqF,YAAYtF,KAAKC,EACxB,EAEUoF,EAAArG,UAAA+G,OAAV,SAAiBvE,GACf,IACE,KAAK8D,YAAYpH,MAAMsD,EAAI,CAC5B,QACC,KAAKmB,aAAa,CAEtB,EAEU0C,EAAArG,UAAAgH,UAAV,WACE,IACE,KAAKV,YAAYI,UAAU,CAC5B,QACC,KAAK/C,aAAa,CAEtB,EACF0C,CAAA,CApHA,CAAmChD,GA2H7B4D,EAAQC,SAASlH,UAAUmH,KAEjC,SAASA,EAAyCC,EAAQC,GACxD,OAAOJ,EAAM/G,KAAKkH,EAAIC,EACxB,CAMA,IAAAC,EAAA,WACE,SAAAA,EAAoBC,GAAA,KAAAA,gBAAAA,CAAwC,CAoC9D,OAlCED,EAAAtH,UAAAgB,KAAA,SAAKC,GACK,IAAAsG,EAAoB,KAAIA,gBAChC,GAAIA,EAAgBvG,KAClB,IACEuG,EAAgBvG,KAAKC,EAAM,CAC3B,MAAO/B,GACPsI,EAAqBtI,EAAM,CAGjC,EAEAoI,EAAAtH,UAAAd,MAAA,SAAMsD,GACI,IAAA+E,EAAoB,KAAIA,gBAChC,GAAIA,EAAgBrI,MAClB,IACEqI,EAAgBrI,MAAMsD,EAAI,CAC1B,MAAOtD,GACPsI,EAAqBtI,EAAM,MAG7BsI,EAAqBhF,EAEzB,EAEA8E,EAAAtH,UAAA0G,SAAA,WACU,IAAAa,EAAoB,KAAIA,gBAChC,GAAIA,EAAgBb,SAClB,IACEa,EAAgBb,UAAU,CAC1B,MAAOxH,GACPsI,EAAqBtI,EAAM,CAGjC,EACFoI,CAAA,CArCA,GAuCAX,EAAA,SAAAtE,GACE,SAAAsE,EACEc,EACAvI,EACAwH,GAHF,IAOMa,EAWEG,EAlBRnB,EAKElE,EAAAnC,KAAA,OAAO,KAGHgC,EAAWuF,KAAoBA,EAGjCF,EAAkB,CAChBvG,KAAOyG,QAAAA,OAAkBtC,EACzBjG,MAAOA,QAAAA,OAASiG,EAChBuB,SAAUA,QAAAA,OAAYvB,GAKpBoB,GAAQxB,EAAOM,2BAIjBqC,EAAU/H,OAAOY,OAAOkH,IAChB9D,YAAc,WAAM,OAAA4C,EAAK5C,aAAL,EAC5B4D,EAAkB,CAChBvG,KAAMyG,EAAezG,MAAQmG,EAAKM,EAAezG,KAAM0G,GACvDxI,MAAOuI,EAAevI,OAASiI,EAAKM,EAAevI,MAAOwI,GAC1DhB,SAAUe,EAAef,UAAYS,EAAKM,EAAef,SAAUgB,KAIrEH,EAAkBE,EAMkC,OAAxDlB,EAAKD,YAAc,IAAIgB,EAAiBC,GAAgB,CAC1D,CACF,OAzCuCpH,EAAAwG,EAAAtE,GAyCvCsE,CAAA,CAzCA,CAAuCN,GA2CvC,SAASmB,EAAqBtI,GDvMxB,IAAuBsD,ECwMvBuC,EAAOK,uCDxMgB5C,ECyMZtD,EDxMX6F,EAAOK,uCAAyCgB,IAClDA,EAAQuB,aAAc,EACtBvB,EAAQlH,MAAQsD,IE3Bd,SAA+BA,GACnC8C,EAAgBC,YAAW,WACjB,IAAAP,EAAqBD,EAAMC,iBACnC,IAAIA,EAKF,MAAMxC,EAHNwC,EAAiBxC,EAKrB,GACF,CD0NIoF,CAAqB1I,EAEzB,CAiBA,SAAS0H,EAA0BiB,EAA2CC,GACpE,IAAA7C,EAA0BF,EAAME,sBACxCA,GAAyBK,EAAgBC,YAAW,WAAM,OAAAN,EAAsB4C,EAAcC,EAApC,GAC5D,CAOO,IAAMrB,EAA6D,CACxElD,QAAQ,EACRvC,KAAMgF,EACN9G,MAtBF,SAA6BsD,GAC3B,MAAMA,CACR,EAqBEkE,SAAUV,GE5QC+B,EAAwD,mBAAXpH,QAAyBA,OAAOoH,YAAe,eCoCnG,SAAUC,EAAYC,GAC1B,OAAOA,CACT,CCsCM,SAAUC,EAAoBC,GAClC,OAAmB,IAAfA,EAAIpH,OACCiH,EAGU,IAAfG,EAAIpH,OACCoH,EAAI,GAGN,SAAeC,GACpB,OAAOD,EAAIE,QAAO,SAACC,EAAWlB,GAA4B,OAAAA,EAAGkB,EAAH,GAAUF,EACtE,CACF,CC9EA,IAAAG,EAAA,WAkBE,SAAAA,EAAYC,GACNA,IACF,KAAKC,WAAaD,EAEtB,CAwbF,OA5ZED,EAAAvI,UAAA0I,KAAA,SAAQC,GACN,IAAMZ,EAAa,IAAIQ,EAGvB,OAFAR,EAAWa,OAAS,KACpBb,EAAWY,SAAWA,EACfZ,CACT,EA6IAQ,EAAAvI,UAAAwI,UAAA,SACEf,EACAvI,EACAwH,GAHF,IA2RuBzF,EA3RvBsF,EAAA,KAKQuB,GAsRe7G,EAtRWwG,IAuRjBxG,aAAiBoF,GALpC,SAAuBpF,GACrB,OAAOA,GAASiB,EAAWjB,EAAMD,OAASkB,EAAWjB,EAAM/B,QAAUgD,EAAWjB,EAAMyF,SACxF,CAGoDmC,CAAW5H,IAAU6D,EAAe7D,GAvRlCwG,EAAiB,IAAId,EAAec,EAAgBvI,EAAOwH,GAoB7G,ONlOE,SAAuBoC,GAC3B,GAAI/D,EAAOK,sCAAuC,CAChD,IAAM2D,GAAU3C,EAKhB,GAJI2C,IACF3C,EAAU,CAAEuB,aAAa,EAAOzI,MAAO,OAEzC4J,IACIC,EAAQ,CACJ,IAAAtE,EAAyB2B,EAAvBuB,EAAWlD,EAAAkD,YAAEzI,EAAKuF,EAAAvF,MAE1B,GADAkH,EAAU,KACNuB,EACF,MAAMzI,CAAK,OAMf4J,GAEJ,CM6LIE,EAAa,WACL,IAAAvE,EAAuB8B,EAArBoC,EAAQlE,EAAAkE,SAAEC,EAAMnE,EAAAmE,OACxBd,EAAWzD,IACTsE,EAGIA,EAASzI,KAAK4H,EAAYc,GAC1BA,EAIArC,EAAKkC,WAAWX,GAGhBvB,EAAK0C,cAAcnB,GAE3B,IAEOA,CACT,EAGUS,EAAAvI,UAAAiJ,cAAV,SAAwBC,GACtB,IACE,OAAO,KAAKT,WAAWS,EAAK,CAC5B,MAAO1G,GAIP0G,EAAKhK,MAAMsD,EAAI,CAEnB,EA6DA+F,EAAAvI,UAAAmJ,QAAA,SAAQnI,EAA0BoI,GAAlC,IAAA7C,EAAA,KAGE,OAAO,IAFP6C,EAAcC,EAAeD,KAEA,SAACE,EAASC,GACrC,IAAMzB,EAAa,IAAInB,EAAkB,CACvC3F,KAAM,SAACC,GACL,IACED,EAAKC,EAAM,CACX,MAAOuB,GACP+G,EAAO/G,GACPsF,EAAWnE,aAAa,CAE5B,EACAzE,MAAOqK,EACP7C,SAAU4C,IAEZ/C,EAAKiC,UAAUV,EACjB,GACF,EAGUS,EAAAvI,UAAAyI,WAAV,SAAqBX,GAA2B,MAC9C,OAAkB,QAAXrD,EAAA,KAAKmE,cAAM,IAAAnE,OAAA,EAAAA,EAAE+D,UAAUV,EAChC,EAOAS,EAAAvI,UAACwJ,GAAD,WACE,OAAO,IACT,EA4FAjB,EAAAvI,UAAAyJ,KAAA,WAAK,QAAAC,EAAA,GAAA/D,EAAA,EAAAA,EAAA9D,UAAAd,OAAA4E,IAAA+D,EAAA/D,GAAA9D,UAAA8D,GACH,OAAOuC,EAAcwB,EAAdxB,CAA0B,KACnC,EA6BAK,EAAAvI,UAAA2J,UAAA,SAAUP,GAAV,IAAA7C,EAAA,KAGE,OAAO,IAFP6C,EAAcC,EAAeD,KAEN,SAACE,EAASC,GAC/B,IAAItI,EACJsF,EAAKiC,WACH,SAACP,GAAS,OAAChH,EAAQgH,CAAT,IACV,SAACzF,GAAa,OAAA+G,EAAO/G,EAAP,IACd,WAAM,OAAA8G,EAAQrI,EAAR,GAEV,GACF,EA1aOsH,EAAAhI,OAAkC,SAAIiI,GAC3C,OAAO,IAAID,EAAcC,EAC3B,EAyaFD,CAAC,CA9cD,GAudA,SAASc,EAAeD,GAA+C,MACrE,OAAoC,QAA7B3E,EAAA2E,QAAAA,EAAerE,EAAOG,eAAO,IAAAT,EAAAA,EAAIS,OAC1C,CCzeO,MAIM0E,EACX,KALgD,CAAC,IAAK,IAAK,IAAK,KAMvClH,KAAK,MAE3BmH,QAAQ,IAAK,KCkELC,GD3D6B,IAAIC,OAAO,IAAIH,KAAgC,KAE9B,IAAIG,OAE7D,QAAQH,KACR,KAGoC,IAAIG,OAAO,qCAEF,IAAIA,OAAO,qBCkDhD,UADGD,EAEJ,QAFIA,EAGL,OAHKA,EAIF,UAJEA,EAKF,UAmBEE,EAII,iBCpGjB,IAAYC,ECAAC,GDAZ,SAAYD,GACV,YACA,oBACA,oBAEA,WAED,CAPD,CAAYA,IAAAA,EAAU,KCAtB,SAAYC,GACV,yBACA,iBACA,yCACA,2CACA,uCACA,qCACA,uCACA,mCACA,mCACA,qCACA,wCACA,oCACA,kCACA,8BACA,wCACA,sCACA,kBACA,sCACA,wCACA,4CACA,0CACA,kBACA,wBACA,gCACA,4BACA,2BACD,CA3BD,CAAYA,IAAAA,EAAU,KAuCnBA,EAAWC,QACXD,EAAWE,IACXF,EAAWG,cACXH,EAAWI,gBACXJ,EAAWK,iBACXL,EAAWM,eACXN,EAAWO,cACXP,EAAWQ,iBACXR,EAAWS,gBACXT,EAAWU,cACXV,EAAWW,eACXX,EAAWY,aACXZ,EAAWa,YACXb,EAAWc,UACXd,EAAWe,cACXf,EAAWgB,eACXhB,EAAWiB,eACXjB,EAAWkB,eACXlB,EAAWmB,aACXnB,EAAWoB,aACXpB,EAAWqB,IACXrB,EAAWsB,WACXtB,EAAWuB,SACXvB,EAAWwB,SACXxB,EAAWyB,IACXzB,EAAW0B,OC/Dd,IAAYC,ECAAC,ECKAC,ECNAC,ECAAC,ECAAC,GLCZ,SAAYL,GACV,mBACA,+CACA,4BACD,CAJD,CAAYA,IAAAA,EAAe,KCA3B,SAAYC,GACV,uCACA,sDACA,4CACA,8CACA,gDACA,8CACA,oDACA,8DACA,8DACA,0EACA,sEACA,0EAEA,0CACA,0CACA,0CACA,8DACA,8DACA,wCACA,gEACA,oDACA,oFACA,0FACA,oFACA,oEACA,0EACA,oEACA,4DACA,kDACA,4CACA,wFACA,oFAEA,kDACA,kDACA,kDAEA,wCACA,wCACA,wCAEA,8DACA,kEACA,8DACA,8DACA,0EACA,sEACA,wFACA,4FACA,oFACA,wEACA,0EACA,8DACA,gEACA,sFACA,sFACA,8DACA,wDAEA,sDACA,8DACA,8EACA,kEACA,4DACA,8DACA,8EACA,gFACA,4EACA,8FAEA,0CAEA,sDACA,0DACA,sDACA,sDAEA,sEACA,kEACA,sEACA,kFAEA,6DACA,kEACA,oEACA,0EAEA,6CACD,CAzFD,CAAYA,IAAAA,EAAS,KCKrB,SAAYC,GAQV,6BAKA,iDAOA,iCAYA,iBAKA,2BAMA,6BAOA,mEAKA,+BAMA,qCAOA,yCAMA,mCAMA,2CAMA,yBAWA,2CAKA,6CAUA,uBAQA,6BAMA,mCAOA,6BAKA,mCAQA,+CAOA,+CAUA,iCAQA,qCAOA,2CAMA,+BAMA,6BAMA,6CAKA,uCAKA,mEAOA,yCAMA,6BASA,qBAKA,yCAKA,iDAKA,2CAOA,iCAMA,qDAOA,mDAKA,+CAOA,iCAKA,iDAKA,mDAKA,yBAKA,6CAKA,2CAQA,qDAKA,2CAMA,mEAMA,iEASA,mDAMA,yCAKA,iCAMA,iDAKA,yCAKA,2DAKA,uDAKA,mDAKA,qCAKA,mCAOA,sEACD,CArYD,CAAYA,IAAAA,EAAc,KCN1B,SAAYC,GACV,4BACA,YACA,YACA,cACA,cACA,gBACA,iBACD,CARD,CAAYA,IAAAA,EAAe,KCA3B,SAAYC,GACV,iBACA,sBACD,CAHD,CAAYA,IAAAA,EAAsB,KCAlC,SAAYC,GACV,2CACA,2CACA,yCACA,2CACA,iCAEA,6BACA,iCACA,2CACA,2CACA,2CACA,oCAEA,wBAEA,wCACA,wCACA,wCAEA,kCACA,kDAEA,8CACA,sEACA,4FACA,oCACA,gDAEA,mDACD,CA9BD,CAAYA,IAAAA,EAAgB,KCArB,MAAMC,EACX,uBAAOC,CAAiBC,EAAiBC,EAAY,KAAMC,GAAa,GACtE,OAAKC,GAAWC,YAGT,IAAIvH,SAASoE,IAClB,MACMoD,GADM,IAAIC,MAEVC,UAAUnK,WAAa,IAAMoK,KAAKC,MAAMD,KAAKE,SAAWC,OAAOC,kBACpEC,QAAgBC,QAAQC,kBACvB,wBACA,CACEC,GAAIX,EACJL,QAASA,EACTC,KAAMA,EACNgB,aAAc,OAEfC,IACCjE,EAAQiE,EAAS,GAEpB,IAjBMrI,QAAQoE,QAAQ,KAmB3B,E,0SCtBF,MAAMkE,EASJ,WAAaC,CAAKC,EAAuBC,G,yCACvC,GAAKH,EAAwBI,wBAAwBF,EAAe,aAKpE,UACQA,EAAcG,UAAUC,UAAUC,UAAUJ,EACpD,CAAE,MAAOzO,GACPsO,EAAwBQ,kBAAkBxP,MACxC,iFAAiFU,KAGnFP,KAAKsP,oBAAoBP,EAAeC,EAC1C,MAZEhP,KAAKsP,oBAAoBP,EAAeC,EAa5C,G,CAOA,WAAaO,CAAKR,G,yCAChB,IAAKF,EAAwBI,wBAAwBF,EAAe,YAClE,OAAO/O,KAAKwP,oBAAoBT,GAGlC,IACE,aAAaA,EAAcG,UAAUC,UAAUM,UACjD,CAAE,MAAOlP,GAKP,OAJAsO,EAAwBQ,kBAAkBxP,MACxC,mFAAmFU,KAG9EP,KAAKwP,oBAAoBT,EAClC,CACF,G,CASQ,0BAAOO,CAAoBP,EAAuBC,GACxD,IAAKH,EAAwBa,iCAAiCX,EAAe,QAE3E,YADAF,EAAwBQ,kBAAkBhP,QAAQ,oCAIpD,MAAMsP,EAAkBZ,EAAca,SAASC,cAAc,YAC7DF,EAAgBG,YAAed,GAAO,IACtCW,EAAgBI,MAAMC,SAAW,QACjCjB,EAAca,SAASK,KAAKC,YAAYP,GACxCA,EAAgBQ,SAEhB,IACEpB,EAAca,SAASQ,YAAY,OACrC,CAAE,MAAO7P,GACPsO,EAAwBQ,kBAAkBhP,QAAQ,+BAA+BE,IACnF,C,QACEwO,EAAca,SAASK,KAAKI,YAAYV,EAC1C,CACF,CAQQ,0BAAOH,CAAoBT,GACjC,IAAKF,EAAwBa,iCAAiCX,EAAe,SAE3E,OADAF,EAAwBQ,kBAAkBhP,QAAQ,qCAC3C,GAGT,MAAMsP,EAAkBZ,EAAca,SAASC,cAAc,YAC7DF,EAAgBI,MAAMC,SAAW,QACjCjB,EAAca,SAASK,KAAKC,YAAYP,GACxCA,EAAgBW,QAEhB,IACE,OAAOvB,EAAca,SAASQ,YAAY,SAAWT,EAAgBrN,MAAQ,EAC/E,CAAE,MAAO/B,GACPsO,EAAwBQ,kBAAkBhP,QAAQ,iCAAiCE,IACrF,C,QACEwO,EAAca,SAASK,KAAKI,YAAYV,EAC1C,CAEA,MAAO,EACT,CAQQ,8BAAOV,CAAwBF,EAAuBwB,GAC5D,MAAO,cAAexB,EAAcG,WAAaqB,KAAUxB,EAAcG,UAAUC,SACrF,CAQQ,uCAAOO,CAAiCX,EAAuBwB,GACrE,MACE,0BAA2BxB,EAAca,UACzCb,EAAca,SAASY,sBAAsBD,EAEjD,EA3He,EAAAlB,kBAAuC,IAAI9P,GAAkB,GA8H9E,Q,sSCnHO,MAAekR,EAGpB,WAAAjR,CACUkR,EACA3B,EACA4B,GAFA,KAAAD,uBAAAA,EACA,KAAA3B,cAAAA,EACA,KAAA4B,yBAAAA,CACP,CAEH,gBAAOC,CAAU7B,GACf,OAAI/O,KAAK6Q,cAQLJ,EAA4BK,YAC9B9Q,KAAK6Q,YAActF,EAAWK,iBACrB6E,EAA4BM,QAAQhC,GAC7C/O,KAAK6Q,YAActF,EAAWM,eACrB4E,EAA4BO,SACrChR,KAAK6Q,YAActF,EAAWO,cACrB2E,EAA4BQ,YACrCjR,KAAK6Q,YAActF,EAAWQ,iBACrB0E,EAA4BS,SAASnC,GAC9C/O,KAAK6Q,YAActF,EAAWI,gBACrB8E,EAA4BU,SAASpC,KAC9C/O,KAAK6Q,YAActF,EAAWS,kBAlBvBhM,KAAK6Q,WAsBhB,CAEA,SAAAD,GACE,OAAOH,EAA4BG,UAAU5Q,KAAK+O,cACpD,CAEA,eAAAqC,GAEE,OADe7F,EAAWvL,KAAK4Q,aAAaS,cAC9BnG,QAAQ,YAAa,GACrC,CAEA,aAAAoG,GACE,OAAOhG,EAAWiG,OACpB,CAEQ,gBAAOT,GACb,OACgD,IAA9C5B,UAAUsC,UAAUhN,QAAQ,eACgB,IAA5C0K,UAAUsC,UAAUhN,QAAQ,UAEhC,CAEA,SAAAsM,GACE,OAAO9Q,KAAK4Q,cAAgBrF,EAAWK,gBACzC,CAEQ,eAAOsF,CAASnC,GACtB,OAAOA,EAAc0C,SAAuD,IAA7CvC,UAAUsC,UAAUhN,QAAQ,WAC7D,CAEA,QAAA0M,GACE,OAAOlR,KAAK4Q,cAAgBrF,EAAWI,eACzC,CAEQ,aAAOqF,GACb,OAAiD,IAA1C9B,UAAUsC,UAAUhN,QAAQ,QACrC,CAEA,MAAAwM,GACE,OAAOhR,KAAK4Q,cAAgBrF,EAAWO,aACzC,CAEQ,cAAOiF,CAAQhC,G,MACrB,SACqB,QAAjB,EAAAA,EAAc2C,WAAG,eAAEC,WACnB5C,EAAc6C,OAChB1C,UAAUsC,UAAUhN,QAAQ,UAAY,CAE5C,CAEA,OAAAuM,GACE,OAAO/Q,KAAK4Q,cAAgBrF,EAAWM,cACzC,CAEQ,gBAAOoF,GACb,OAAqD,IAA9C/B,UAAUsC,UAAUhN,QAAQ,YACrC,CAEA,SAAAyM,GACE,OAAOjR,KAAK4Q,cAAgBrF,EAAWQ,gBACzC,CAEQ,eAAOoF,CAASpC,GAEtB,OACG0B,EAA4BM,QAAQhC,KACQ,IAA7CG,UAAUsC,UAAUhN,QAAQ,WAEhC,CAEQ,oBAAOqN,G,MACb,OAAqD,QAA9C,EAAA3C,UAAUsC,UAAUM,MAAM,4BAAoB,eAAG,EAC1D,CAEA,QAAAX,GACE,OAAOnR,KAAK4Q,cAAgBrF,EAAWS,eACzC,CAMA,iCAAO+F,CAA2BhD,G,MAChC,GAAI0B,EAA4BG,UAAU7B,KAAmBxD,EAAWS,gBACtE,OAAO,EAGT,MAAMgG,EAAUvB,EAA4BoB,gBACtCI,EAA2B,QAAnB,EAAAD,aAAO,EAAPA,EAASE,MAAM,YAAI,eAAEtO,KAAKuO,GAAM9D,OAAO8D,KACrD,OAAOF,aAAK,EAALA,EAAQ,IAAK,IAAsB,MAAfA,aAAK,EAALA,EAAQ,KAA4B,KAAfA,aAAK,EAALA,EAAQ,GAC1D,CAEA,IAAAG,GACE,OAAO,CACT,CAEA,aAAAC,GACE,OAAO,CACT,CAOM,UAAAC,G,yCACJ,OAAItS,KAAKmR,WAEAtD,GAAW0E,cAGb,IAAIhM,SAAiB,CAACoE,EAASC,KACpC6G,OAAOjD,QAAQgE,YAAY,CAAE9E,QAAS,6BAA+BkB,IACnE,GAAgC,MAA5B6C,OAAOjD,QAAQiE,UAGjB,MAEE,kEADAhB,OAAOjD,QAAQiE,UAAU3S,aAGzB6K,GAAQ,QAKVC,EAAO6G,OAAOjD,QAAQiE,WAIxB9H,EAAQ+H,QAAQ9D,GAAU,GAC1B,GAEN,G,CAEA,WAAA+D,GACE,OAAO,IACT,CAEA,SAAAC,CAAUC,EAAaC,GAGrBjF,GAAWkF,aAAaF,EAAKC,IAAqC,IAA1BA,EAAQE,cAClD,CAEA,qBAAAC,G,MACE,MAAMC,EAAWzB,OAAOjD,QAAQ2E,cAChC,OAAO5M,QAAQoE,QAA6B,QAArB,EAAAuI,EAASE,oBAAY,QAAIF,EAASlB,QAC3D,CAEA,2BAAAqB,GACE,MAAMH,EAAWzB,OAAOjD,QAAQ2E,cAChC,OAAO5M,QAAQoE,QAAQuI,EAASlB,QAAQE,MAAM9G,OAAO,UAAU,GAAGkI,OACpE,CAEA,gBAAAC,CAAiBC,GACf,MAAsC,oBAAxBC,mBAChB,CAEA,WAAAC,GACE,OAAO,CACT,CASA,KAAAjU,GACE,OAAO,CACT,CAEA,UAAAkU,GACE,OAAO,CACT,CAYA,eAAAC,CAAgB5E,EAAc8D,GAC5B,MAAMe,GAAgBf,aAAO,EAAPA,EAASgB,SAAW9T,KAAK+O,cACzCgF,EAAWrB,QAAQI,aAAO,EAAPA,EAASiB,UAC5BC,GAAkBlB,aAAO,EAAPA,EAASkB,UAAW,KACtCC,EAA+B,KAC9BF,GAA2C,MAA/B/T,KAAK0Q,wBACpB1Q,KAAK0Q,uBAAuB1B,EAAMgF,EACpC,EAGEhU,KAAKmR,WACF3D,EAAUC,iBAAiB,kBAAmBuB,GAAMkF,KAAKD,IAK5DjU,KAAKkR,YAAuB,KAATlC,IACrBA,EAAO,MAGLnB,GAAWsG,kBAAkB,IAAMnU,KAAK2Q,yBAAyByD,wBAC9DpU,KAAKqU,gCAAgCrF,GAAMkF,KAAKD,GAKlD,EAAwBnF,KAAK+E,EAAe7E,GAAMkF,KAAKD,GAC9D,CAWM,iBAAAK,CAAkBxB,G,yCACtB,MAAMe,GAAgBf,aAAO,EAAPA,EAASgB,SAAW9T,KAAK+O,cAE/C,OAAI/O,KAAKmR,iBACM3D,EAAUC,iBAAiB,qBAGtCI,GAAWsG,kBAAkB,IAAMnU,KAAK2Q,yBAAyByD,8BACtDpU,KAAKuU,0CAGP,EAAwBhF,KAAKsE,EAC5C,G,CAEA,qBAAAW,GACE,OAAO,CACT,CAEM,2BAAAC,G,yCACJ,IAAIC,EAyBJ,OAvBI1U,KAAKmR,WACPuD,EAAkB,cACT1U,KAAK8Q,aACd4D,SAAyBnG,QAAQoG,SAASC,UAAUC,MACjDC,GAAMA,EAAE9Q,OAASqH,IAClB0J,SAIiD,eAA1CxG,QAAQC,QAAQwG,mBAAmBC,IACtB,iBAApBP,IAEAA,EAAkB,sBAGd,IAAInO,SAASoE,GACjB8G,OAAOkD,SAASC,QAAQE,GACtBnK,EACG+J,EAAkBI,EAAED,MAAMC,GAAMA,EAAE9Q,OAASqH,IAAgC0J,cAK7EL,CACT,G,CAKc,+BAAAL,CAAgCrF,G,+CACtChP,KAAK2Q,yBAAyBuE,aAClC,CAACzD,OAAO0D,UAAUC,OAAOC,WACzB,gCACA,IAAY,wCACJxH,GAAWyH,wBAAwB,2BAA4B,CAAEtG,QACzE,KAEJ,G,CAKc,iCAAAuF,G,yCACZ,MAAM3F,QAAiB5O,KAAK2Q,yBAAyBuE,aACnD,CAACzD,OAAO0D,UAAUC,OAAOC,WACzB,iCACA,IAAY,kCACV,aAAaxH,GAAWyH,wBAAwB,6BAClD,MAEF,MAAwB,iBAAb1G,EACFA,EAGF,EACT,G,EA7Ue,EAAAiC,YAA0B,K,2SCK3C,IAAI0E,GAIG,SAAeC,GACpBC,EACAC,G,0CAMA,OAJKH,KACHA,GAMJ,W,QACE,MAAMI,EAAa,IAAIpW,GAAkB,GACnCqW,EAAcC,WAAWpE,QAAUqE,EAAsCD,WAAWpE,QACpFsE,EACJ,+HACIjF,EAAgC,QAApB,EAAA+E,WAAW3G,iBAAS,eAAEsC,UAAUxL,SAAS,YACrDgQ,EAAetD,QAAyB,QAAjB,EAAAmD,WAAWpE,cAAM,eAAEwE,WAC1CC,EAAkC,iBAAXzE,QAAuB,kBAAmBA,OAEvE,SAASqE,EAA8BK,GACrC,OAAO,IAAIC,MAAMD,EAAQ,CACvB,GAAAE,CAAIF,EAAQG,GACV,GAAKH,EAAOG,GAIZ,MAAuC,mBAA5BH,EAAOG,GACTR,EAAYK,EAAOG,IAGrB,IAAIC,IACT,IAAIhQ,SAAQ,CAACoE,EAASC,KACnBuL,EAAOG,MAAyCC,GAAaC,IACxD/E,OAAOjD,QAAQiE,UACjB7H,EAAO,IAAIpK,MAAMiR,OAAOjD,QAAQiE,UAAU3S,UAE1C6K,EAAQ6L,EACV,GACA,GAER,GAEJ,CAEA,SAASC,EAAmBC,GAC1B,IAAKC,EAAeD,GAClB,MAAM,IAAIlW,MACR,GAAGkW,0CAAqDhV,OAAOqU,KAGrE,CAEA,SAASY,EAAeD,GACtB,MAAwB,eAAjBA,GAAiCX,EAAuBa,KAAKF,EACtE,CAEA,SAASG,EAAmBH,GAC1BD,EAAmBC,GACnB,IAAK,CAAEI,EAAUC,EAAO,GAAIC,GAAYN,EAAaxE,MAAM,2BAmB3D,OAlBA4E,EAAWA,EACR5L,QAAQ,IAAK4F,EAAY,gBAAkB,UAC3CmG,WAAW,OAAQ,OAET,MAATF,EACFA,EAAO,QACEA,IACTA,EAAOA,EACJ7L,QAAQ,UAAW,aACnB+L,WAAW,OAAQ,OACnB/L,QAAQ,OAAQ,UAGrB8L,EAAWA,EACRC,WAAW,OAAQ,OACnBA,WAAW,OAAQ,OACnBA,WAAW,OAAQ,MAEf,IAAMH,EAAWC,EAAO,IAAMC,EAAW,KAClD,CAEA,SAASE,KAAkBC,GACzB,OAA6B,IAAzBA,EAAc/U,OACT,KAGL+U,EAAcnR,SAAS,cAElB,0BAGLmR,EAAcnR,SAAS,WAElB8K,EAAY,sCAAwC,+BAGtD,IAAI1F,OAAO+L,EAAcvT,KAAK0F,GAAMuN,EAAmBvN,KAAIvF,KAAK,KACzE,CAEA,SAASqT,EAAoBjB,GAC3B,MAAsB,iBAAXA,EACF,OAAP,wBAAYA,GAAM,CAAEkB,WAAW,IAG1B,CACLC,MAAOnB,EACPoB,aAAS/Q,EACT6Q,WAAW,EAEf,CAEA,SAASG,EAAUC,GACjB,OAAItW,MAAM8D,QAAQwS,GACTA,EAGF,CAACA,EACV,CAEA,SAASC,EAAiBpV,GACxB,YAAiBkE,IAAVlE,OAAsBkE,EAAY,CAAClE,EAC5C,CAEA,SAAeqV,EAAU,G,4CACvB,MACEL,EAAK,QACLC,EAAO,MACPK,EAAK,UACLP,EAAS,gBACTQ,EAAe,MACfC,IASF,mBAAEC,GAAyD,CAAC,GAE5D,MAAMC,EAAiBzR,QAAQ0R,IAC7BL,EAAMhU,KAAWsU,GAAY,mCAK3B,MAJuB,iBAAZA,IACTA,EAAU,CAAEC,KAAMD,IAGhBlC,EACKvE,OAAOwE,UAAU0B,UAAU,CAChCxB,OAAQ,CACNmB,QACAc,SAAUV,EAAiBH,GAC3BF,eAAuB7Q,IAAZ+Q,EAAwBF,OAAY7Q,GAEjDoR,MAAO,SAAUM,EAAU,CAACA,EAAQC,WAAQ3R,EAC5C6R,IAAK,SAAUH,EAAUA,EAAQI,UAAO9R,IAIrCoP,EAAY2C,KAAKZ,UAAUL,EAAO,OAAF,wBAClCY,GAAO,CACVL,kBACAR,YACAE,UACAO,MAAOA,QAAAA,EAAS,mBAEpB,OAGEC,QACIS,EAA2BR,SAE3BA,CAEV,G,CACA,SAASS,EAAab,GACpB,GAAIA,EAAMc,MAAMR,GAAY,SAAUA,IACpC,MAAM,IAAI1X,MAAM,gEAEpB,CAEA,SAAemY,EAAc,G,4CAC3B,MACErB,EAAK,QACLC,EAAO,MACPK,EAAK,UACLP,EAAS,gBACTQ,EAAe,MACfC,IASF,mBAAEC,GAAyD,CAAC,GAE5D,MAAMa,EAAkBhB,EAAMhU,KAAKuU,GAA0B,iBAATA,EAAoB,CAAEA,QAASA,IAEnF,GAAInC,EAAc,CAChByC,EAAaG,GACb,MAAMC,EAAYpH,OAAOwE,UAAU0C,cAAc,CAC/CxC,OAAQ,CACNmB,QACAc,SAAUV,EAAiBH,GAC3BF,eAAuB7Q,IAAZ+Q,EAAwBF,OAAY7Q,GAEjDoR,MAAOgB,EAAgBhV,KAAI,EAAGuU,UAA6BA,MAS7D,YANIJ,QACIS,EAA2BK,SAE3BA,EAIV,CAEA,MAAMC,EAAa,GACnB,IAAK,MAAMZ,KAAWU,EAChB,SAAUV,UACNY,EAAWC,IAAI,IAGvBD,EAAWjW,KACT+S,EAAY2C,KAAKI,cAAcrB,EAAO,OAAF,wBAC/BY,GAAO,CACVL,kBACAR,YACAE,UACAO,YAKFC,QACIS,EAA2BjS,QAAQ0R,IAAIa,UAEvCvS,QAAQ0R,IAAIa,EAEtB,G,CAEA,SAAeE,EAAoB,EAAD,G,2CAChCC,EACAC,EAMApG,EAAU,CAAC,GAEX,MAAMqG,EAAU3B,EAAUyB,SACpB1S,QAAQ0R,IACZkB,EAAQvV,KAAWuS,GAAU,mCAC3B,OAAAiD,EAAoChC,EAAoBjB,GAAS+C,EAASpG,EAAQ,MAGxF,G,CAEA,SAAesG,EAAoC,EAAD,G,4CAChD,QAAE7B,EAAO,MAAED,EAAK,UAAED,GAClB6B,EAMApG,EAAU,CAAC,GAEX,MAAMuG,EAAa7B,EAAU0B,GAASI,SAASC,I,gBAAW,OACxD5B,EACE,CACEL,QACAC,UACAF,YACAO,MAAiB,QAAV,EAAA2B,EAAOlB,WAAG,QAAI,GACrBR,gBAAuC,QAAtB,EAAA0B,EAAO1B,uBAAe,QAAI0B,EAAOC,kBAClD1B,MAAmB,QAAZ,EAAAyB,EAAOzB,aAAK,QAAIyB,EAAOE,QAEhC3G,GAEF6F,EACE,CACErB,QACAC,UACAF,YACAO,MAAgB,QAAT,EAAA2B,EAAOG,UAAE,QAAI,GACpB7B,gBAAuC,QAAtB,EAAA0B,EAAO1B,uBAAe,QAAI0B,EAAOC,kBAClD1B,MAAmB,QAAZ,EAAAyB,EAAOzB,aAAK,QAAIyB,EAAOE,QAEhC3G,GAEH,UACKvM,QAAQ0R,IAAIoB,EACpB,G,CAEA,SAAeb,EAA2BmB,G,0CACxC,UACQA,CACR,CAAE,MAAOpZ,GAGP,IADE,2GACgBqW,KAAKrW,aAAK,EAALA,EAAOT,SAC5B,MAAMS,CAEV,CACF,G,CAEA,SAAeqZ,EAAkBC,G,0CAC/B,OAAOjE,EAAYkE,YAAYC,SAAS,CACtCC,QAAS,CAAC,IAAIC,IAAIJ,GAAKK,OAAS,OAEpC,G,CAEA,MAAO,CACLzE,EACAC,IACG,mCACH,MAAM,GACJgE,EAAK,GAAE,IACPrB,EAAM,GAAE,gBACRR,EAAe,QACfsC,EAAU,GAAE,eACZC,EAAc,MACdtC,GACErC,EACJ,IAAI,UAAE4B,GAAc5B,EAUpB,GARIS,EACFmB,GAAY,EACHA,GACT1B,EAAWtV,QACT,0JAImB,IAAnB8Z,EAAQ/X,OACV,MAAM,IAAI5B,MACR,4JAIE+F,QAAQ0R,IACZkC,EAAQvW,KAAWyW,GAAoB,mCACrC,WAAYzE,EAAYkE,YAAYC,SAAS,CAAEC,QAAS,CAACK,MACvD,MAAM,IAAI7Z,MAAM,sDAAsD6Z,IAE1E,OAGF,MAAMC,EAAepD,KAAkBiD,GACjCI,EAAsBrD,KACtBkD,QAAuDA,EAAiB,IAExEI,EAAS,YAAmD,qCAA5CX,EAAavC,EAAeC,EAAU,GAEvD+C,EAAa1D,KAAKiD,KACnBU,EAAoB3D,KAAKiD,WACjBD,EAAkBC,YAKtBb,EACJ,CAAE1B,QAAOC,WACT,CAAEc,MAAKqB,KAAI7B,kBAAiBC,SAC5B,CAAEC,oBAAoB,IAE1B,IACM0C,EAAc,SAIf,kCAHHnD,GACA,OAAEoD,IACF,IAAEb,IAEa,YAAXa,GAAwBb,GACrBW,EAAOX,EAAKvC,EAErB,IACMqD,EAAc,GAI+C,+BAJxC,MACzBrD,EAAK,QACLC,EAAO,IACPsC,IAEKW,EAAOX,EAAKvC,EAAOC,EAC1B,IAEIrB,EACFrI,GAAW+M,YAAYnJ,OAAOoJ,cAAcC,YAAaH,GAEzD9M,GAAW+M,YAAYnJ,OAAO8G,KAAKwC,UAAWN,GAGhD,MAAMO,EAA0B,CACxB,UAAAC,G,0CACA/E,EACFzE,OAAOoJ,cAAcC,YAAYI,eAAeP,GAEhDlJ,OAAO8G,KAAKwC,UAAUG,eAAeT,EAEzC,G,GAOF,MAJwB,mBAAb/E,GACTA,EAASsF,GAGJA,CACT,GACF,CAvZ6BG,IAGpB5F,GAAuBE,EAAsBC,EACtD,G,4SCnBO,MAAM7H,GAOX,0BAAWuN,GACT,OAAO3J,OAAOjD,QAAQ2E,cAAckI,gBACtC,CAOA,wBAAOlH,CAAkBmH,GACvB,OAAOzN,GAAWuN,kBAAoBE,CACxC,CAOA,gBAAaC,CAAUC,G,0CACrB,OAAKA,QAIQ3N,GAAW4N,cAAcD,GAH7B3N,GAAW6N,kBAItB,G,CAKA,uBAAaA,G,0CACX,OAAO,IAAInV,SAASoE,GAAY8G,OAAOkK,QAAQC,WAAW,CAAEC,UAAU,GAAQlR,IAChF,G,CAOA,oBAAa8Q,CAAcD,G,0CACzB,OAAO,IAAIjV,SAASoE,GAAY8G,OAAOkK,QAAQtF,IAAImF,EAAU,CAAEK,UAAU,GAAQlR,IACnF,G,CAEA,mBAAamR,CAAahJ,G,0CACxB,OAAO,IAAIvM,SAASoE,IAClB8G,OAAOkK,QAAQ/Z,OAAOkR,GAAgBiJ,GAAc,mCAClD,IAAKlO,GAAWC,YACd,OAAOnD,EAAQoR,GAIjB,MAAMC,QAAmB,IAAIzV,SAAkCoE,IAC7D8G,OAAOkK,QAAQ/G,OAAO,CAAEqH,YAAa,CAAC,YAAcN,GAAYhR,EAAQgR,IAAS,IAG7EO,EAAaF,EAAWnH,MAAMf,GAAWA,EAAOpF,KAAOqN,EAAUrN,KAGvE,GAAkB,MAAdwN,IAAuBA,EAAWxN,GACpC,OAAO/D,EAAQoR,GAIjBtK,OAAOkK,QAAQQ,OAAOD,EAAWxN,GAAI,CAAE0N,SAAS,IAAQ,KAEtD3K,OAAOkK,QAAQQ,OAAOJ,EAAUrN,GAAI,CAAE0N,SAAS,IAAQ,KACrDzR,EAAQoR,EAAU,GAClB,GAEN,KAAE,GAEN,G,CAOA,mBAAaM,CAAab,G,0CACxB,OAAO,IAAIjV,SAASoE,GAAY8G,OAAOkK,QAAQvW,OAAOoW,GAAU,IAAM7Q,OACxE,G,CAQA,6BAAa2R,CACXd,EACA1I,G,0CAEA,OAAO,IAAIvM,SAASoE,GAClB8G,OAAOkK,QAAQQ,OAAOX,EAAU1I,GAAS,KACvCnI,GAAS,KAGf,G,CAOA,kBAAa4R,CAAYf,G,gDACjB3N,GAAWyO,uBAAuBd,EAAU,CAAEY,SAAS,GAC/D,G,CAEA,gCAAaI,G,0CACX,aAAa3O,GAAW4O,qCAAqC,CAC3DC,QAAQ,EACRlB,SAAU/J,OAAOkK,QAAQgB,mBAE7B,G,CAEA,6BAAOC,CAAuBC,GAG5B,OAFepM,EAA4BG,UAAUiM,IAGnD,KAAKtR,EAAWI,gBAChB,KAAKJ,EAAWU,cACd,OAAOd,EACT,KAAKI,EAAWM,eAChB,KAAKN,EAAWY,aACd,OAAOhB,EACT,KAAKI,EAAWO,cAChB,KAAKP,EAAWa,YACd,OAAOjB,EACT,KAAKI,EAAWQ,iBAChB,KAAKR,EAAWgB,eACd,OAAOpB,EACT,QACE,OAAOA,EAEb,CAOA,aAAa2R,CAAOxF,G,0CAClB,OAAKA,EAIDzJ,GAAWsG,kBAAkB,SAClB1C,OAAO8G,KAAKlC,IAAIiB,GAGxB,IAAI/Q,SAASoE,GAClB8G,OAAO8G,KAAKlC,IAAIiB,GAAQyF,IACtBpS,EAAQoS,EAAI,MATP,IAYX,G,CAEA,8BAAaC,G,0CACX,aAAanP,GAAW4O,qCAAqC,CAC3DC,QAAQ,EACRO,eAAe,GAEnB,G,CAEA,oBAAaC,G,0CACX,aAAarP,GAAWsP,UAAU,CAChCT,QAAQ,GAEZ,G,CAKA,oBAAaU,G,0CACX,OAAIvP,GAAWsG,kBAAkB,SAClB1C,OAAO8G,KAAKqD,aAGpB,IAAIrV,SAASoE,GAClB8G,OAAO8G,KAAKqD,YAAYmB,IACtBpS,EAAQoS,EAAI,KAGlB,G,CAEA,gBAAaI,CAAUrK,G,0CACrB,OAAO,IAAIvM,SAASoE,IAClB8G,OAAO8G,KAAK8E,MAAMvK,GAAUyF,IAC1B5N,EAAQ4N,EAAK,GACb,GAEN,G,CAEA,qBAAa+E,CAAexK,G,0CAC1B,MAAMyF,QAAa1K,GAAWsP,UAAUrK,GACxC,OAAIyF,EAAKnW,OAAS,EACTmW,EAAK,GAGP,IACT,G,CA8BA,2CAAakE,CACX3J,G,gDAEA,IAAKjF,GAAWC,YACd,aAAaD,GAAWyP,eAAexK,GAGzC,MAAMyK,SAAyB1P,GAAW6N,oBAAoBhN,GACxD6J,QAAa1K,GAAWsP,UAAUrK,GAExC,OAAIyF,EAAKnW,QAAU,GAAwB,MAAnBmb,EACfhF,EAAK,GAGyC,QAAhD,EAAAA,EAAK1D,MAAM2I,GAAMA,EAAEhC,WAAa+B,WAAgB,QAAIhF,EAAK,EAClE,G,CAEA,yBAAOkF,CACLV,EACArP,EACAC,EAAY,MAEZ,MAAM+P,EAAW,CACfhQ,QAASA,GAOX,OAJY,MAARC,IACF+P,EAAI/P,KAAOA,GAGNE,GAAW8P,eAAeZ,EAAKW,EACxC,CAEA,qBAAaC,CAAc,K,2CACzBZ,EACAW,EACA5K,EAA0C,KAC1C8K,GAAgB,GAEhB,GAAKb,GAAQA,EAAIrO,GAIjB,OAAO,IAAInI,SAAmB,CAACoE,EAASC,KACtC6G,OAAO8G,KAAK/F,YAAYuK,EAAIrO,GAAIgP,EAAK5K,GAAUlE,IACzC6C,OAAOjD,QAAQiE,WAAamL,GAE9BhT,IAEFD,EAAQiE,EAAS,GACjB,GAEN,G,CAEA,sBAAOiP,CACLvG,EACAxX,EACAgT,EACAgL,GAEArM,OAAO8G,KAAK/F,YAA2B8E,EAAOxX,EAASgT,EAASgL,EAClE,CAEA,sBAAaC,CAAgBrI,G,0CAI3B,OAAOjE,OAAOkK,QAAQqC,UAAUpD,YAAYlF,EAC9C,G,CAOA,wBAAOuI,GACL,YAAkD,IAAvCxM,OAAOyM,UAAUD,kBACnB,KAGFxM,OAAOyM,UAAUD,mBAC1B,CAQA,uBAAOE,CAAiBrK,GACtB,YAAyB,IAAXA,GAA0BA,IAAWjG,GAAWoQ,mBAChE,CASA,wBAAOG,CAAkBC,GACvB,YAAyC,IAA9B5M,OAAOyM,UAAUI,SACnB,GAGF7M,OAAOyM,UAAUI,SAASD,EACnC,CAMA,kBAAa9L,G,0CACX,OAAOhM,QAAQoE,QAAQkD,GAAWuQ,kBAAkB,CAAEG,KAAM,UAAWnc,OAAS,EAClF,G,CAEA,mBAAO2Q,CAAa8G,EAAa6C,GAAS,GACxC,OAAO,IAAInW,SAASoE,GAClB8G,OAAO8G,KAAK3W,OAAO,CAAEiY,IAAKA,EAAK6C,OAAQA,IAAWK,GAAQpS,EAAQoS,MAEtE,CAOA,sBAAayB,CACXC,G,0CAEA,OAAO,IAAIlY,SAASoE,GAAY8G,OAAOoJ,cAAc6D,SAASD,EAAS9T,IACzE,G,CAOA,yBAAagU,CACXrH,G,0CAEA,OAAO,IAAI/Q,SAASoE,GAAY8G,OAAOoJ,cAAc+D,aAAa,CAAEtH,SAAS3M,IAC/E,G,CASA,sBAAOkU,CACL7a,EACA0R,GAMA7H,GAAW+M,YAAYnJ,OAAOjD,QAAQsQ,UAAWpJ,EACnD,CAEA,uBAAOqJ,GACL,OAAO,IAAInV,GAAqBT,IAC9B,MAAMtC,EAAW/G,IACfqJ,EAAW9G,KAAKvC,EAAQ,EAK1B,OAFA+N,GAAW+M,YAAYnJ,OAAOjD,QAAQsQ,UAAWjY,GAE1C,IAAMgH,GAAWqN,eAAezJ,OAAOjD,QAAQsQ,UAAWjY,EAAQ,GAE7E,CAEA,4BAAOmY,CACLtJ,GAEA7H,GAAW+M,YAAYnJ,OAAOwN,QAAQC,UAAWxJ,EACnD,CAWA,kBAAOkF,CACLuE,EACAzJ,GAEAyJ,EAAMvE,YAAYlF,GAEd7H,GAAWC,cAAgBD,GAAWsQ,iBAAiBiB,QACzDvR,GAAWwR,4BAA4Bxc,KAAK,CAACsc,EAAOzJ,IACpD7H,GAAWyR,uBAEf,CAOA,qBAAOpE,CACLiE,EACAzJ,GAIA,GAFAyJ,EAAMjE,eAAexF,GAEjB7H,GAAWC,cAAgBD,GAAWsQ,iBAAiBiB,MAAO,CAChE,MAAM7a,EAAQsJ,GAAWwR,4BAA4BE,WAAU,EAAEC,EAAQC,KAChEA,GAAiB/J,KAEX,IAAXnR,GACFsJ,GAAWwR,4BAA4B5a,OAAOF,EAAO,EAEzD,CACF,CAGQ,2BAAO+a,GAGbF,KAAKM,iBAAiB,YAAY,KAChC,IAAK,MAAOP,EAAOzJ,KAAa7H,GAAWwR,4BACzCF,EAAMjE,eAAexF,EACvB,GAEJ,CAEA,kBAAOlD,CAAYrJ,EAAoBwW,EAAW,CAAC,GACjD,MAAM7f,EAAUkB,OAAO4e,OAAO,CAAC,EAAG,CAAElS,QAASvE,GAAcwW,GAC3D,OAAOlO,OAAOjD,QAAQgE,YAAY1S,EACpC,CAEA,8BAAOwV,CAAmCnM,EAAoBwW,EAAW,CAAC,GACxE,MAAM7f,EAAUkB,OAAO4e,OAAO,CAAC,EAAG,CAAElS,QAASvE,GAAcwW,GAC3D,OAAO,IAAIpZ,SAAoBoE,GAAY8G,OAAOjD,QAAQgE,YAAY1S,EAAS6K,IACjF,CAEA,eAAakV,CAASvI,G,0CAGpB7F,OAAO8G,KAAK4D,OAAO7E,EAAO,CAAEoF,QAAQ,EAAMoD,aAAa,GACzD,G,CAEA,iBAAOC,CAAWvM,GACZ3F,GAAWmS,oBAAsBnS,GAAWoS,mBAM9C1R,QAAQgK,KAAK4D,OAAO,CAAEO,QAAQ,IAAQwD,QAAQ1M,EAAI2M,OAElD3M,EAAI2M,OAER,CAEA,eAAOC,GACL,OAAO,CACT,CAEA,oBAAOC,GACL,OAAO5O,OAAO6O,KAAKD,eACrB,CAKA,sBAAOE,GAIL,OAAIvgB,KAAK8N,YACAsR,KAAKoB,SAASC,SAEhBhP,OAAOjD,QAAQiS,QACxB,CAQA,wBAAOC,CAAkBC,GAAoB,GAC3C,MAAMC,EAAQ/S,GAAWuQ,oBACzB,IAAKwC,EAAMxe,OACT,OAGF,MAAMye,EAAczB,KAAKoB,SAASM,KAClCF,EACGlhB,QAAQqhB,GAAyB,MAAnBA,EAAEP,SAASM,OAAiBC,EAAEP,SAASM,KAAK9a,SAAS,qBACnEtG,QAAQqhB,IAAOJ,GAAqBI,EAAEP,SAASM,OAASD,IACxDrW,SAASuW,GAAMA,EAAEP,SAASC,UAC/B,CAEA,oBAAOO,CAAcC,GACnB,OAAIpT,GAAWmS,mBACNzR,QAAQC,QAAQwS,cAAcC,GAC5BpT,GAAWqT,YACbzP,OAAOjD,QAAQwS,cAAcC,QAD/B,CAGT,CAEA,wBAAOE,CAAkBC,GACvB,OAAIvT,GAAWmS,mBACNzR,QAAQuL,YAAYuH,QAAQD,GAE9B,IAAI7a,SAASoE,IAClB8G,OAAOqI,YAAYuH,QAAQD,EAAYzW,EAAQ,GAEnD,CAOA,yBAAa2W,CACXxH,G,0CAEA,OAAO,IAAIvT,SAASoE,GAClB8G,OAAOqI,YAAYC,SAAS,CAAED,gBAAgBtD,GAAW7L,EAAQ6L,MAErE,G,CAEA,sBAAOxB,GACL,OAAInH,GAAWmS,mBACNzR,QAAQC,QAAQwG,kBAElB,IAAIzO,SAASoE,IAClB8G,OAAOjD,QAAQwG,gBAAgBrK,EAAQ,GAE3C,CAKA,uBAAO4W,GACL,OAAO1T,GAAWsG,kBAAkB,GAAK1C,OAAO+P,OAAS/P,OAAOgQ,aAClE,CAEA,uBAAOC,CACLlO,G,MAEA,MAAMmO,EAAalR,EAA4BG,UAAU4C,GACzD,OAAImO,IAAepW,EAAWK,iBACrB2C,QAAQqT,cAGbD,IAAepW,EAAWM,eACd,QAAP,EAAA2H,EAAI9B,WAAG,eAAEkQ,cAGX,IACT,CAEA,wBAAOC,GACL,OAAO,IAAItb,SAASoE,IAClB8G,OAAO8G,KAAKsJ,kBAAkB,KAAM,CAAEC,OAAQ,OAASnX,EAAQ,GAEnE,CAUA,yBAAOoX,CACLzK,EACAmH,EACAuD,G,MAIA,GAAInU,GAAWsG,kBAAkB,GAAI,CACnC,MAAMgC,EAA2C,CAC/CmB,SAWF,MAR+B,iBAApBmH,EAAQlH,UACjBpB,EAAOiC,SAAW,CAACqG,EAAQlH,YAGT,QAAf,EAAApB,EAAOiC,gBAAQ,eAAEhW,SAAUqc,EAAQpH,YACtClB,EAAOkB,UAAYoH,EAAQpH,WAGtB5F,OAAOwE,UAAU0C,cAAc,CACpCxC,SACAyB,MAAO6G,EAAQtG,KAAO,CAACsG,EAAQtG,MAAQ,KACvC8J,kBAAqC,mBAAlBxD,EAAQ3G,MAC3BoK,OAAOF,aAAmB,EAAnBA,EAAqBE,QAAS,YAEzC,CAEA,OAAO,IAAI3b,SAASoE,IAClB8G,OAAO8G,KAAKI,cAAcrB,EAAOmH,GAAUjI,IACzC7L,EAAQ6L,EAAO,GACf,GAEN,CAKA,wCAAa2L,G,0CACX,WAAYtU,GAAWyT,mBAAmB,CAAC,aACzC,OAAO,EAGT,MAAMc,EAAuB3D,GACA,iCAA3BA,EAAQ4D,iBAAsD5D,EAAQnc,MAElEggB,QAA2C,IAAI/b,SAASoE,GAC5D8G,OAAO8Q,QAAQC,SAASC,uBAAuBpM,IAAI,CAAC,GAAIoI,GACtD9T,EAAQyX,EAAoB3D,QAI1BiE,QAA8C,IAAInc,SAASoE,GAC/D8G,OAAO8Q,QAAQC,SAASG,0BAA0BtM,IAAI,CAAC,GAAIoI,GACzD9T,EAAQyX,EAAoB3D,QAI1BmE,QAA0C,IAAIrc,SAASoE,GAC3D8G,OAAO8Q,QAAQC,SAASK,sBAAsBxM,IAAI,CAAC,GAAIoI,GACrD9T,EAAQyX,EAAoB3D,QAIhC,OAAO6D,GAA6BI,GAAgCE,CACtE,G,CAOA,2CAAaE,CAAqCxgB,G,gDAC1CmP,OAAO8Q,QAAQC,SAASC,uBAAuBM,IAAI,CAAEzgB,gBACrDmP,OAAO8Q,QAAQC,SAASG,0BAA0BI,IAAI,CAAEzgB,gBACxDmP,OAAO8Q,QAAQC,SAASK,sBAAsBE,IAAI,CAAEzgB,SAC5D,G,CAOA,gCAAa0gB,CACXvN,G,gDAEA,MAAuB,oBAAZlH,UAAmD,QAAtB,EAAAA,QAAQ0U,sBAAc,eAAEC,gBACjD3U,QAAQ0U,eAAeC,SAASzN,SAGlCD,GAA+BC,EAC9C,G,CAOA,gCAAa0N,CACXjK,G,gDAEMzH,OAAOwE,UAAUV,uBAAuB2D,EAChD,G,CAOA,kCAAakK,CACX1jB,G,gDAEM+R,OAAOwE,UAAUoN,yBAAyB3jB,EAClD,G,EArtBO,GAAAsgB,mBAAiD,oBAAZzR,QACrC,GAAAT,aCdwC,IAA7CoB,UAAUsC,UAAUhN,QAAQ,cACiB,IAA7C0K,UAAUsC,UAAUhN,QAAQ,cACmB,IAA/C0K,UAAUsC,UAAUhN,QAAQ,cDavB,GAAA0c,aAAwBrT,GAAWC,aAAiC,oBAAX2D,OACzD,GAAAwO,oBACwC,IAA7C/Q,UAAUsC,UAAUhN,QAAQ,cAAkE,IAA5C0K,UAAUsC,UAAUhN,QAAQ,WAyXjE,GAAA6a,4BAGT,G,2SElYR,MAAMiE,GAAN,cACU,KAAAjU,kBAAuC,IAAI9P,GAAkB,GACpD,KAAAgkB,yBAAsE,CACrFC,yBAA0B,EAAG1jB,aAAcE,KAAKyjB,+BAA+B3jB,GAC/E4jB,2BAA4B,IAAM1jB,KAAK2jB,mCACvCC,gBAAiB,EAAG9jB,aAAcE,KAAK6jB,sBAAsB/jB,EAAQgkB,KACrEC,iBAAkB,EAAGjkB,aAAcE,KAAKgkB,uBAAuBlkB,EAAQgkB,IAAKhkB,EAAQwC,OACpF2hB,mBAAoB,EAAGnkB,aAAcE,KAAKkkB,yBAAyBpkB,EAAQgkB,MAoDrE,KAAAK,uBAAyB,CAC/BrkB,EACAskB,EACAC,KAEA,MAAMxd,EAAwC7G,KAAKujB,yBAAyBzjB,aAAO,EAAPA,EAAS4N,SACrF,IAAK7G,EACH,OAGF,MAAMyd,EAAkBzd,EAAQ,CAAE/G,UAASskB,WAC3C,OAAKE,GAIL/d,QAAQoE,QAAQ2Z,GACbpQ,MAAMtF,GAAayV,EAAazV,KAChC2V,OAAOhkB,GACNP,KAAKqP,kBAAkB9O,MAAM,6CAA8CA,MAExE,QATP,CASW,CAEf,CApEE,IAAAikB,GACExkB,KAAKykB,+BACP,CAOc,8BAAAhB,CAA+B3jB,G,gDACrC,EAAwBgP,KAAKsQ,KAAMtf,EAAQkP,KACnD,G,CAKc,gCAAA2U,G,0CACZ,aAAa,EAAwBpU,KAAK6P,KAC5C,G,CAEQ,qBAAAyE,CAAsBC,GAC5B,OAAO1E,KAAKsF,aAAaC,QAAQb,EACnC,CAEQ,sBAAAE,CAAuBF,EAAaxhB,GAC1C8c,KAAKsF,aAAaE,QAAQd,EAAKxhB,EACjC,CAEQ,wBAAA4hB,CAAyBJ,GAC/B1E,KAAKsF,aAAaG,WAAWf,EAC/B,CAKQ,6BAAAW,GACN5W,GAAWgR,gBAAgB,qBAAsB7e,KAAKmkB,uBACxD,GAkC0B,IAAIb,IACZkB,M", "sources": ["webpack:///../../libs/common/src/platform/enums/log-level-type.enum.ts", "webpack:///../../libs/common/src/platform/services/console-log.service.ts", "webpack:///../../node_modules/tslib/tslib.es6.mjs", "webpack:///../../../../src/internal/util/isFunction.ts", "webpack:///../../../../src/internal/util/UnsubscriptionError.ts", "webpack:///../../../../src/internal/util/createErrorClass.ts", "webpack:///../../../../src/internal/util/arrRemove.ts", "webpack:///../../../src/internal/Subscription.ts", "webpack:///../../../src/internal/config.ts", "webpack:///../../../../src/internal/scheduler/timeoutProvider.ts", "webpack:///../../../../src/internal/util/noop.ts", "webpack:///../../../src/internal/NotificationFactories.ts", "webpack:///../../../../src/internal/util/errorContext.ts", "webpack:///../../../src/internal/Subscriber.ts", "webpack:///../../../../src/internal/util/reportUnhandledError.ts", "webpack:///../../../../src/internal/symbol/observable.ts", "webpack:///../../../../src/internal/util/identity.ts", "webpack:///../../../../src/internal/util/pipe.ts", "webpack:///../../../src/internal/Observable.ts", "webpack:///../../libs/common/src/autofill/constants/match-patterns.ts", "webpack:///../../libs/common/src/autofill/constants/index.ts", "webpack:///../../libs/common/src/enums/client-type.enum.ts", "webpack:///../../libs/common/src/enums/device-type.enum.ts", "webpack:///../../libs/common/src/enums/event-system-user.enum.ts", "webpack:///../../libs/common/src/enums/event-type.enum.ts", "webpack:///../../libs/common/src/enums/http-status-code.enum.ts", "webpack:///../../libs/common/src/enums/integration-type.enum.ts", "webpack:///../../libs/common/src/enums/native-messaging-version.enum.ts", "webpack:///../../libs/common/src/enums/notification-type.enum.ts", "webpack:///./src/browser/safariApp.ts", "webpack:///./src/platform/services/browser-clipboard.service.ts", "webpack:///./src/platform/services/platform-utils/browser-platform-utils.service.ts", "webpack:///./src/platform/browser/browser-api.register-content-scripts-polyfill.ts", "webpack:///./src/platform/browser/browser-api.ts", "webpack:///../../libs/platform/src/services/browser-service.ts", "webpack:///./src/platform/offscreen-document/offscreen-document.ts"], "sourcesContent": ["// FIXME: update to use a const object instead of a typescript enum\n// eslint-disable-next-line @bitwarden/platform/no-enums\nexport enum LogLevelType {\n  Debug,\n  Info,\n  Warning,\n  Error,\n}\n", "// FIXME: Update this file to be type safe and remove this and next line\n// @ts-strict-ignore\nimport { LogService as LogServiceAbstraction } from \"../abstractions/log.service\";\nimport { LogLevelType } from \"../enums/log-level-type.enum\";\n\nexport class ConsoleLogService implements LogServiceAbstraction {\n  protected timersMap: Map<string, [number, number]> = new Map();\n\n  constructor(\n    protected isDev: boolean,\n    protected filter: (level: LogLevelType) => boolean = null,\n  ) {}\n\n  debug(message?: any, ...optionalParams: any[]) {\n    if (!this.isDev) {\n      return;\n    }\n    this.write(LogLevelType.Debug, message, ...optionalParams);\n  }\n\n  info(message?: any, ...optionalParams: any[]) {\n    this.write(LogLevelType.Info, message, ...optionalParams);\n  }\n\n  warning(message?: any, ...optionalParams: any[]) {\n    this.write(LogLevelType.Warning, message, ...optionalParams);\n  }\n\n  error(message?: any, ...optionalParams: any[]) {\n    this.write(LogLevelType.Error, message, ...optionalParams);\n  }\n\n  write(level: LogLevelType, message?: any, ...optionalParams: any[]) {\n    if (this.filter != null && this.filter(level)) {\n      return;\n    }\n\n    switch (level) {\n      case LogLevelType.Debug:\n        // eslint-disable-next-line\n        console.log(message, ...optionalParams);\n        break;\n      case LogLevelType.Info:\n        // eslint-disable-next-line\n        console.log(message, ...optionalParams);\n        break;\n      case LogLevelType.Warning:\n        // eslint-disable-next-line\n        console.warn(message, ...optionalParams);\n        break;\n      case LogLevelType.Error:\n        // eslint-disable-next-line\n        console.error(message, ...optionalParams);\n        break;\n      default:\n        break;\n    }\n  }\n}\n", "/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "export const CardExpiryDateDelimiters: string[] = [\"/\", \"-\", \".\", \" \"];\n\n// `CardExpiryDateDelimiters` is not intended solely for regex consumption,\n// so we need to format it here\nexport const ExpiryDateDelimitersPattern =\n  \"\\\\\" +\n  CardExpiryDateDelimiters.join(\"\\\\\")\n    // replace space character with the regex whitespace character class\n    .replace(\" \", \"s\");\n\nexport const MonthPattern = \"(([1]{1}[0-2]{1})|(0?[1-9]{1}))\";\n\n// Because we're dealing with expiry dates, we assume the year will be in current or next century (as of 2024)\nexport const ExpiryFullYearPattern = \"2[0-1]{1}\\\\d{2}\";\n\nexport const DelimiterPatternExpression = new RegExp(`[${ExpiryDateDelimitersPattern}]`, \"g\");\n\nexport const IrrelevantExpiryCharactersPatternExpression = new RegExp(\n  // \"nor digits\" to ensure numbers are removed from guidance pattern, which aren't covered by ^\\w\n  `[^\\\\d${ExpiryDateDelimitersPattern}]`,\n  \"g\",\n);\n\nexport const MonthPatternExpression = new RegExp(`^${MonthPattern}$`);\n\nexport const ExpiryFullYearPatternExpression = new RegExp(`^${ExpiryFullYearPattern}$`);\n", "export const TYPE_CHECK = {\n  FUNCTION: \"function\",\n  NUMBER: \"number\",\n  STRING: \"string\",\n} as const;\n\nexport const EVENTS = {\n  CHANGE: \"change\",\n  INPUT: \"input\",\n  KEYDOWN: \"keydown\",\n  KEYPRESS: \"keypress\",\n  <PERSON><PERSON><PERSON><PERSON>: \"keyup\",\n  BL<PERSON>: \"blur\",\n  CL<PERSON><PERSON>: \"click\",\n  FOCUS: \"focus\",\n  FOCUSIN: \"focusin\",\n  FOCUSOUT: \"focusout\",\n  SCROLL: \"scroll\",\n  RESIZE: \"resize\",\n  DOMCONTENTLOADED: \"DOMContentLoaded\",\n  LOAD: \"load\",\n  MESSAGE: \"message\",\n  VISIBILITYCHANGE: \"visibilitychange\",\n  MOUSEENTER: \"mouseenter\",\n  MOUSELEAVE: \"mouseleave\",\n  MOUSEUP: \"mouseup\",\n  MOUSEOUT: \"mouseout\",\n  SUBMIT: \"submit\",\n} as const;\n\nexport const ClearClipboardDelay = {\n  Never: null as null,\n  TenSeconds: 10,\n  TwentySeconds: 20,\n  ThirtySeconds: 30,\n  OneMinute: 60,\n  TwoMinutes: 120,\n  FiveMinutes: 300,\n} as const;\n\n/* Ids for context menu items and messaging events */\nexport const AUTOFILL_CARD_ID = \"autofill-card\";\nexport const AUTOFILL_ID = \"autofill\";\nexport const SHOW_AUTOFILL_BUTTON = \"show-autofill-button\";\nexport const AUTOFILL_IDENTITY_ID = \"autofill-identity\";\nexport const COPY_IDENTIFIER_ID = \"copy-identifier\";\nexport const COPY_PASSWORD_ID = \"copy-password\";\nexport const COPY_USERNAME_ID = \"copy-username\";\nexport const COPY_VERIFICATION_CODE_ID = \"copy-totp\";\nexport const CREATE_CARD_ID = \"create-card\";\nexport const CREATE_IDENTITY_ID = \"create-identity\";\nexport const CREATE_LOGIN_ID = \"create-login\";\nexport const GENERATE_PASSWORD_ID = \"generate-password\";\nexport const NOOP_COMMAND_SUFFIX = \"noop\";\nexport const ROOT_ID = \"root\";\nexport const SEPARATOR_ID = \"separator\";\nexport const UPDATE_PASSWORD = \"update-password\";\n\nexport const NOTIFICATION_BAR_LIFESPAN_MS = 150000; // 150 seconds\n\nexport const AUTOFILL_OVERLAY_HANDLE_REPOSITION = \"autofill-overlay-handle-reposition-event\";\n\nexport const AUTOFILL_OVERLAY_HANDLE_SCROLL = \"autofill-overlay-handle-scroll-event\";\n\nexport const UPDATE_PASSKEYS_HEADINGS_ON_SCROLL = \"update-passkeys-headings-on-scroll\";\n\nexport const AUTOFILL_TRIGGER_FORM_FIELD_SUBMIT = \"autofill-trigger-form-field-submit\";\n\nexport const AutofillOverlayVisibility = {\n  Off: 0,\n  OnButtonClick: 1,\n  OnFieldFocus: 2,\n} as const;\n\nexport const BrowserClientVendors = {\n  Chrome: \"Chrome\",\n  Opera: \"Opera\",\n  Edge: \"Edge\",\n  Vivaldi: \"Vivaldi\",\n  Unknown: \"Unknown\",\n} as const;\n\nexport const BrowserShortcutsUris = {\n  Chrome: \"chrome://extensions/shortcuts\",\n  Opera: \"opera://extensions/shortcuts\",\n  Edge: \"edge://extensions/shortcuts\",\n  Vivaldi: \"vivaldi://extensions/shortcuts\",\n  Unknown: \"https://bitwarden.com/help/keyboard-shortcuts\",\n} as const;\n\nexport const DisablePasswordManagerUris = {\n  Chrome: \"chrome://settings/autofill\",\n  Opera: \"opera://settings/autofill\",\n  Edge: \"edge://settings/passwords\",\n  Vivaldi: \"vivaldi://settings/autofill\",\n  Unknown: \"https://bitwarden.com/help/disable-browser-autofill/\",\n} as const;\n\nexport const ExtensionCommand = {\n  AutofillCommand: \"autofill_cmd\",\n  AutofillCard: \"autofill_card\",\n  AutofillIdentity: \"autofill_identity\",\n  AutofillLogin: \"autofill_login\",\n  OpenAutofillOverlay: \"open_autofill_overlay\",\n  GeneratePassword: \"generate_password\",\n  OpenPopup: \"open_popup\",\n  LockVault: \"lock_vault\",\n  NoopCommand: \"noop\",\n} as const;\n\nexport type ExtensionCommandType = (typeof ExtensionCommand)[keyof typeof ExtensionCommand];\n\nexport const CLEAR_NOTIFICATION_LOGIN_DATA_DURATION = 60 * 1000; // 1 minute\n\nexport const MAX_DEEP_QUERY_RECURSION_DEPTH = 4;\n\nexport * from \"./match-patterns\";\n", "// FIXME: update to use a const object instead of a typescript enum\n// eslint-disable-next-line @bitwarden/platform/no-enums\nexport enum ClientType {\n  Web = \"web\",\n  Browser = \"browser\",\n  Desktop = \"desktop\",\n  // Mobile = \"mobile\",\n  Cli = \"cli\",\n  // DirectoryConnector = \"connector\",\n}\n", "// FIXME: update to use a const object instead of a typescript enum\n// eslint-disable-next-line @bitwarden/platform/no-enums\nexport enum DeviceType {\n  Android = 0,\n  iOS = 1,\n  ChromeExtension = 2,\n  FirefoxExtension = 3,\n  OperaExtension = 4,\n  EdgeExtension = 5,\n  WindowsDesktop = 6,\n  MacOsDesktop = 7,\n  LinuxDesktop = 8,\n  ChromeBrowser = 9,\n  FirefoxBrowser = 10,\n  OperaBrowser = 11,\n  EdgeBrowser = 12,\n  IEBrowser = 13,\n  UnknownBrowser = 14,\n  AndroidAmazon = 15,\n  UWP = 16,\n  SafariBrowser = 17,\n  VivaldiBrowser = 18,\n  VivaldiExtension = 19,\n  SafariExtension = 20,\n  SDK = 21,\n  Server = 22,\n  WindowsCLI = 23,\n  MacOsCLI = 24,\n  LinuxCLI = 25,\n}\n\n/**\n * Device type metadata\n * Each device type has a category corresponding to the client type and platform (Android, iOS, Chrome, Firefox, etc.)\n */\ninterface DeviceTypeMetadata {\n  category: \"mobile\" | \"extension\" | \"webVault\" | \"desktop\" | \"cli\" | \"sdk\" | \"server\";\n  platform: string;\n}\n\nexport const DeviceTypeMetadata: Record<DeviceType, DeviceTypeMetadata> = {\n  [DeviceType.Android]: { category: \"mobile\", platform: \"Android\" },\n  [DeviceType.iOS]: { category: \"mobile\", platform: \"iOS\" },\n  [DeviceType.AndroidAmazon]: { category: \"mobile\", platform: \"Amazon\" },\n  [DeviceType.ChromeExtension]: { category: \"extension\", platform: \"Chrome\" },\n  [DeviceType.FirefoxExtension]: { category: \"extension\", platform: \"Firefox\" },\n  [DeviceType.OperaExtension]: { category: \"extension\", platform: \"Opera\" },\n  [DeviceType.EdgeExtension]: { category: \"extension\", platform: \"Edge\" },\n  [DeviceType.VivaldiExtension]: { category: \"extension\", platform: \"Vivaldi\" },\n  [DeviceType.SafariExtension]: { category: \"extension\", platform: \"Safari\" },\n  [DeviceType.ChromeBrowser]: { category: \"webVault\", platform: \"Chrome\" },\n  [DeviceType.FirefoxBrowser]: { category: \"webVault\", platform: \"Firefox\" },\n  [DeviceType.OperaBrowser]: { category: \"webVault\", platform: \"Opera\" },\n  [DeviceType.EdgeBrowser]: { category: \"webVault\", platform: \"Edge\" },\n  [DeviceType.IEBrowser]: { category: \"webVault\", platform: \"IE\" },\n  [DeviceType.SafariBrowser]: { category: \"webVault\", platform: \"Safari\" },\n  [DeviceType.VivaldiBrowser]: { category: \"webVault\", platform: \"Vivaldi\" },\n  [DeviceType.UnknownBrowser]: { category: \"webVault\", platform: \"Unknown\" },\n  [DeviceType.WindowsDesktop]: { category: \"desktop\", platform: \"Windows\" },\n  [DeviceType.MacOsDesktop]: { category: \"desktop\", platform: \"macOS\" },\n  [DeviceType.LinuxDesktop]: { category: \"desktop\", platform: \"Linux\" },\n  [DeviceType.UWP]: { category: \"desktop\", platform: \"Windows UWP\" },\n  [DeviceType.WindowsCLI]: { category: \"cli\", platform: \"Windows\" },\n  [DeviceType.MacOsCLI]: { category: \"cli\", platform: \"macOS\" },\n  [DeviceType.LinuxCLI]: { category: \"cli\", platform: \"Linux\" },\n  [DeviceType.SDK]: { category: \"sdk\", platform: \"\" },\n  [DeviceType.Server]: { category: \"server\", platform: \"\" },\n};\n", "// Note: the enum key is used to describe the EventSystemUser in the UI. Be careful about changing it.\n// FIXME: update to use a const object instead of a typescript enum\n// eslint-disable-next-line @bitwarden/platform/no-enums\nexport enum EventSystemUser {\n  SCIM = 1,\n  DomainVerification = 2,\n  PublicApi = 3,\n}\n", "// Increment by 100 for each new set of events\n// FIXME: update to use a const object instead of a typescript enum\n// eslint-disable-next-line @bitwarden/platform/no-enums\nexport enum EventType {\n  User_LoggedIn = 1000,\n  User_ChangedPassword = 1001,\n  User_Updated2fa = 1002,\n  User_Disabled2fa = 1003,\n  User_Recovered2fa = 1004,\n  User_FailedLogIn = 1005,\n  User_FailedLogIn2fa = 1006,\n  User_ClientExportedVault = 1007,\n  User_UpdatedTempPassword = 1008,\n  User_MigratedKeyToKeyConnector = 1009,\n  User_RequestedDeviceApproval = 1010,\n  User_TdeOffboardingPasswordSet = 1011,\n\n  Cipher_Created = 1100,\n  Cipher_Updated = 1101,\n  Cipher_Deleted = 1102,\n  Cipher_AttachmentCreated = 1103,\n  Cipher_AttachmentDeleted = 1104,\n  Cipher_Shared = 1105,\n  Cipher_UpdatedCollections = 1106,\n  Cipher_ClientViewed = 1107,\n  Cipher_ClientToggledPasswordVisible = 1108,\n  Cipher_ClientToggledHiddenFieldVisible = 1109,\n  Cipher_ClientToggledCardCodeVisible = 1110,\n  Cipher_ClientCopiedPassword = 1111,\n  Cipher_ClientCopiedHiddenField = 1112,\n  Cipher_ClientCopiedCardCode = 1113,\n  Cipher_ClientAutofilled = 1114,\n  Cipher_SoftDeleted = 1115,\n  Cipher_Restored = 1116,\n  Cipher_ClientToggledCardNumberVisible = 1117,\n  Cipher_ClientToggledTOTPSeedVisible = 1118,\n\n  Collection_Created = 1300,\n  Collection_Updated = 1301,\n  Collection_Deleted = 1302,\n\n  Group_Created = 1400,\n  Group_Updated = 1401,\n  Group_Deleted = 1402,\n\n  OrganizationUser_Invited = 1500,\n  OrganizationUser_Confirmed = 1501,\n  OrganizationUser_Updated = 1502,\n  OrganizationUser_Removed = 1503,\n  OrganizationUser_UpdatedGroups = 1504,\n  OrganizationUser_UnlinkedSso = 1505,\n  OrganizationUser_ResetPassword_Enroll = 1506,\n  OrganizationUser_ResetPassword_Withdraw = 1507,\n  OrganizationUser_AdminResetPassword = 1508,\n  OrganizationUser_ResetSsoLink = 1509,\n  OrganizationUser_FirstSsoLogin = 1510,\n  OrganizationUser_Revoked = 1511,\n  OrganizationUser_Restored = 1512,\n  OrganizationUser_ApprovedAuthRequest = 1513,\n  OrganizationUser_RejectedAuthRequest = 1514,\n  OrganizationUser_Deleted = 1515,\n  OrganizationUser_Left = 1516,\n\n  Organization_Updated = 1600,\n  Organization_PurgedVault = 1601,\n  Organization_ClientExportedVault = 1602,\n  Organization_VaultAccessed = 1603,\n  Organization_EnabledSso = 1604,\n  Organization_DisabledSso = 1605,\n  Organization_EnabledKeyConnector = 1606,\n  Organization_DisabledKeyConnector = 1607,\n  Organization_SponsorshipsSynced = 1608,\n  Organization_CollectionManagementUpdated = 1609,\n\n  Policy_Updated = 1700,\n\n  ProviderUser_Invited = 1800,\n  ProviderUser_Confirmed = 1801,\n  ProviderUser_Updated = 1802,\n  ProviderUser_Removed = 1803,\n\n  ProviderOrganization_Created = 1900,\n  ProviderOrganization_Added = 1901,\n  ProviderOrganization_Removed = 1902,\n  ProviderOrganization_VaultAccessed = 1903,\n\n  OrganizationDomain_Added = 2000,\n  OrganizationDomain_Removed = 2001,\n  OrganizationDomain_Verified = 2002,\n  OrganizationDomain_NotVerified = 2003,\n\n  Secret_Retrieved = 2100,\n}\n", "/**\n * Hypertext Transfer Protocol (HTTP) response status codes.\n *\n * @see {@link https://en.wikipedia.org/wiki/List_of_HTTP_status_codes}\n * src: https://gist.github.com/RWOverdijk/6cef816cfdf5722228e01cc05fd4b094\n */\n// FIXME: update to use a const object instead of a typescript enum\n// eslint-disable-next-line @bitwarden/platform/no-enums\nexport enum HttpStatusCode {\n  /**\n   * The server has received the request headers and the client should proceed to send the request body\n   * (in the case of a request for which a body needs to be sent; for example, a POST request).\n   * Sending a large request body to a server after a request has been rejected for inappropriate headers would be inefficient.\n   * To have a server check the request's headers, a client must send Expect: 100-continue as a header in its initial request\n   * and receive a 100 Continue status code in response before sending the body. The response 417 Expectation Failed indicates the request should not be continued.\n   */\n  Continue = 100,\n\n  /**\n   * The requester has asked the server to switch protocols and the server has agreed to do so.\n   */\n  SwitchingProtocols = 101,\n\n  /**\n   * A WebDAV request may contain many sub-requests involving file operations, requiring a long time to complete the request.\n   * This code indicates that the server has received and is processing the request, but no response is available yet.\n   * This prevents the client from timing out and assuming the request was lost.\n   */\n  Processing = 102,\n\n  // **********************************************************************************************************\n  // 200s - SUCCESS\n  // **********************************************************************************************************\n\n  /**\n   * Standard response for successful HTTP requests.\n   * The actual response will depend on the request method used.\n   * In a GET request, the response will contain an entity corresponding to the requested resource.\n   * In a POST request, the response will contain an entity describing or containing the result of the action.\n   */\n  Ok = 200,\n\n  /**\n   * The request has been fulfilled, resulting in the creation of a new resource.\n   */\n  Created = 201,\n\n  /**\n   * The request has been accepted for processing, but the processing has not been completed.\n   * The request might or might not be eventually acted upon, and may be disallowed when processing occurs.\n   */\n  Accepted = 202,\n\n  /**\n   * SINCE HTTP/1.1\n   * The server is a transforming proxy that received a 200 OK from its origin,\n   * but is returning a modified version of the origin's response.\n   */\n  NonAuthoritativeInformation = 203,\n\n  /**\n   * The server successfully processed the request and is not returning any content.\n   */\n  NoContent = 204,\n\n  /**\n   * The server successfully processed the request, but is not returning any content.\n   * Unlike a 204 response, this response requires that the requester reset the document view.\n   */\n  ResetContent = 205,\n\n  /**\n   * The server is delivering only part of the resource (byte serving) due to a range header sent by the client.\n   * The range header is used by HTTP clients to enable resuming of interrupted downloads,\n   * or split a download into multiple simultaneous streams.\n   */\n  PartialContent = 206,\n\n  /**\n   * The message body that follows is an XML message and can contain a number of separate response codes,\n   * depending on how many sub-requests were made.\n   */\n  MultiStatus = 207,\n\n  /**\n   * The members of a DAV binding have already been enumerated in a preceding part of the (multistatus) response,\n   * and are not being included again.\n   */\n  AlreadyReported = 208,\n\n  /**\n   * The server has fulfilled a request for the resource,\n   * and the response is a representation of the result of one or more instance-manipulations applied to the current instance.\n   */\n  ImUsed = 226,\n\n  // **********************************************************************************************************\n  // 300s - Redirections\n  // **********************************************************************************************************\n\n  /**\n   * Indicates multiple options for the resource from which the client may choose (via agent-driven content negotiation).\n   * For example, this code could be used to present multiple video format options,\n   * to list files with different filename extensions, or to suggest word-sense disambiguation.\n   */\n  MultipleChoices = 300,\n\n  /**\n   * This and all future requests should be directed to the given URI.\n   */\n  MovedPermanently = 301,\n\n  /**\n   * This is an example of industry practice contradicting the standard.\n   * The HTTP/1.0 specification (RFC 1945) required the client to perform a temporary redirect\n   * (the original describing phrase was \"Moved Temporarily\"), but popular browsers implemented 302\n   * with the functionality of a 303 See Other. Therefore, HTTP/1.1 added status codes 303 and 307\n   * to distinguish between the two behaviours. However, some Web applications and frameworks\n   * use the 302 status code as if it were the 303.\n   */\n  Found = 302,\n\n  /**\n   * SINCE HTTP/1.1\n   * The response to the request can be found under another URI using a GET method.\n   * When received in response to a POST (or PUT/DELETE), the client should presume that\n   * the server has received the data and should issue a redirect with a separate GET message.\n   */\n  SeeOther = 303,\n\n  /**\n   * Indicates that the resource has not been modified since the version specified by the request headers If-Modified-Since or If-None-Match.\n   * In such case, there is no need to retransmit the resource since the client still has a previously-downloaded copy.\n   */\n  NotModified = 304,\n\n  /**\n   * SINCE HTTP/1.1\n   * The requested resource is available only through a proxy, the address for which is provided in the response.\n   * Many HTTP clients (such as Mozilla and Internet Explorer) do not correctly handle responses with this status code, primarily for security reasons.\n   */\n  UseProxy = 305,\n\n  /**\n   * No longer used. Originally meant \"Subsequent requests should use the specified proxy.\"\n   */\n  SwitchProxy = 306,\n\n  /**\n   * SINCE HTTP/1.1\n   * In this case, the request should be repeated with another URI; however, future requests should still use the original URI.\n   * In contrast to how 302 was historically implemented, the request method is not allowed to be changed when reissuing the original request.\n   * For example, a POST request should be repeated using another POST request.\n   */\n  TemporaryRedirect = 307,\n\n  /**\n   * The request and all future requests should be repeated using another URI.\n   * 307 and 308 parallel the behaviors of 302 and 301, but do not allow the HTTP method to change.\n   * So, for example, submitting a form to a permanently redirected resource may continue smoothly.\n   */\n  PermanentRedirect = 308,\n\n  // **********************************************************************************************************\n  // 400s - Client / User messed up\n  // **********************************************************************************************************\n\n  /**\n   * The server cannot or will not process the request due to an apparent client error\n   * (e.g., malformed request syntax, too large size, invalid request message framing, or deceptive request routing).\n   */\n  BadRequest = 400,\n\n  /**\n   * Similar to 403 Forbidden, but specifically for use when authentication is required and has failed or has not yet\n   * been provided. The response must include a WWW-Authenticate header field containing a challenge applicable to the\n   * requested resource. See Basic access authentication and Digest access authentication. 401 semantically means\n   * \"unauthenticated\",i.e. the user does not have the necessary credentials.\n   */\n  Unauthorized = 401,\n\n  /**\n   * Reserved for future use. The original intention was that this code might be used as part of some form of digital\n   * cash or micro payment scheme, but that has not happened, and this code is not usually used.\n   * Google Developers API uses this status if a particular developer has exceeded the daily limit on requests.\n   */\n  PaymentRequired = 402,\n\n  /**\n   * The request was valid, but the server is refusing action.\n   * The user might not have the necessary permissions for a resource.\n   */\n  Forbidden = 403,\n\n  /**\n   * The requested resource could not be found but may be available in the future.\n   * Subsequent requests by the client are permissible.\n   */\n  NotFound = 404,\n\n  /**\n   * A request method is not supported for the requested resource;\n   * for example, a GET request on a form that requires data to be presented via POST, or a PUT request on a read-only resource.\n   */\n  MethodNotAllowed = 405,\n\n  /**\n   * The requested resource is capable of generating only content not acceptable according to the Accept headers sent in the request.\n   */\n  NotAcceptable = 406,\n\n  /**\n   * The client must first authenticate itself with the proxy.\n   */\n  ProxyAuthenticationRequired = 407,\n\n  /**\n   * The server timed out waiting for the request.\n   * According to HTTP specifications:\n   * \"The client did not produce a request within the time that the server was prepared to wait. The client MAY repeat the request without modifications at any later time.\"\n   */\n  RequestTimeout = 408,\n\n  /**\n   * Indicates that the request could not be processed because of conflict in the request,\n   * such as an edit conflict between multiple simultaneous updates.\n   */\n  Conflict = 409,\n\n  /**\n   * Indicates that the resource requested is no longer available and will not be available again.\n   * This should be used when a resource has been intentionally removed and the resource should be purged.\n   * Upon receiving a 410 status code, the client should not request the resource in the future.\n   * Clients such as search engines should remove the resource from their indices.\n   * Most use cases do not require clients and search engines to purge the resource, and a \"404 Not Found\" may be used instead.\n   */\n  Gone = 410,\n\n  /**\n   * The request did not specify the length of its content, which is required by the requested resource.\n   */\n  LengthRequired = 411,\n\n  /**\n   * The server does not meet one of the preconditions that the requester put on the request.\n   */\n  PreconditionFailed = 412,\n\n  /**\n   * The request is larger than the server is willing or able to process. Previously called \"Request Entity Too Large\".\n   */\n  PayloadTooLarge = 413,\n\n  /**\n   * The URI provided was too long for the server to process. Often the result of too much data being encoded as a query-string of a GET request,\n   * in which case it should be converted to a POST request.\n   * Called \"Request-URI Too Long\" previously.\n   */\n  UriTooLong = 414,\n\n  /**\n   * The request entity has a media type which the server or resource does not support.\n   * For example, the client uploads an image as image/svg+xml, but the server requires that images use a different format.\n   */\n  UnsupportedMediaType = 415,\n\n  /**\n   * The client has asked for a portion of the file (byte serving), but the server cannot supply that portion.\n   * For example, if the client asked for a part of the file that lies beyond the end of the file.\n   * Called \"Requested Range Not Satisfiable\" previously.\n   */\n  RangeNotSatisfiable = 416,\n\n  /**\n   * The server cannot meet the requirements of the Expect request-header field.\n   */\n  ExpectationFailed = 417,\n\n  /**\n   * This code was defined in 1998 as one of the traditional IETF April Fools' jokes, in RFC 2324, Hyper Text Coffee Pot Control Protocol,\n   * and is not expected to be implemented by actual HTTP servers. The RFC specifies this code should be returned by\n   * teapots requested to brew coffee. This HTTP status is used as an Easter egg in some websites, including Google.com.\n   */\n  IAmATeapot = 418,\n\n  /**\n   * The request was directed at a server that is not able to produce a response (for example because a connection reuse).\n   */\n  MisdirectedRequest = 421,\n\n  /**\n   * The request was well-formed but was unable to be followed due to semantic errors.\n   */\n  UnprocessableEntity = 422,\n\n  /**\n   * The resource that is being accessed is locked.\n   */\n  Locked = 423,\n\n  /**\n   * The request failed due to failure of a previous request (e.g., a PROPPATCH).\n   */\n  FailedDependency = 424,\n\n  /**\n   * The client should switch to a different protocol such as TLS/1.0, given in the Upgrade header field.\n   */\n  UpgradeRequired = 426,\n\n  /**\n   * The origin server requires the request to be conditional.\n   * Intended to prevent \"the 'lost update' problem, where a client\n   * GETs a resource's state, modifies it, and PUTs it back to the server,\n   * when meanwhile a third party has modified the state on the server, leading to a conflict.\"\n   */\n  PreconditionRequired = 428,\n\n  /**\n   * The user has sent too many requests in a given amount of time. Intended for use with rate-limiting schemes.\n   */\n  TooManyRequests = 429,\n\n  /**\n   * The server is unwilling to process the request because either an individual header field,\n   * or all the header fields collectively, are too large.\n   */\n  RequestHeaderFieldsTooLarge = 431,\n\n  /**\n   * A server operator has received a legal demand to deny access to a resource or to a set of resources\n   * that includes the requested resource. The code 451 was chosen as a reference to the novel Fahrenheit 451.\n   */\n  UnavailableForLegalReasons = 451,\n\n  // **********************************************************************************************************\n  // 500s - Server messed up\n  // **********************************************************************************************************\n\n  /**\n   * A generic error message, given when an unexpected condition was encountered and no more specific message is suitable.\n   */\n  InternalServerError = 500,\n\n  /**\n   * The server either does not recognize the request method, or it lacks the ability to fulfill the request.\n   * Usually this implies future availability (e.g., a new feature of a web-service API).\n   */\n  NotImplemented = 501,\n\n  /**\n   * The server was acting as a gateway or proxy and received an invalid response from the upstream server.\n   */\n  BadGateway = 502,\n\n  /**\n   * The server is currently unavailable (because it is overloaded or down for maintenance).\n   * Generally, this is a temporary state.\n   */\n  ServiceUnavailable = 503,\n\n  /**\n   * The server was acting as a gateway or proxy and did not receive a timely response from the upstream server.\n   */\n  GatewayTimeout = 504,\n\n  /**\n   * The server does not support the HTTP protocol version used in the request\n   */\n  HttpVersionNotSupported = 505,\n\n  /**\n   * Transparent content negotiation for the request results in a circular reference.\n   */\n  VariantAlsoNegotiates = 506,\n\n  /**\n   * The server is unable to store the representation needed to complete the request.\n   */\n  InsufficientStorage = 507,\n\n  /**\n   * The server detected an infinite loop while processing the request.\n   */\n  LoopDetected = 508,\n\n  /**\n   * Further extensions to the request are required for the server to fulfill it.\n   */\n  NotExtended = 510,\n\n  /**\n   * The client needs to authenticate to gain network access.\n   * Intended for use by intercepting proxies used to control access to the network (e.g., \"captive portals\" used\n   * to require agreement to Terms of Service before granting full Internet access via a Wi-Fi hotspot).\n   */\n  NetworkAuthenticationRequired = 511,\n}\n", "// FIXME: update to use a const object instead of a typescript enum\n// eslint-disable-next-line @bitwarden/platform/no-enums\nexport enum IntegrationType {\n  Integration = \"integration\",\n  SDK = \"sdk\",\n  SSO = \"sso\",\n  SCIM = \"scim\",\n  BWDC = \"bwdc\",\n  EVENT = \"event\",\n  DEVICE = \"device\",\n}\n", "// FIXME: update to use a const object instead of a typescript enum\n// eslint-disable-next-line @bitwarden/platform/no-enums\nexport enum NativeMessagingVersion {\n  One = 1, // Original implementation\n  Latest = One,\n}\n", "// FIXME: update to use a const object instead of a typescript enum\n// eslint-disable-next-line @bitwarden/platform/no-enums\nexport enum NotificationType {\n  SyncCipherUpdate = 0,\n  SyncCipherCreate = 1,\n  SyncLoginDelete = 2,\n  SyncFolderDelete = 3,\n  SyncCiphers = 4,\n\n  SyncVault = 5,\n  SyncOrgKeys = 6,\n  SyncFolderCreate = 7,\n  SyncFolderUpdate = 8,\n  SyncCipherDelete = 9,\n  SyncSettings = 10,\n\n  LogOut = 11,\n\n  SyncSendCreate = 12,\n  SyncSendUpdate = 13,\n  SyncSendDelete = 14,\n\n  AuthRequest = 15,\n  AuthRequestResponse = 16,\n\n  SyncOrganizations = 17,\n  SyncOrganizationStatusChanged = 18,\n  SyncOrganizationCollectionSettingChanged = 19,\n  Notification = 20,\n  NotificationStatus = 21,\n\n  PendingSecurityTasks = 22,\n}\n", "import { <PERSON>rowser<PERSON><PERSON> } from \"../platform/browser/browser-api\";\n\nexport class SafariApp {\n  static sendMessageToApp(command: string, data: any = null, resolveNow = false): Promise<any> {\n    if (!BrowserApi.isSafariApi) {\n      return Promise.resolve(null);\n    }\n    return new Promise((resolve) => {\n      const now = new Date();\n      const messageId =\n        now.getTime().toString() + \"_\" + Math.floor(Math.random() * Number.MAX_SAFE_INTEGER);\n      (browser as any).runtime.sendNativeMessage(\n        \"com.bitwarden.desktop\",\n        {\n          id: messageId,\n          command: command,\n          data: data,\n          responseData: null,\n        },\n        (response: any) => {\n          resolve(response);\n        },\n      );\n    });\n  }\n}\n", "import { ConsoleLogService } from \"@bitwarden/common/platform/services/console-log.service\";\n\nclass BrowserClipboardService {\n  private static consoleLogService: ConsoleLogService = new ConsoleLogService(false);\n\n  /**\n   * Copies the given text to the user's clipboard.\n   *\n   * @param globalContext - The global window context.\n   * @param text - The text to copy.\n   */\n  static async copy(globalContext: Window, text: string) {\n    if (!BrowserClipboardService.isClipboardApiSupported(globalContext, \"writeText\")) {\n      this.useLegacyCopyMethod(globalContext, text);\n      return;\n    }\n\n    try {\n      await globalContext.navigator.clipboard.writeText(text);\n    } catch (error) {\n      BrowserClipboardService.consoleLogService.debug(\n        `Error copying to clipboard using the clipboard API, attempting legacy method: ${error}`,\n      );\n\n      this.useLegacyCopyMethod(globalContext, text);\n    }\n  }\n\n  /**\n   * Reads the user's clipboard and returns the text.\n   *\n   * @param globalContext - The global window context.\n   */\n  static async read(globalContext: Window): Promise<string> {\n    if (!BrowserClipboardService.isClipboardApiSupported(globalContext, \"readText\")) {\n      return this.useLegacyReadMethod(globalContext);\n    }\n\n    try {\n      return await globalContext.navigator.clipboard.readText();\n    } catch (error) {\n      BrowserClipboardService.consoleLogService.debug(\n        `Error reading from clipboard using the clipboard API, attempting legacy method: ${error}`,\n      );\n\n      return this.useLegacyReadMethod(globalContext);\n    }\n  }\n\n  /**\n   * Copies the given text to the user's clipboard using the legacy `execCommand` method. This\n   * method is used as a fallback when the clipboard API is not supported or fails.\n   *\n   * @param globalContext - The global window context.\n   * @param text - The text to copy.\n   */\n  private static useLegacyCopyMethod(globalContext: Window, text: string) {\n    if (!BrowserClipboardService.isLegacyClipboardMethodSupported(globalContext, \"copy\")) {\n      BrowserClipboardService.consoleLogService.warning(\"Legacy copy method not supported\");\n      return;\n    }\n\n    const textareaElement = globalContext.document.createElement(\"textarea\");\n    textareaElement.textContent = !text ? \" \" : text;\n    textareaElement.style.position = \"fixed\";\n    globalContext.document.body.appendChild(textareaElement);\n    textareaElement.select();\n\n    try {\n      globalContext.document.execCommand(\"copy\");\n    } catch (error) {\n      BrowserClipboardService.consoleLogService.warning(`Error writing to clipboard: ${error}`);\n    } finally {\n      globalContext.document.body.removeChild(textareaElement);\n    }\n  }\n\n  /**\n   * Reads the user's clipboard using the legacy `execCommand` method. This method is used as a\n   * fallback when the clipboard API is not supported or fails.\n   *\n   * @param globalContext - The global window context.\n   */\n  private static useLegacyReadMethod(globalContext: Window): string {\n    if (!BrowserClipboardService.isLegacyClipboardMethodSupported(globalContext, \"paste\")) {\n      BrowserClipboardService.consoleLogService.warning(\"Legacy paste method not supported\");\n      return \"\";\n    }\n\n    const textareaElement = globalContext.document.createElement(\"textarea\");\n    textareaElement.style.position = \"fixed\";\n    globalContext.document.body.appendChild(textareaElement);\n    textareaElement.focus();\n\n    try {\n      return globalContext.document.execCommand(\"paste\") ? textareaElement.value : \"\";\n    } catch (error) {\n      BrowserClipboardService.consoleLogService.warning(`Error reading from clipboard: ${error}`);\n    } finally {\n      globalContext.document.body.removeChild(textareaElement);\n    }\n\n    return \"\";\n  }\n\n  /**\n   * Checks if the clipboard API is supported in the current environment.\n   *\n   * @param globalContext - The global window context.\n   * @param method - The clipboard API method to check for support.\n   */\n  private static isClipboardApiSupported(globalContext: Window, method: \"writeText\" | \"readText\") {\n    return \"clipboard\" in globalContext.navigator && method in globalContext.navigator.clipboard;\n  }\n\n  /**\n   * Checks if the legacy clipboard method is supported in the current environment.\n   *\n   * @param globalContext - The global window context.\n   * @param method - The legacy clipboard method to check for support.\n   */\n  private static isLegacyClipboardMethodSupported(globalContext: Window, method: \"copy\" | \"paste\") {\n    return (\n      \"queryCommandSupported\" in globalContext.document &&\n      globalContext.document.queryCommandSupported(method)\n    );\n  }\n}\n\nexport default BrowserClipboardService;\n", "// FIXME: Update this file to be type safe and remove this and next line\n// @ts-strict-ignore\nimport { ExtensionCommand } from \"@bitwarden/common/autofill/constants\";\nimport { ClientType, DeviceType } from \"@bitwarden/common/enums\";\nimport {\n  ClipboardOptions,\n  PlatformUtilsService,\n} from \"@bitwarden/common/platform/abstractions/platform-utils.service\";\n\nimport { SafariApp } from \"../../../browser/safariApp\";\nimport { BrowserApi } from \"../../browser/browser-api\";\nimport { OffscreenDocumentService } from \"../../offscreen-document/abstractions/offscreen-document\";\nimport BrowserClipboardService from \"../browser-clipboard.service\";\n\nexport abstract class BrowserPlatformUtilsService implements PlatformUtilsService {\n  private static deviceCache: DeviceType = null;\n\n  constructor(\n    private clipboardWriteCallback: (clipboardValue: string, clearMs: number) => void,\n    private globalContext: Window | ServiceWorkerGlobalScope,\n    private offscreenDocumentService: OffscreenDocumentService,\n  ) {}\n\n  static getDevice(globalContext: Window | ServiceWorkerGlobalScope): DeviceType {\n    if (this.deviceCache) {\n      return this.deviceCache;\n    }\n\n    // ORDERING MATTERS HERE\n    // Ordered from most specific to least specific. We try to discern the greatest detail\n    // for the type of extension the user is on by checking specific cases first and as we go down\n    // the list we hope to catch all by the most generic clients they could be on.\n    if (BrowserPlatformUtilsService.isFirefox()) {\n      this.deviceCache = DeviceType.FirefoxExtension;\n    } else if (BrowserPlatformUtilsService.isOpera(globalContext)) {\n      this.deviceCache = DeviceType.OperaExtension;\n    } else if (BrowserPlatformUtilsService.isEdge()) {\n      this.deviceCache = DeviceType.EdgeExtension;\n    } else if (BrowserPlatformUtilsService.isVivaldi()) {\n      this.deviceCache = DeviceType.VivaldiExtension;\n    } else if (BrowserPlatformUtilsService.isChrome(globalContext)) {\n      this.deviceCache = DeviceType.ChromeExtension;\n    } else if (BrowserPlatformUtilsService.isSafari(globalContext)) {\n      this.deviceCache = DeviceType.SafariExtension;\n    }\n\n    return this.deviceCache;\n  }\n\n  getDevice(): DeviceType {\n    return BrowserPlatformUtilsService.getDevice(this.globalContext);\n  }\n\n  getDeviceString(): string {\n    const device = DeviceType[this.getDevice()].toLowerCase();\n    return device.replace(\"extension\", \"\");\n  }\n\n  getClientType(): ClientType {\n    return ClientType.Browser;\n  }\n\n  private static isFirefox(): boolean {\n    return (\n      navigator.userAgent.indexOf(\" Firefox/\") !== -1 ||\n      navigator.userAgent.indexOf(\" Gecko/\") !== -1\n    );\n  }\n\n  isFirefox(): boolean {\n    return this.getDevice() === DeviceType.FirefoxExtension;\n  }\n\n  private static isChrome(globalContext: Window | ServiceWorkerGlobalScope): boolean {\n    return globalContext.chrome && navigator.userAgent.indexOf(\" Chrome/\") !== -1;\n  }\n\n  isChrome(): boolean {\n    return this.getDevice() === DeviceType.ChromeExtension;\n  }\n\n  private static isEdge(): boolean {\n    return navigator.userAgent.indexOf(\" Edg/\") !== -1;\n  }\n\n  isEdge(): boolean {\n    return this.getDevice() === DeviceType.EdgeExtension;\n  }\n\n  private static isOpera(globalContext: Window | ServiceWorkerGlobalScope): boolean {\n    return (\n      !!globalContext.opr?.addons ||\n      !!globalContext.opera ||\n      navigator.userAgent.indexOf(\" OPR/\") >= 0\n    );\n  }\n\n  isOpera(): boolean {\n    return this.getDevice() === DeviceType.OperaExtension;\n  }\n\n  private static isVivaldi(): boolean {\n    return navigator.userAgent.indexOf(\" Vivaldi/\") !== -1;\n  }\n\n  isVivaldi(): boolean {\n    return this.getDevice() === DeviceType.VivaldiExtension;\n  }\n\n  private static isSafari(globalContext: Window | ServiceWorkerGlobalScope): boolean {\n    // Opera masquerades as Safari, so make sure we're not there first\n    return (\n      !BrowserPlatformUtilsService.isOpera(globalContext) &&\n      navigator.userAgent.indexOf(\" Safari/\") !== -1\n    );\n  }\n\n  private static safariVersion(): string {\n    return navigator.userAgent.match(\"Version/([0-9.]*)\")?.[1];\n  }\n\n  isSafari(): boolean {\n    return this.getDevice() === DeviceType.SafariExtension;\n  }\n\n  /**\n   * Safari previous to version 16.1 had a bug which caused artifacts on hover in large extension popups.\n   * https://bugs.webkit.org/show_bug.cgi?id=218704\n   */\n  static shouldApplySafariHeightFix(globalContext: Window | ServiceWorkerGlobalScope): boolean {\n    if (BrowserPlatformUtilsService.getDevice(globalContext) !== DeviceType.SafariExtension) {\n      return false;\n    }\n\n    const version = BrowserPlatformUtilsService.safariVersion();\n    const parts = version?.split(\".\")?.map((v) => Number(v));\n    return parts?.[0] < 16 || (parts?.[0] === 16 && parts?.[1] === 0);\n  }\n\n  isIE(): boolean {\n    return false;\n  }\n\n  isMacAppStore(): boolean {\n    return false;\n  }\n\n  /**\n   * Identifies if the vault popup is currently open. This is done by sending a\n   * message to the popup and waiting for a response. If a response is received,\n   * the view is open.\n   */\n  async isViewOpen(): Promise<boolean> {\n    if (this.isSafari()) {\n      // Query views on safari since chrome.runtime.sendMessage does not timeout and will hang.\n      return BrowserApi.isPopupOpen();\n    }\n\n    return new Promise<boolean>((resolve, reject) => {\n      chrome.runtime.sendMessage({ command: \"checkVaultPopupHeartbeat\" }, (response) => {\n        if (chrome.runtime.lastError != null) {\n          // This error means that nothing was there to listen to the message,\n          // meaning the view is not open.\n          if (\n            chrome.runtime.lastError.message ===\n            \"Could not establish connection. Receiving end does not exist.\"\n          ) {\n            resolve(false);\n            return;\n          }\n\n          // All unhandled errors still reject\n          reject(chrome.runtime.lastError);\n          return;\n        }\n\n        resolve(Boolean(response));\n      });\n    });\n  }\n\n  lockTimeout(): number {\n    return null;\n  }\n\n  launchUri(uri: string, options?: any): void {\n    // FIXME: Verify that this floating promise is intentional. If it is, add an explanatory comment and ensure there is proper error handling.\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    BrowserApi.createNewTab(uri, options && options.extensionPage === true);\n  }\n\n  getApplicationVersion(): Promise<string> {\n    const manifest = chrome.runtime.getManifest();\n    return Promise.resolve(manifest.version_name ?? manifest.version);\n  }\n\n  getApplicationVersionNumber(): Promise<string> {\n    const manifest = chrome.runtime.getManifest();\n    return Promise.resolve(manifest.version.split(RegExp(\"[+|-]\"))[0].trim());\n  }\n\n  supportsWebAuthn(win: Window): boolean {\n    return typeof PublicKeyCredential !== \"undefined\";\n  }\n\n  supportsDuo(): boolean {\n    return true;\n  }\n\n  abstract showToast(\n    type: \"error\" | \"success\" | \"warning\" | \"info\",\n    title: string,\n    text: string | string[],\n    options?: any,\n  ): void;\n\n  isDev(): boolean {\n    return process.env.ENV === \"development\";\n  }\n\n  isSelfHost(): boolean {\n    return false;\n  }\n\n  /**\n   * Copies the passed text to the clipboard. For Safari, this will use\n   * the native messaging API to send the text to the Bitwarden app. If\n   * the extension is using manifest v3, the offscreen document API will\n   * be used to copy the text to the clipboard. Otherwise, the browser's\n   * clipboard API will be used.\n   *\n   * @param text - The text to copy to the clipboard.\n   * @param options - Options for the clipboard operation.\n   */\n  copyToClipboard(text: string, options?: ClipboardOptions): void {\n    const windowContext = options?.window || (this.globalContext as Window);\n    const clearing = Boolean(options?.clearing);\n    const clearMs: number = options?.clearMs || null;\n    const handleClipboardWriteCallback = () => {\n      if (!clearing && this.clipboardWriteCallback != null) {\n        this.clipboardWriteCallback(text, clearMs);\n      }\n    };\n\n    if (this.isSafari()) {\n      void SafariApp.sendMessageToApp(\"copyToClipboard\", text).then(handleClipboardWriteCallback);\n\n      return;\n    }\n\n    if (this.isChrome() && text === \"\") {\n      text = \"\\u0000\";\n    }\n\n    if (BrowserApi.isManifestVersion(3) && this.offscreenDocumentService.offscreenApiSupported()) {\n      void this.triggerOffscreenCopyToClipboard(text).then(handleClipboardWriteCallback);\n\n      return;\n    }\n\n    void BrowserClipboardService.copy(windowContext, text).then(handleClipboardWriteCallback);\n  }\n\n  /**\n   * Reads the text from the clipboard. For Safari, this will use the\n   * native messaging API to request the text from the Bitwarden app. If\n   * the extension is using manifest v3, the offscreen document API will\n   * be used to read the text from the clipboard. Otherwise, the browser's\n   * clipboard API will be used.\n   *\n   * @param options - Options for the clipboard operation.\n   */\n  async readFromClipboard(options?: ClipboardOptions): Promise<string> {\n    const windowContext = options?.window || (this.globalContext as Window);\n\n    if (this.isSafari()) {\n      return await SafariApp.sendMessageToApp(\"readFromClipboard\");\n    }\n\n    if (BrowserApi.isManifestVersion(3) && this.offscreenDocumentService.offscreenApiSupported()) {\n      return await this.triggerOffscreenReadFromClipboard();\n    }\n\n    return await BrowserClipboardService.read(windowContext);\n  }\n\n  supportsSecureStorage(): boolean {\n    return false;\n  }\n\n  async getAutofillKeyboardShortcut(): Promise<string> {\n    let autofillCommand: string;\n    // You can not change the command in Safari or obtain it programmatically\n    if (this.isSafari()) {\n      autofillCommand = \"Cmd+Shift+L\";\n    } else if (this.isFirefox()) {\n      autofillCommand = (await browser.commands.getAll()).find(\n        (c) => c.name === ExtensionCommand.AutofillLogin,\n      ).shortcut;\n      // Firefox is returning Ctrl instead of Cmd for the modifier key on macOS if\n      // the command is the default one set on installation.\n      if (\n        (await browser.runtime.getPlatformInfo()).os === \"mac\" &&\n        autofillCommand === \"Ctrl+Shift+L\"\n      ) {\n        autofillCommand = \"Cmd+Shift+L\";\n      }\n    } else {\n      await new Promise((resolve) =>\n        chrome.commands.getAll((c) =>\n          resolve(\n            (autofillCommand = c.find((c) => c.name === ExtensionCommand.AutofillLogin).shortcut),\n          ),\n        ),\n      );\n    }\n    return autofillCommand;\n  }\n\n  /**\n   * Triggers the offscreen document API to copy the text to the clipboard.\n   */\n  private async triggerOffscreenCopyToClipboard(text: string) {\n    await this.offscreenDocumentService.withDocument(\n      [chrome.offscreen.Reason.CLIPBOARD],\n      \"Write text to the clipboard.\",\n      async () => {\n        await BrowserApi.sendMessageWithResponse(\"offscreenCopyToClipboard\", { text });\n      },\n    );\n  }\n\n  /**\n   * Triggers the offscreen document API to read the text from the clipboard.\n   */\n  private async triggerOffscreenReadFromClipboard() {\n    const response = await this.offscreenDocumentService.withDocument(\n      [chrome.offscreen.Reason.CLIPBOARD],\n      \"Read text from the clipboard.\",\n      async () => {\n        return await BrowserApi.sendMessageWithResponse(\"offscreenReadFromClipboard\");\n      },\n    );\n    if (typeof response === \"string\") {\n      return response;\n    }\n\n    return \"\";\n  }\n}\n", "// FIXME: Update this file to be type safe and remove this and next line\n// @ts-strict-ignore\n/**\n * MIT License\n *\n * Copyright (c) <PERSON> <<EMAIL>> (https://fregante.com)\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n *\n * @see https://github.com/fregante/content-scripts-register-polyfill\n * @version 4.0.2\n */\nimport { ConsoleLogService } from \"@bitwarden/common/platform/services/console-log.service\";\n\nimport { BrowserApi } from \"./browser-api\";\n\nlet registerContentScripts: (\n  contentScriptOptions: browser.contentScripts.RegisteredContentScriptOptions,\n  callback?: (registeredContentScript: browser.contentScripts.RegisteredContentScript) => void,\n) => Promise<browser.contentScripts.RegisteredContentScript>;\nexport async function registerContentScriptsPolyfill(\n  contentScriptOptions: browser.contentScripts.RegisteredContentScriptOptions,\n  callback?: (registeredContentScript: browser.contentScripts.RegisteredContentScript) => void,\n) {\n  if (!registerContentScripts) {\n    registerContentScripts = buildRegisterContentScriptsPolyfill();\n  }\n\n  return registerContentScripts(contentScriptOptions, callback);\n}\n\nfunction buildRegisterContentScriptsPolyfill() {\n  const logService = new ConsoleLogService(false);\n  const chromeProxy = globalThis.chrome && NestedProxy<typeof globalThis.chrome>(globalThis.chrome);\n  const patternValidationRegex =\n    /^(https?|wss?|file|ftp|\\*):\\/\\/(\\*|\\*\\.[^*/]+|[^*/]+)\\/.*$|^file:\\/\\/\\/.*$|^resource:\\/\\/(\\*|\\*\\.[^*/]+|[^*/]+)\\/.*$|^about:/;\n  const isFirefox = globalThis.navigator?.userAgent.includes(\"Firefox/\");\n  const gotScripting = Boolean(globalThis.chrome?.scripting);\n  const gotNavigation = typeof chrome === \"object\" && \"webNavigation\" in chrome;\n\n  function NestedProxy<T extends object>(target: T): T {\n    return new Proxy(target, {\n      get(target, prop) {\n        if (!target[prop as keyof T]) {\n          return;\n        }\n\n        if (typeof target[prop as keyof T] !== \"function\") {\n          return NestedProxy(target[prop as keyof T] as object);\n        }\n\n        return (...arguments_: any[]) =>\n          new Promise((resolve, reject) => {\n            (target[prop as keyof T] as CallableFunction)(...arguments_, (result: any) => {\n              if (chrome.runtime.lastError) {\n                reject(new Error(chrome.runtime.lastError.message));\n              } else {\n                resolve(result);\n              }\n            });\n          });\n      },\n    });\n  }\n\n  function assertValidPattern(matchPattern: string) {\n    if (!isValidPattern(matchPattern)) {\n      throw new Error(\n        `${matchPattern} is an invalid pattern, it must match ${String(patternValidationRegex)}`,\n      );\n    }\n  }\n\n  function isValidPattern(matchPattern: string) {\n    return matchPattern === \"<all_urls>\" || patternValidationRegex.test(matchPattern);\n  }\n\n  function getRawPatternRegex(matchPattern: string) {\n    assertValidPattern(matchPattern);\n    let [, protocol, host = \"\", pathname] = matchPattern.split(/(^[^:]+:[/][/])([^/]+)?/);\n    protocol = protocol\n      .replace(\"*\", isFirefox ? \"(https?|wss?)\" : \"https?\")\n      .replaceAll(/[/]/g, \"[/]\");\n\n    if (host === \"*\") {\n      host = \"[^/]+\";\n    } else if (host) {\n      host = host\n        .replace(/^[*][.]/, \"([^/]+.)*\")\n        .replaceAll(/[.]/g, \"[.]\")\n        .replace(/[*]$/, \"[^.]+\");\n    }\n\n    pathname = pathname\n      .replaceAll(/[/]/g, \"[/]\")\n      .replaceAll(/[.]/g, \"[.]\")\n      .replaceAll(/[*]/g, \".*\");\n\n    return \"^\" + protocol + host + \"(\" + pathname + \")?$\";\n  }\n\n  function patternToRegex(...matchPatterns: string[]) {\n    if (matchPatterns.length === 0) {\n      return /$./;\n    }\n\n    if (matchPatterns.includes(\"<all_urls>\")) {\n      // <all_urls> regex\n      return /^(https?|file|ftp):[/]+/;\n    }\n\n    if (matchPatterns.includes(\"*://*/*\")) {\n      // all stars regex\n      return isFirefox ? /^(https?|wss?):[/][/][^/]+([/].*)?$/ : /^https?:[/][/][^/]+([/].*)?$/;\n    }\n\n    return new RegExp(matchPatterns.map((x) => getRawPatternRegex(x)).join(\"|\"));\n  }\n\n  function castAllFramesTarget(target: number | { tabId: number; frameId: number }) {\n    if (typeof target === \"object\") {\n      return { ...target, allFrames: false };\n    }\n\n    return {\n      tabId: target,\n      frameId: undefined,\n      allFrames: true,\n    };\n  }\n\n  function castArray(possibleArray: any | any[]) {\n    if (Array.isArray(possibleArray)) {\n      return possibleArray;\n    }\n\n    return [possibleArray];\n  }\n\n  function arrayOrUndefined(value?: number) {\n    return value === undefined ? undefined : [value];\n  }\n\n  async function insertCSS(\n    {\n      tabId,\n      frameId,\n      files,\n      allFrames,\n      matchAboutBlank,\n      runAt,\n    }: {\n      tabId: number;\n      frameId?: number;\n      files: browser.extensionTypes.ExtensionFileOrCode[];\n      allFrames: boolean;\n      matchAboutBlank: boolean;\n      runAt: browser.extensionTypes.RunAt;\n    },\n    { ignoreTargetErrors }: { ignoreTargetErrors?: boolean } = {},\n  ) {\n    const everyInsertion = Promise.all(\n      files.map(async (content) => {\n        if (typeof content === \"string\") {\n          content = { file: content };\n        }\n\n        if (gotScripting) {\n          return chrome.scripting.insertCSS({\n            target: {\n              tabId,\n              frameIds: arrayOrUndefined(frameId),\n              allFrames: frameId === undefined ? allFrames : undefined,\n            },\n            files: \"file\" in content ? [content.file] : undefined,\n            css: \"code\" in content ? content.code : undefined,\n          });\n        }\n\n        return chromeProxy.tabs.insertCSS(tabId, {\n          ...content,\n          matchAboutBlank,\n          allFrames,\n          frameId,\n          runAt: runAt ?? \"document_start\",\n        });\n      }),\n    );\n\n    if (ignoreTargetErrors) {\n      await catchTargetInjectionErrors(everyInsertion);\n    } else {\n      await everyInsertion;\n    }\n  }\n  function assertNoCode(files: browser.extensionTypes.ExtensionFileOrCode[]) {\n    if (files.some((content) => \"code\" in content)) {\n      throw new Error(\"chrome.scripting does not support injecting strings of `code`\");\n    }\n  }\n\n  async function executeScript(\n    {\n      tabId,\n      frameId,\n      files,\n      allFrames,\n      matchAboutBlank,\n      runAt,\n    }: {\n      tabId: number;\n      frameId?: number;\n      files: browser.extensionTypes.ExtensionFileOrCode[];\n      allFrames: boolean;\n      matchAboutBlank: boolean;\n      runAt: browser.extensionTypes.RunAt;\n    },\n    { ignoreTargetErrors }: { ignoreTargetErrors?: boolean } = {},\n  ) {\n    const normalizedFiles = files.map((file) => (typeof file === \"string\" ? { file } : file));\n\n    if (gotScripting) {\n      assertNoCode(normalizedFiles);\n      const injection = chrome.scripting.executeScript({\n        target: {\n          tabId,\n          frameIds: arrayOrUndefined(frameId),\n          allFrames: frameId === undefined ? allFrames : undefined,\n        },\n        files: normalizedFiles.map(({ file }: { file: string }) => file),\n      });\n\n      if (ignoreTargetErrors) {\n        await catchTargetInjectionErrors(injection);\n      } else {\n        await injection;\n      }\n\n      return;\n    }\n\n    const executions = [];\n    for (const content of normalizedFiles) {\n      if (\"code\" in content) {\n        await executions.at(-1);\n      }\n\n      executions.push(\n        chromeProxy.tabs.executeScript(tabId, {\n          ...content,\n          matchAboutBlank,\n          allFrames,\n          frameId,\n          runAt,\n        }),\n      );\n    }\n\n    if (ignoreTargetErrors) {\n      await catchTargetInjectionErrors(Promise.all(executions));\n    } else {\n      await Promise.all(executions);\n    }\n  }\n\n  async function injectContentScript(\n    where: { tabId: number; frameId: number },\n    scripts: {\n      css: browser.extensionTypes.ExtensionFileOrCode[];\n      js: browser.extensionTypes.ExtensionFileOrCode[];\n      matchAboutBlank: boolean;\n      runAt: browser.extensionTypes.RunAt;\n    },\n    options = {},\n  ) {\n    const targets = castArray(where);\n    await Promise.all(\n      targets.map(async (target) =>\n        injectContentScriptInSpecificTarget(castAllFramesTarget(target), scripts, options),\n      ),\n    );\n  }\n\n  async function injectContentScriptInSpecificTarget(\n    { frameId, tabId, allFrames }: { frameId?: number; tabId: number; allFrames: boolean },\n    scripts: {\n      css: browser.extensionTypes.ExtensionFileOrCode[];\n      js: browser.extensionTypes.ExtensionFileOrCode[];\n      matchAboutBlank: boolean;\n      runAt: browser.extensionTypes.RunAt;\n    },\n    options = {},\n  ) {\n    const injections = castArray(scripts).flatMap((script) => [\n      insertCSS(\n        {\n          tabId,\n          frameId,\n          allFrames,\n          files: script.css ?? [],\n          matchAboutBlank: script.matchAboutBlank ?? script.match_about_blank,\n          runAt: script.runAt ?? script.run_at,\n        },\n        options,\n      ),\n      executeScript(\n        {\n          tabId,\n          frameId,\n          allFrames,\n          files: script.js ?? [],\n          matchAboutBlank: script.matchAboutBlank ?? script.match_about_blank,\n          runAt: script.runAt ?? script.run_at,\n        },\n        options,\n      ),\n    ]);\n    await Promise.all(injections);\n  }\n\n  async function catchTargetInjectionErrors(promise: Promise<any>) {\n    try {\n      await promise;\n    } catch (error) {\n      const targetErrors =\n        /^No frame with id \\d+ in tab \\d+.$|^No tab with id: \\d+.$|^The tab was closed.$|^The frame was removed.$/;\n      if (!targetErrors.test(error?.message)) {\n        throw error;\n      }\n    }\n  }\n\n  async function isOriginPermitted(url: string) {\n    return chromeProxy.permissions.contains({\n      origins: [new URL(url).origin + \"/*\"],\n    });\n  }\n\n  return async (\n    contentScriptOptions: browser.contentScripts.RegisteredContentScriptOptions,\n    callback: CallableFunction,\n  ) => {\n    const {\n      js = [],\n      css = [],\n      matchAboutBlank,\n      matches = [],\n      excludeMatches,\n      runAt,\n    } = contentScriptOptions;\n    let { allFrames } = contentScriptOptions;\n\n    if (gotNavigation) {\n      allFrames = false;\n    } else if (allFrames) {\n      logService.warning(\n        \"`allFrames: true` requires the `webNavigation` permission to work correctly: https://github.com/fregante/content-scripts-register-polyfill#permissions\",\n      );\n    }\n\n    if (matches.length === 0) {\n      throw new Error(\n        \"Type error for parameter contentScriptOptions (Error processing matches: Array requires at least 1 items; you have 0) for contentScripts.register.\",\n      );\n    }\n\n    await Promise.all(\n      matches.map(async (pattern: string) => {\n        if (!(await chromeProxy.permissions.contains({ origins: [pattern] }))) {\n          throw new Error(`Permission denied to register a content script for ${pattern}`);\n        }\n      }),\n    );\n\n    const matchesRegex = patternToRegex(...matches);\n    const excludeMatchesRegex = patternToRegex(\n      ...(excludeMatches !== null && excludeMatches !== void 0 ? excludeMatches : []),\n    );\n    const inject = async (url: string, tabId: number, frameId = 0) => {\n      if (\n        !matchesRegex.test(url) ||\n        excludeMatchesRegex.test(url) ||\n        !(await isOriginPermitted(url))\n      ) {\n        return;\n      }\n\n      await injectContentScript(\n        { tabId, frameId },\n        { css, js, matchAboutBlank, runAt },\n        { ignoreTargetErrors: true },\n      );\n    };\n    const tabListener = async (\n      tabId: number,\n      { status }: chrome.tabs.TabChangeInfo,\n      { url }: chrome.tabs.Tab,\n    ) => {\n      if (status === \"loading\" && url) {\n        void inject(url, tabId);\n      }\n    };\n    const navListener = async ({\n      tabId,\n      frameId,\n      url,\n    }: chrome.webNavigation.WebNavigationTransitionCallbackDetails) => {\n      void inject(url, tabId, frameId);\n    };\n\n    if (gotNavigation) {\n      BrowserApi.addListener(chrome.webNavigation.onCommitted, navListener);\n    } else {\n      BrowserApi.addListener(chrome.tabs.onUpdated, tabListener);\n    }\n\n    const registeredContentScript = {\n      async unregister() {\n        if (gotNavigation) {\n          chrome.webNavigation.onCommitted.removeListener(navListener);\n        } else {\n          chrome.tabs.onUpdated.removeListener(tabListener);\n        }\n      },\n    };\n\n    if (typeof callback === \"function\") {\n      callback(registeredContentScript);\n    }\n\n    return registeredContentScript;\n  };\n}\n", "// FIXME: Update this file to be type safe and remove this and next line\n// @ts-strict-ignore\nimport { Observable } from \"rxjs\";\n\nimport { BrowserClientVendors } from \"@bitwarden/common/autofill/constants\";\nimport { BrowserClientVendor } from \"@bitwarden/common/autofill/types\";\nimport { DeviceType } from \"@bitwarden/common/enums\";\nimport { isBrowserSafariApi } from \"@bitwarden/platform\";\n\nimport { TabMessage } from \"../../types/tab-messages\";\nimport { BrowserPlatformUtilsService } from \"../services/platform-utils/browser-platform-utils.service\";\n\nimport { registerContentScriptsPolyfill } from \"./browser-api.register-content-scripts-polyfill\";\n\nexport class BrowserApi {\n  static isWebExtensionsApi: boolean = typeof browser !== \"undefined\";\n  static isSafariApi: boolean = isBrowserSafariApi();\n  static isChromeApi: boolean = !BrowserApi.isSafariApi && typeof chrome !== \"undefined\";\n  static isFirefoxOnAndroid: boolean =\n    navigator.userAgent.indexOf(\"Firefox/\") !== -1 && navigator.userAgent.indexOf(\"Android\") !== -1;\n\n  static get manifestVersion() {\n    return chrome.runtime.getManifest().manifest_version;\n  }\n\n  /**\n   * Determines if the extension manifest version is the given version.\n   *\n   * @param expectedVersion - The expected manifest version to check against.\n   */\n  static isManifestVersion(expectedVersion: 2 | 3) {\n    return BrowserApi.manifestVersion === expectedVersion;\n  }\n\n  /**\n   * Gets the current window or the window with the given id.\n   *\n   * @param windowId - The id of the window to get. If not provided, the current window is returned.\n   */\n  static async getWindow(windowId?: number): Promise<chrome.windows.Window> {\n    if (!windowId) {\n      return BrowserApi.getCurrentWindow();\n    }\n\n    return await BrowserApi.getWindowById(windowId);\n  }\n\n  /**\n   * Gets the currently active browser window.\n   */\n  static async getCurrentWindow(): Promise<chrome.windows.Window> {\n    return new Promise((resolve) => chrome.windows.getCurrent({ populate: true }, resolve));\n  }\n\n  /**\n   * Gets the window with the given id.\n   *\n   * @param windowId - The id of the window to get.\n   */\n  static async getWindowById(windowId: number): Promise<chrome.windows.Window> {\n    return new Promise((resolve) => chrome.windows.get(windowId, { populate: true }, resolve));\n  }\n\n  static async createWindow(options: chrome.windows.CreateData): Promise<chrome.windows.Window> {\n    return new Promise((resolve) => {\n      chrome.windows.create(options, async (newWindow) => {\n        if (!BrowserApi.isSafariApi) {\n          return resolve(newWindow);\n        }\n        // Safari doesn't close the default extension popup when a new window is created so we need to\n        // manually trigger the close by focusing the main window after the new window is created\n        const allWindows = await new Promise<chrome.windows.Window[]>((resolve) => {\n          chrome.windows.getAll({ windowTypes: [\"normal\"] }, (windows) => resolve(windows));\n        });\n\n        const mainWindow = allWindows.find((window) => window.id !== newWindow.id);\n\n        // No main window found, resolve the new window\n        if (mainWindow == null || !mainWindow.id) {\n          return resolve(newWindow);\n        }\n\n        // Focus the main window to close the extension popup\n        chrome.windows.update(mainWindow.id, { focused: true }, () => {\n          // Refocus the newly created window\n          chrome.windows.update(newWindow.id, { focused: true }, () => {\n            resolve(newWindow);\n          });\n        });\n      });\n    });\n  }\n\n  /**\n   * Removes the window with the given id.\n   *\n   * @param windowId - The id of the window to remove.\n   */\n  static async removeWindow(windowId: number): Promise<void> {\n    return new Promise((resolve) => chrome.windows.remove(windowId, () => resolve()));\n  }\n\n  /**\n   * Updates the properties of the window with the given id.\n   *\n   * @param windowId - The id of the window to update.\n   * @param options - The window properties to update.\n   */\n  static async updateWindowProperties(\n    windowId: number,\n    options: chrome.windows.UpdateInfo,\n  ): Promise<void> {\n    return new Promise((resolve) =>\n      chrome.windows.update(windowId, options, () => {\n        resolve();\n      }),\n    );\n  }\n\n  /**\n   * Focuses the window with the given id.\n   *\n   * @param windowId - The id of the window to focus.\n   */\n  static async focusWindow(windowId: number) {\n    await BrowserApi.updateWindowProperties(windowId, { focused: true });\n  }\n\n  static async getTabFromCurrentWindowId(): Promise<chrome.tabs.Tab> | null {\n    return await BrowserApi.tabsQueryFirstCurrentWindowForSafari({\n      active: true,\n      windowId: chrome.windows.WINDOW_ID_CURRENT,\n    });\n  }\n\n  static getBrowserClientVendor(clientWindow: Window): BrowserClientVendor {\n    const device = BrowserPlatformUtilsService.getDevice(clientWindow);\n\n    switch (device) {\n      case DeviceType.ChromeExtension:\n      case DeviceType.ChromeBrowser:\n        return BrowserClientVendors.Chrome;\n      case DeviceType.OperaExtension:\n      case DeviceType.OperaBrowser:\n        return BrowserClientVendors.Opera;\n      case DeviceType.EdgeExtension:\n      case DeviceType.EdgeBrowser:\n        return BrowserClientVendors.Edge;\n      case DeviceType.VivaldiExtension:\n      case DeviceType.VivaldiBrowser:\n        return BrowserClientVendors.Vivaldi;\n      default:\n        return BrowserClientVendors.Unknown;\n    }\n  }\n\n  /**\n   * Gets the tab with the given id.\n   *\n   * @param tabId - The id of the tab to get.\n   */\n  static async getTab(tabId: number): Promise<chrome.tabs.Tab> | null {\n    if (!tabId) {\n      return null;\n    }\n\n    if (BrowserApi.isManifestVersion(3)) {\n      return await chrome.tabs.get(tabId);\n    }\n\n    return new Promise((resolve) =>\n      chrome.tabs.get(tabId, (tab) => {\n        resolve(tab);\n      }),\n    );\n  }\n\n  static async getTabFromCurrentWindow(): Promise<chrome.tabs.Tab> | null {\n    return await BrowserApi.tabsQueryFirstCurrentWindowForSafari({\n      active: true,\n      currentWindow: true,\n    });\n  }\n\n  static async getActiveTabs(): Promise<chrome.tabs.Tab[]> {\n    return await BrowserApi.tabsQuery({\n      active: true,\n    });\n  }\n\n  /**\n   * Fetch the currently open browser tab\n   */\n  static async getCurrentTab(): Promise<chrome.tabs.Tab> | null {\n    if (BrowserApi.isManifestVersion(3)) {\n      return await chrome.tabs.getCurrent();\n    }\n\n    return new Promise((resolve) =>\n      chrome.tabs.getCurrent((tab) => {\n        resolve(tab);\n      }),\n    );\n  }\n\n  static async tabsQuery(options: chrome.tabs.QueryInfo): Promise<chrome.tabs.Tab[]> {\n    return new Promise((resolve) => {\n      chrome.tabs.query(options, (tabs) => {\n        resolve(tabs);\n      });\n    });\n  }\n\n  static async tabsQueryFirst(options: chrome.tabs.QueryInfo): Promise<chrome.tabs.Tab> | null {\n    const tabs = await BrowserApi.tabsQuery(options);\n    if (tabs.length > 0) {\n      return tabs[0];\n    }\n\n    return null;\n  }\n\n  /**\n   * Drop-in replacement for {@link BrowserApi.tabsQueryFirst}.\n   *\n   * Safari sometimes returns >1 tabs unexpectedly even when\n   * specificing a `windowId` or `currentWindow: true` query option.\n   *\n   * For all of these calls,\n   * ```\n   * await chrome.tabs.query({active: true, currentWindow: true})\n   * await chrome.tabs.query({active: true, windowId: chrome.windows.WINDOW_ID_CURRENT})\n   * await chrome.tabs.query({active: true, windowId: 10})\n   * ```\n   *\n   * Safari could return:\n   * ```\n   * [\n   *   {windowId: 2, pinned: true, title: \"Incorrect tab in another window\", …},\n   *   {windowId: 10, title: \"Correct tab in foreground\", …},\n   * ]\n   * ```\n   *\n   * This function captures the current window ID manually before running the query,\n   * then finds and returns the tab with the matching window ID.\n   *\n   * See the `SafariTabsQuery` tests in `browser-api.spec.ts`.\n   *\n   * This workaround can be removed when Safari fixes this bug.\n   */\n  static async tabsQueryFirstCurrentWindowForSafari(\n    options: chrome.tabs.QueryInfo,\n  ): Promise<chrome.tabs.Tab> | null {\n    if (!BrowserApi.isSafariApi) {\n      return await BrowserApi.tabsQueryFirst(options);\n    }\n\n    const currentWindowId = (await BrowserApi.getCurrentWindow()).id;\n    const tabs = await BrowserApi.tabsQuery(options);\n\n    if (tabs.length <= 1 || currentWindowId == null) {\n      return tabs[0];\n    }\n\n    return tabs.find((t) => t.windowId === currentWindowId) ?? tabs[0];\n  }\n\n  static tabSendMessageData(\n    tab: chrome.tabs.Tab,\n    command: string,\n    data: any = null,\n  ): Promise<void> {\n    const obj: any = {\n      command: command,\n    };\n\n    if (data != null) {\n      obj.data = data;\n    }\n\n    return BrowserApi.tabSendMessage(tab, obj);\n  }\n\n  static async tabSendMessage<T, TResponse = unknown>(\n    tab: chrome.tabs.Tab,\n    obj: T,\n    options: chrome.tabs.MessageSendOptions = null,\n    rejectOnError = false,\n  ): Promise<TResponse> {\n    if (!tab || !tab.id) {\n      return;\n    }\n\n    return new Promise<TResponse>((resolve, reject) => {\n      chrome.tabs.sendMessage(tab.id, obj, options, (response) => {\n        if (chrome.runtime.lastError && rejectOnError) {\n          // Some error happened\n          reject();\n        }\n        resolve(response);\n      });\n    });\n  }\n\n  static sendTabsMessage<T>(\n    tabId: number,\n    message: TabMessage,\n    options?: chrome.tabs.MessageSendOptions,\n    responseCallback?: (response: T) => void,\n  ) {\n    chrome.tabs.sendMessage<TabMessage, T>(tabId, message, options, responseCallback);\n  }\n\n  static async onWindowCreated(callback: (win: chrome.windows.Window) => any) {\n    // FIXME: Make sure that is does not cause a memory leak in Safari or use BrowserApi.AddListener\n    // and test that it doesn't break.\n    // eslint-disable-next-line no-restricted-syntax\n    return chrome.windows.onCreated.addListener(callback);\n  }\n\n  /**\n   * Gets the background page for the extension. This method is\n   * not valid within manifest v3 background service workers. As\n   * a result, it will return null when called from that context.\n   */\n  static getBackgroundPage(): any {\n    if (typeof chrome.extension.getBackgroundPage === \"undefined\") {\n      return null;\n    }\n\n    return chrome.extension.getBackgroundPage();\n  }\n\n  /**\n   * Accepts a window object and determines if it is\n   * associated with the background page of the extension.\n   *\n   * @param window - The window to check.\n   */\n  static isBackgroundPage(window: Window & typeof globalThis): boolean {\n    return typeof window !== \"undefined\" && window === BrowserApi.getBackgroundPage();\n  }\n\n  /**\n   * Gets the extension views that match the given properties. This method is not\n   * available within background service worker. As a result, it will return an\n   * empty array when called from that context.\n   *\n   * @param fetchProperties - The properties used to filter extension views.\n   */\n  static getExtensionViews(fetchProperties?: chrome.extension.FetchProperties): Window[] {\n    if (typeof chrome.extension.getViews === \"undefined\") {\n      return [];\n    }\n\n    return chrome.extension.getViews(fetchProperties);\n  }\n\n  /**\n   * Queries all extension views that are of type `popup`\n   * and returns whether any are currently open.\n   */\n  static async isPopupOpen(): Promise<boolean> {\n    return Promise.resolve(BrowserApi.getExtensionViews({ type: \"popup\" }).length > 0);\n  }\n\n  static createNewTab(url: string, active = true): Promise<chrome.tabs.Tab> {\n    return new Promise((resolve) =>\n      chrome.tabs.create({ url: url, active: active }, (tab) => resolve(tab)),\n    );\n  }\n\n  /**\n   * Gathers the details for a specified sub-frame of a tab.\n   *\n   * @param details - The details of the frame to get.\n   */\n  static async getFrameDetails(\n    details: chrome.webNavigation.GetFrameDetails,\n  ): Promise<chrome.webNavigation.GetFrameResultDetails> {\n    return new Promise((resolve) => chrome.webNavigation.getFrame(details, resolve));\n  }\n\n  /**\n   * Gets all frames associated with a tab.\n   *\n   * @param tabId - The id of the tab to get the frames for.\n   */\n  static async getAllFrameDetails(\n    tabId: chrome.tabs.Tab[\"id\"],\n  ): Promise<chrome.webNavigation.GetAllFrameResultDetails[]> {\n    return new Promise((resolve) => chrome.webNavigation.getAllFrames({ tabId }, resolve));\n  }\n\n  // Keep track of all the events registered in a Safari popup so we can remove\n  // them when the popup gets unloaded, otherwise we cause a memory leak\n  private static trackedChromeEventListeners: [\n    event: chrome.events.Event<(...args: unknown[]) => unknown>,\n    callback: (...args: unknown[]) => unknown,\n  ][] = [];\n\n  static messageListener(\n    name: string,\n    callback: (\n      message: any,\n      sender: chrome.runtime.MessageSender,\n      sendResponse: any,\n    ) => boolean | void,\n  ) {\n    BrowserApi.addListener(chrome.runtime.onMessage, callback);\n  }\n\n  static messageListener$() {\n    return new Observable<unknown>((subscriber) => {\n      const handler = (message: unknown) => {\n        subscriber.next(message);\n      };\n\n      BrowserApi.addListener(chrome.runtime.onMessage, handler);\n\n      return () => BrowserApi.removeListener(chrome.runtime.onMessage, handler);\n    });\n  }\n\n  static storageChangeListener(\n    callback: Parameters<typeof chrome.storage.onChanged.addListener>[0],\n  ) {\n    BrowserApi.addListener(chrome.storage.onChanged, callback);\n  }\n\n  /**\n   * Adds a callback to the given chrome event in a cross-browser platform manner.\n   *\n   * **Important:** All event listeners in the browser extension popup context must\n   * use this instead of the native APIs to handle unsubscribing from Safari properly.\n   *\n   * @param event - The event in which to add the listener to.\n   * @param callback - The callback you want registered onto the event.\n   */\n  static addListener<T extends (...args: readonly unknown[]) => unknown>(\n    event: chrome.events.Event<T>,\n    callback: T,\n  ) {\n    event.addListener(callback);\n\n    if (BrowserApi.isSafariApi && !BrowserApi.isBackgroundPage(self)) {\n      BrowserApi.trackedChromeEventListeners.push([event, callback]);\n      BrowserApi.setupUnloadListeners();\n    }\n  }\n\n  /**\n   * Removes a callback from the given chrome event in a cross-browser platform manner.\n   * @param event - The event in which to remove the listener from.\n   * @param callback - The callback you want removed from the event.\n   */\n  static removeListener<T extends (...args: readonly unknown[]) => unknown>(\n    event: chrome.events.Event<T>,\n    callback: T,\n  ) {\n    event.removeListener(callback);\n\n    if (BrowserApi.isSafariApi && !BrowserApi.isBackgroundPage(self)) {\n      const index = BrowserApi.trackedChromeEventListeners.findIndex(([_event, eventListener]) => {\n        return eventListener == callback;\n      });\n      if (index !== -1) {\n        BrowserApi.trackedChromeEventListeners.splice(index, 1);\n      }\n    }\n  }\n\n  // Setup the event to destroy all the listeners when the popup gets unloaded in Safari, otherwise we get a memory leak\n  private static setupUnloadListeners() {\n    // The MDN recommend using 'visibilitychange' but that event is fired any time the popup window is obscured as well\n    // 'pagehide' works just like 'unload' but is compatible with the back/forward cache, so we prefer using that one\n    self.addEventListener(\"pagehide\", () => {\n      for (const [event, callback] of BrowserApi.trackedChromeEventListeners) {\n        event.removeListener(callback);\n      }\n    });\n  }\n\n  static sendMessage(subscriber: string, arg: any = {}) {\n    const message = Object.assign({}, { command: subscriber }, arg);\n    return chrome.runtime.sendMessage(message);\n  }\n\n  static sendMessageWithResponse<TResponse>(subscriber: string, arg: any = {}) {\n    const message = Object.assign({}, { command: subscriber }, arg);\n    return new Promise<TResponse>((resolve) => chrome.runtime.sendMessage(message, resolve));\n  }\n\n  static async focusTab(tabId: number) {\n    // FIXME: Verify that this floating promise is intentional. If it is, add an explanatory comment and ensure there is proper error handling.\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    chrome.tabs.update(tabId, { active: true, highlighted: true });\n  }\n\n  static closePopup(win: Window) {\n    if (BrowserApi.isWebExtensionsApi && BrowserApi.isFirefoxOnAndroid) {\n      // Reactivating the active tab dismisses the popup tab. The promise final\n      // condition is only called if the popup wasn't already dismissed (future proofing).\n      // ref: https://bugzilla.mozilla.org/show_bug.cgi?id=1433604\n      // FIXME: Verify that this floating promise is intentional. If it is, add an explanatory comment and ensure there is proper error handling.\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      browser.tabs.update({ active: true }).finally(win.close);\n    } else {\n      win.close();\n    }\n  }\n\n  static gaFilter() {\n    return process.env.ENV !== \"production\";\n  }\n\n  static getUILanguage() {\n    return chrome.i18n.getUILanguage();\n  }\n\n  /**\n   * Handles reloading the extension using the underlying functionality exposed by the browser API.\n   */\n  static reloadExtension() {\n    // If we do `chrome.runtime.reload` on safari they will send an onInstalled reason of install\n    // and that prompts us to show a new tab, this apparently doesn't happen on sideloaded\n    // extensions and only shows itself production scenarios. See: https://bitwarden.atlassian.net/browse/PM-12298\n    if (this.isSafariApi) {\n      return self.location.reload();\n    }\n    return chrome.runtime.reload();\n  }\n\n  /**\n   * Reloads all open extension views, except the background page. Will also\n   * skip reloading the current window location if exemptCurrentHref is true.\n   *\n   * @param exemptCurrentHref - Whether to exempt the current window location from the reload.\n   */\n  static reloadOpenWindows(exemptCurrentHref = false) {\n    const views = BrowserApi.getExtensionViews();\n    if (!views.length) {\n      return;\n    }\n\n    const currentHref = self.location.href;\n    views\n      .filter((w) => w.location.href != null && !w.location.href.includes(\"background.html\"))\n      .filter((w) => !exemptCurrentHref || w.location.href !== currentHref)\n      .forEach((w) => w.location.reload());\n  }\n\n  static connectNative(application: string): browser.runtime.Port | chrome.runtime.Port {\n    if (BrowserApi.isWebExtensionsApi) {\n      return browser.runtime.connectNative(application);\n    } else if (BrowserApi.isChromeApi) {\n      return chrome.runtime.connectNative(application);\n    }\n  }\n\n  static requestPermission(permission: any) {\n    if (BrowserApi.isWebExtensionsApi) {\n      return browser.permissions.request(permission);\n    }\n    return new Promise((resolve) => {\n      chrome.permissions.request(permission, resolve);\n    });\n  }\n\n  /**\n   * Checks if the user has provided the given permissions to the extension.\n   *\n   * @param permissions - The permissions to check.\n   */\n  static async permissionsGranted(\n    permissions: chrome.runtime.ManifestPermissions[],\n  ): Promise<boolean> {\n    return new Promise((resolve) =>\n      chrome.permissions.contains({ permissions }, (result) => resolve(result)),\n    );\n  }\n\n  static getPlatformInfo(): Promise<browser.runtime.PlatformInfo | chrome.runtime.PlatformInfo> {\n    if (BrowserApi.isWebExtensionsApi) {\n      return browser.runtime.getPlatformInfo();\n    }\n    return new Promise((resolve) => {\n      chrome.runtime.getPlatformInfo(resolve);\n    });\n  }\n\n  /**\n   * Returns the supported BrowserAction API based on the manifest version.\n   */\n  static getBrowserAction() {\n    return BrowserApi.isManifestVersion(3) ? chrome.action : chrome.browserAction;\n  }\n\n  static getSidebarAction(\n    win: Window & typeof globalThis,\n  ): OperaSidebarAction | FirefoxSidebarAction | null {\n    const deviceType = BrowserPlatformUtilsService.getDevice(win);\n    if (deviceType === DeviceType.FirefoxExtension) {\n      return browser.sidebarAction;\n    }\n\n    if (deviceType === DeviceType.OperaExtension) {\n      return win.opr?.sidebarAction;\n    }\n\n    return null;\n  }\n\n  static captureVisibleTab(): Promise<string> {\n    return new Promise((resolve) => {\n      chrome.tabs.captureVisibleTab(null, { format: \"png\" }, resolve);\n    });\n  }\n\n  /**\n   * Extension API helper method used to execute a script in a tab.\n   *\n   * @see https://developer.chrome.com/docs/extensions/reference/tabs/#method-executeScript\n   * @param tabId - The id of the tab to execute the script in.\n   * @param details {@link \"InjectDetails\" https://developer.mozilla.org/en-US/docs/Mozilla/Add-ons/WebExtensions/API/extensionTypes/InjectDetails}\n   * @param scriptingApiDetails {@link \"ExecutionWorld\" https://developer.mozilla.org/en-US/docs/Mozilla/Add-ons/WebExtensions/API/scripting/ExecutionWorld}\n   */\n  static executeScriptInTab(\n    tabId: number,\n    details: chrome.tabs.InjectDetails,\n    scriptingApiDetails?: {\n      world: chrome.scripting.ExecutionWorld;\n    },\n  ): Promise<unknown> {\n    if (BrowserApi.isManifestVersion(3)) {\n      const target: chrome.scripting.InjectionTarget = {\n        tabId,\n      };\n\n      if (typeof details.frameId === \"number\") {\n        target.frameIds = [details.frameId];\n      }\n\n      if (!target.frameIds?.length && details.allFrames) {\n        target.allFrames = details.allFrames;\n      }\n\n      return chrome.scripting.executeScript({\n        target,\n        files: details.file ? [details.file] : null,\n        injectImmediately: details.runAt === \"document_start\",\n        world: scriptingApiDetails?.world || \"ISOLATED\",\n      });\n    }\n\n    return new Promise((resolve) => {\n      chrome.tabs.executeScript(tabId, details, (result) => {\n        resolve(result);\n      });\n    });\n  }\n\n  /**\n   * Identifies if the browser autofill settings are overridden by the extension.\n   */\n  static async browserAutofillSettingsOverridden(): Promise<boolean> {\n    if (!(await BrowserApi.permissionsGranted([\"privacy\"]))) {\n      return false;\n    }\n\n    const checkOverrideStatus = (details: chrome.types.ChromeSettingGetResult<boolean>) =>\n      details.levelOfControl === \"controlled_by_this_extension\" && !details.value;\n\n    const autofillAddressOverridden: boolean = await new Promise((resolve) =>\n      chrome.privacy.services.autofillAddressEnabled.get({}, (details) =>\n        resolve(checkOverrideStatus(details)),\n      ),\n    );\n\n    const autofillCreditCardOverridden: boolean = await new Promise((resolve) =>\n      chrome.privacy.services.autofillCreditCardEnabled.get({}, (details) =>\n        resolve(checkOverrideStatus(details)),\n      ),\n    );\n\n    const passwordSavingOverridden: boolean = await new Promise((resolve) =>\n      chrome.privacy.services.passwordSavingEnabled.get({}, (details) =>\n        resolve(checkOverrideStatus(details)),\n      ),\n    );\n\n    return autofillAddressOverridden && autofillCreditCardOverridden && passwordSavingOverridden;\n  }\n\n  /**\n   * Updates the browser autofill settings to the given value.\n   *\n   * @param value - Determines whether to enable or disable the autofill settings.\n   */\n  static async updateDefaultBrowserAutofillSettings(value: boolean) {\n    await chrome.privacy.services.autofillAddressEnabled.set({ value });\n    await chrome.privacy.services.autofillCreditCardEnabled.set({ value });\n    await chrome.privacy.services.passwordSavingEnabled.set({ value });\n  }\n\n  /**\n   * Handles registration of static content scripts within manifest v2.\n   *\n   * @param contentScriptOptions - Details of the registered content scripts\n   */\n  static async registerContentScriptsMv2(\n    contentScriptOptions: browser.contentScripts.RegisteredContentScriptOptions,\n  ): Promise<browser.contentScripts.RegisteredContentScript> {\n    if (typeof browser !== \"undefined\" && !!browser.contentScripts?.register) {\n      return await browser.contentScripts.register(contentScriptOptions);\n    }\n\n    return await registerContentScriptsPolyfill(contentScriptOptions);\n  }\n\n  /**\n   * Handles registration of static content scripts within manifest v3.\n   *\n   * @param scripts - Details of the registered content scripts\n   */\n  static async registerContentScriptsMv3(\n    scripts: chrome.scripting.RegisteredContentScript[],\n  ): Promise<void> {\n    await chrome.scripting.registerContentScripts(scripts);\n  }\n\n  /**\n   * Handles unregistering of static content scripts within manifest v3.\n   *\n   * @param filter - Optional filter to unregister content scripts. Passing an empty object will unregister all content scripts.\n   */\n  static async unregisterContentScriptsMv3(\n    filter?: chrome.scripting.ContentScriptFilter,\n  ): Promise<void> {\n    await chrome.scripting.unregisterContentScripts(filter);\n  }\n}\n", "export function isBrowserSafariApi(): boolean {\n  return (\n    navigator.userAgent.indexOf(\" Safari/\") !== -1 &&\n    navigator.userAgent.indexOf(\" Chrome/\") === -1 &&\n    navigator.userAgent.indexOf(\" Chromium/\") === -1\n  );\n}\n", "// FIXME: Update this file to be type safe and remove this and next line\n// @ts-strict-ignore\nimport { ConsoleLogService } from \"@bitwarden/common/platform/services/console-log.service\";\n\nimport { BrowserApi } from \"../browser/browser-api\";\nimport BrowserClipboardService from \"../services/browser-clipboard.service\";\n\nimport {\n  OffscreenDocumentExtensionMessage,\n  OffscreenDocumentExtensionMessageHandlers,\n  OffscreenDocument as OffscreenDocumentInterface,\n} from \"./abstractions/offscreen-document\";\n\nclass OffscreenDocument implements OffscreenDocumentInterface {\n  private consoleLogService: ConsoleLogService = new ConsoleLogService(false);\n  private readonly extensionMessageHandlers: OffscreenDocumentExtensionMessageHandlers = {\n    offscreenCopyToClipboard: ({ message }) => this.handleOffscreenCopyToClipboard(message),\n    offscreenReadFromClipboard: () => this.handleOffscreenReadFromClipboard(),\n    localStorageGet: ({ message }) => this.handleLocalStorageGet(message.key),\n    localStorageSave: ({ message }) => this.handleLocalStorageSave(message.key, message.value),\n    localStorageRemove: ({ message }) => this.handleLocalStorageRemove(message.key),\n  };\n\n  /**\n   * Initializes the offscreen document extension.\n   */\n  init() {\n    this.setupExtensionMessageListener();\n  }\n\n  /**\n   * Copies the given text to the user's clipboard.\n   *\n   * @param message - The extension message containing the text to copy\n   */\n  private async handleOffscreenCopyToClipboard(message: OffscreenDocumentExtensionMessage) {\n    await BrowserClipboardService.copy(self, message.text);\n  }\n\n  /**\n   * Reads the user's clipboard and returns the text.\n   */\n  private async handleOffscreenReadFromClipboard() {\n    return await BrowserClipboardService.read(self);\n  }\n\n  private handleLocalStorageGet(key: string) {\n    return self.localStorage.getItem(key);\n  }\n\n  private handleLocalStorageSave(key: string, value: string) {\n    self.localStorage.setItem(key, value);\n  }\n\n  private handleLocalStorageRemove(key: string) {\n    self.localStorage.removeItem(key);\n  }\n\n  /**\n   * Sets up the listener for extension messages.\n   */\n  private setupExtensionMessageListener() {\n    BrowserApi.messageListener(\"offscreen-document\", this.handleExtensionMessage);\n  }\n\n  /**\n   * Handles extension messages sent to the extension background.\n   *\n   * @param message - The message received from the extension\n   * @param sender - The sender of the message\n   * @param sendResponse - The response to send back to the sender\n   */\n  private handleExtensionMessage = (\n    message: OffscreenDocumentExtensionMessage,\n    sender: chrome.runtime.MessageSender,\n    sendResponse: (response?: any) => void,\n  ) => {\n    const handler: CallableFunction | undefined = this.extensionMessageHandlers[message?.command];\n    if (!handler) {\n      return;\n    }\n\n    const messageResponse = handler({ message, sender });\n    if (!messageResponse) {\n      return;\n    }\n\n    Promise.resolve(messageResponse)\n      .then((response) => sendResponse(response))\n      .catch((error) =>\n        this.consoleLogService.error(\"Error resolving extension message response\", error),\n      );\n    return true;\n  };\n}\n\n(() => {\n  const offscreenDocument = new OffscreenDocument();\n  offscreenDocument.init();\n})();\n"], "names": ["LogLevelType", "ConsoleLogService", "constructor", "isDev", "filter", "timersMap", "Map", "debug", "message", "optionalParams", "this", "write", "Debug", "info", "Info", "warning", "Warning", "error", "Error", "level", "console", "log", "warn", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "__extends", "TypeError", "String", "__", "create", "__values", "o", "s", "Symbol", "iterator", "m", "i", "length", "next", "value", "done", "__read", "n", "r", "e", "ar", "push", "__spread<PERSON><PERSON>y", "to", "from", "pack", "arguments", "l", "slice", "concat", "SuppressedError", "isFunction", "ctorFunc", "UnsubscriptionError", "_super", "errors", "map", "err", "toString", "join", "name", "createImpl", "instance", "stack", "arr<PERSON><PERSON><PERSON>", "arr", "item", "index", "indexOf", "splice", "Subscription", "initialTeardown", "closed", "_parentage", "_finalizers", "empty", "unsubscribe", "isArray", "_parentage_1", "_parentage_1_1", "remove", "initialFinalizer", "_finalizers_1", "_finalizers_1_1", "finalizer", "execFinalizer", "add", "teardown", "_hasParent", "_addParent", "_a", "parent", "includes", "_removeParent", "EMPTY", "isSubscription", "config", "onUnhandledError", "onStoppedNotification", "Promise", "undefined", "useDeprecatedSynchronousErrorHandling", "useDeprecatedNextContext", "timeout<PERSON>rovider", "setTimeout", "handler", "timeout", "args", "_i", "delegate", "apply", "clearTimeout", "handle", "noop", "COMPLETE_NOTIFICATION", "createNotification", "kind", "context", "Subscriber", "destination", "_this", "isStopped", "EMPTY_OBSERVER", "complete", "SafeSubscriber", "handleStoppedNotification", "nextNotification", "_next", "_error", "_complete", "_bind", "Function", "bind", "fn", "thisArg", "ConsumerObserver", "partialObserver", "handleUnhandledError", "observerOrNext", "context_1", "errorThrown", "reportUnhandledError", "notification", "subscriber", "observable", "identity", "x", "pipeFromArray", "fns", "input", "reduce", "prev", "Observable", "subscribe", "_subscribe", "lift", "operator", "source", "isObserver", "cb", "isRoot", "errorContext", "_trySubscribe", "sink", "for<PERSON>ach", "promiseCtor", "getPromiseCtor", "resolve", "reject", "Symbol_observable", "pipe", "operations", "to<PERSON>romise", "ExpiryDateDelimitersPattern", "replace", "BrowserClientVendors", "RegExp", "ExtensionCommand", "ClientType", "DeviceType", "Android", "iOS", "AndroidAmazon", "ChromeExtension", "FirefoxExtension", "OperaExtension", "EdgeExtension", "VivaldiExtension", "SafariExtension", "ChromeBrowser", "FirefoxBrowser", "<PERSON><PERSON><PERSON><PERSON>", "Edge<PERSON><PERSON><PERSON>", "IEBrowser", "SafariBrowser", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "WindowsDesktop", "MacOsDesktop", "LinuxDesktop", "UWP", "WindowsCLI", "MacOsCLI", "LinuxCLI", "SDK", "Server", "EventSystemUser", "EventType", "HttpStatusCode", "IntegrationType", "NativeMessagingVersion", "NotificationType", "SafariApp", "sendMessageToApp", "command", "data", "resolveNow", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isSafariApi", "messageId", "Date", "getTime", "Math", "floor", "random", "Number", "MAX_SAFE_INTEGER", "browser", "runtime", "sendNativeMessage", "id", "responseData", "response", "BrowserClipboardService", "copy", "globalContext", "text", "isClipboardApiSupported", "navigator", "clipboard", "writeText", "consoleLogService", "useLegacyCopyMethod", "read", "useLegacyReadMethod", "readText", "isLegacyClipboardMethodSupported", "textareaElement", "document", "createElement", "textContent", "style", "position", "body", "append<PERSON><PERSON><PERSON>", "select", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "focus", "method", "queryCommandSupported", "BrowserPlatformUtilsService", "clipboardWriteCallback", "offscreenDocumentService", "getDevice", "deviceCache", "isFirefox", "isOpera", "isEdge", "isVivaldi", "isChrome", "<PERSON><PERSON><PERSON><PERSON>", "getDeviceString", "toLowerCase", "getClientType", "Browser", "userAgent", "chrome", "opr", "addons", "opera", "safariVersion", "match", "shouldApplySafariHeightFix", "version", "parts", "split", "v", "isIE", "isMacAppStore", "isViewOpen", "isPopupOpen", "sendMessage", "lastError", "Boolean", "lockTimeout", "launchUri", "uri", "options", "createNewTab", "extensionPage", "getApplicationVersion", "manifest", "getManifest", "version_name", "getApplicationVersionNumber", "trim", "supportsWebAuthn", "win", "PublicKeyCredential", "supportsDuo", "isSelfHost", "copyToClipboard", "windowContext", "window", "clearing", "clearMs", "handleClipboardWriteCallback", "then", "isManifestVersion", "offscreenApiSupported", "triggerOffscreenCopyToClipboard", "readFromClipboard", "triggerOffscreenReadFromClipboard", "supportsSecureStorage", "getAutofillKeyboardShortcut", "autofillCommand", "commands", "getAll", "find", "c", "shortcut", "getPlatformInfo", "os", "withDocument", "offscreen", "Reason", "CLIPBOARD", "sendMessageWithResponse", "registerContentScripts", "registerContentScriptsPolyfill", "contentScriptOptions", "callback", "logService", "chromeProxy", "globalThis", "NestedProxy", "patternValidationRegex", "gotScripting", "scripting", "gotNavigation", "target", "Proxy", "get", "prop", "arguments_", "result", "assertValidPattern", "matchPattern", "isValidPattern", "test", "getRawPatternRegex", "protocol", "host", "pathname", "replaceAll", "patternToRegex", "matchPatterns", "castAll<PERSON><PERSON><PERSON><PERSON>arget", "allFrames", "tabId", "frameId", "<PERSON><PERSON><PERSON><PERSON>", "possible<PERSON><PERSON>y", "arrayOrUndefined", "insertCSS", "files", "matchAboutBlank", "runAt", "ignoreTargetErrors", "everyInsertion", "all", "content", "file", "frameIds", "css", "code", "tabs", "catchTargetInjectionErrors", "assertNoCode", "some", "executeScript", "normalizedFiles", "injection", "executions", "at", "injectContentScript", "where", "scripts", "targets", "injectContentScriptInSpecificTarget", "injections", "flatMap", "script", "match_about_blank", "run_at", "js", "promise", "isOriginPermitted", "url", "permissions", "contains", "origins", "URL", "origin", "matches", "excludeMatches", "pattern", "matchesRegex", "excludeMatchesRegex", "inject", "tabListener", "status", "navListener", "addListener", "webNavigation", "onCommitted", "onUpdated", "registeredContentScript", "unregister", "removeListener", "buildRegisterContentScriptsPolyfill", "manifestVersion", "manifest_version", "expectedVersion", "getWindow", "windowId", "getWindowById", "getCurrentWindow", "windows", "get<PERSON>urrent", "populate", "createWindow", "newWindow", "allWindows", "windowTypes", "mainWindow", "update", "focused", "<PERSON><PERSON><PERSON>ow", "updateWindowProperties", "focusWindow", "getTabFromCurrentWindowId", "tabsQueryFirstCurrentWindowForSafari", "active", "WINDOW_ID_CURRENT", "getBrowserClientVendor", "clientWindow", "getTab", "tab", "getTabFromCurrentWindow", "currentWindow", "getActiveTabs", "tabsQuery", "getCurrentTab", "query", "tabsQueryFirst", "currentWindowId", "t", "tabSendMessageData", "obj", "tabSendMessage", "rejectOnError", "sendTabsMessage", "responseCallback", "onWindowCreated", "onCreated", "getBackgroundPage", "extension", "isBackgroundPage", "getExtensionViews", "fetchProperties", "getViews", "type", "getFrameDetails", "details", "getFrame", "getAllFrameDetails", "getAllFrames", "messageListener", "onMessage", "messageListener$", "storageChangeListener", "storage", "onChanged", "event", "self", "trackedChromeEventListeners", "setupUnloadListeners", "findIndex", "_event", "eventListener", "addEventListener", "arg", "assign", "focusTab", "highlighted", "closePopup", "isWebExtensionsApi", "isFirefoxOnAndroid", "finally", "close", "gaFilter", "getUILanguage", "i18n", "reloadExtension", "location", "reload", "reloadOpenWindows", "exemptCurrentHref", "views", "currentHref", "href", "w", "connectNative", "application", "isChromeApi", "requestPermission", "permission", "request", "permissionsGranted", "getBrowserAction", "action", "browserAction", "getSidebarAction", "deviceType", "sidebarAction", "captureVisibleTab", "format", "executeScriptInTab", "scriptingApiDetails", "injectImmediately", "world", "browserAutofillSettingsOverridden", "checkOverrideStatus", "levelOfControl", "autofillAddressOverridden", "privacy", "services", "autofillAddressEnabled", "autofillCreditCardOverridden", "autofillCreditCardEnabled", "passwordSavingOverridden", "passwordSavingEnabled", "updateDefaultBrowserAutofillSettings", "set", "registerContentScriptsMv2", "contentScripts", "register", "registerContentScriptsMv3", "unregisterContentScriptsMv3", "unregisterContentScripts", "OffscreenDocument", "extensionMessageHandlers", "offscreenCopyToClipboard", "handleOffscreenCopyToClipboard", "offscreenReadFromClipboard", "handleOffscreenReadFromClipboard", "localStorageGet", "handleLocalStorageGet", "key", "localStorageSave", "handleLocalStorageSave", "localStorageRemove", "handleLocalStorageRemove", "handleExtensionMessage", "sender", "sendResponse", "messageResponse", "catch", "init", "setupExtensionMessageListener", "localStorage", "getItem", "setItem", "removeItem"], "sourceRoot": ""}