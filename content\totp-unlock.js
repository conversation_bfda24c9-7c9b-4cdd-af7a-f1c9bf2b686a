// TOTP Unlock Script for Bitwarden Chrome Extension
// This script bypasses premium restrictions for TOTP functionality

(function() {
    'use strict';
    
    console.log('TOTP Unlock Script loaded');
    
    // Override chrome.runtime.sendMessage to intercept getUserPremiumStatus calls
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
        const originalSendMessage = chrome.runtime.sendMessage;
        
        chrome.runtime.sendMessage = function(message, options, callback) {
            // Handle different call signatures
            if (typeof options === 'function') {
                callback = options;
                options = undefined;
            }
            
            // Intercept getUserPremiumStatus calls
            if (message && message.command === 'getUserPremiumStatus') {
                console.log('Intercepted getUserPremiumStatus call, returning premium status');
                if (callback) {
                    setTimeout(() => {
                        callback({ result: true });
                    }, 0);
                }
                return;
            }
            
            // Call original function for other messages
            return originalSendMessage.call(this, message, options, callback);
        };
    }
    
    // Override sendExtensionMessage function if it exists
    if (typeof sendExtensionMessage !== 'undefined') {
        const originalSendExtensionMessage = sendExtensionMessage;
        
        window.sendExtensionMessage = function(command, data) {
            if (command === 'getUserPremiumStatus') {
                console.log('Intercepted sendExtensionMessage getUserPremiumStatus call');
                return Promise.resolve({ result: true });
            }
            return originalSendExtensionMessage.call(this, command, data);
        };
    }
    
    // Function to patch premium status checks in existing objects
    function patchPremiumChecks() {
        // Find and patch any objects with premiumEnabled property
        const allElements = document.querySelectorAll('*');
        
        allElements.forEach(element => {
            if (element._premiumEnabled !== undefined) {
                element._premiumEnabled = true;
            }
            
            // Check for any attached objects or data
            for (let prop in element) {
                if (prop.includes('premium') || prop.includes('Premium')) {
                    try {
                        if (typeof element[prop] === 'boolean') {
                            element[prop] = true;
                        } else if (typeof element[prop] === 'object' && element[prop] !== null) {
                            if (element[prop].premiumEnabled !== undefined) {
                                element[prop].premiumEnabled = true;
                            }
                        }
                    } catch (e) {
                        // Ignore errors for read-only properties
                    }
                }
            }
        });
    }
    
    // Function to patch TOTP display
    function patchTotpDisplay() {
        // Find all elements with masked TOTP codes
        const maskedElements = document.querySelectorAll('.masked-totp, [data-testid="totp-code"]');
        
        maskedElements.forEach(element => {
            if (element.textContent === '●●●●●●') {
                // Try to find the actual TOTP code from nearby elements or data attributes
                const parent = element.closest('.cipher-details');
                if (parent) {
                    // Look for any data attributes that might contain the TOTP code
                    const totpData = parent.querySelector('[data-totp], [data-totp-code]');
                    if (totpData) {
                        const totpCode = totpData.dataset.totp || totpData.dataset.totpCode;
                        if (totpCode && totpCode !== '●●●●●●') {
                            element.textContent = totpCode;
                            element.style.cursor = 'pointer';
                            
                            // Add click to copy functionality
                            element.addEventListener('click', function(e) {
                                e.stopPropagation();
                                navigator.clipboard.writeText(totpCode.replace(/\s/g, '')).then(() => {
                                    const originalText = element.textContent;
                                    element.textContent = 'Copied!';
                                    setTimeout(() => {
                                        element.textContent = originalText;
                                    }, 1000);
                                });
                            });
                        }
                    }
                }
            }
        });
    }
    
    // Function to patch premium status in window objects
    function patchWindowObjects() {
        // Patch any global objects that might contain premium status
        if (typeof window !== 'undefined') {
            // Look for common Bitwarden objects
            const possibleObjects = ['bitwarden', 'BW', 'vault', 'autofill'];
            
            possibleObjects.forEach(objName => {
                if (window[objName]) {
                    try {
                        if (window[objName].premiumEnabled !== undefined) {
                            window[objName].premiumEnabled = true;
                        }
                        if (window[objName].premium !== undefined) {
                            window[objName].premium = true;
                        }
                    } catch (e) {
                        // Ignore errors
                    }
                }
            });
        }
    }
    
    // Run patches immediately
    patchPremiumChecks();
    patchTotpDisplay();
    patchWindowObjects();
    
    // Run patches periodically to catch dynamically created content
    setInterval(() => {
        patchPremiumChecks();
        patchTotpDisplay();
        patchWindowObjects();
    }, 1000);
    
    // Run patches when DOM changes
    const observer = new MutationObserver((mutations) => {
        let shouldPatch = false;
        
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                shouldPatch = true;
            }
        });
        
        if (shouldPatch) {
            setTimeout(() => {
                patchPremiumChecks();
                patchTotpDisplay();
            }, 100);
        }
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    
    console.log('TOTP Unlock Script initialized');
})();
