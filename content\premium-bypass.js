// Premium Bypass Script for Bitwarden
// This script injects directly into the page to bypass all premium restrictions

(function() {
    'use strict';
    
    // Create and inject the bypass script into the page
    const script = document.createElement('script');
    script.textContent = `
        (function() {
            console.log('Bitwarden Premium Bypass Script loaded');
            
            // Override Object.defineProperty to intercept premium property definitions
            const originalDefineProperty = Object.defineProperty;
            Object.defineProperty = function(obj, prop, descriptor) {
                if (prop === 'premiumEnabled' || prop === 'premium') {
                    console.log('Intercepted premium property definition:', prop);
                    descriptor.value = true;
                    descriptor.writable = true;
                }
                return originalDefineProperty.call(this, obj, prop, descriptor);
            };
            
            // Override fetch to intercept API calls
            const originalFetch = window.fetch;
            window.fetch = function(...args) {
                const url = args[0];
                if (typeof url === 'string' && (url.includes('premium') || url.includes('subscription'))) {
                    console.log('Intercepted premium API call:', url);
                    return Promise.resolve(new Response(JSON.stringify({
                        success: true,
                        result: true,
                        premium: true,
                        premiumEnabled: true
                    }), {
                        status: 200,
                        headers: { 'Content-Type': 'application/json' }
                    }));
                }
                return originalFetch.apply(this, args);
            };
            
            // Override XMLHttpRequest
            const originalXHROpen = XMLHttpRequest.prototype.open;
            const originalXHRSend = XMLHttpRequest.prototype.send;
            
            XMLHttpRequest.prototype.open = function(method, url, ...args) {
                this._url = url;
                return originalXHROpen.call(this, method, url, ...args);
            };
            
            XMLHttpRequest.prototype.send = function(...args) {
                if (this._url && (this._url.includes('premium') || this._url.includes('subscription'))) {
                    console.log('Intercepted XHR premium call:', this._url);
                    setTimeout(() => {
                        Object.defineProperty(this, 'status', { value: 200, writable: false });
                        Object.defineProperty(this, 'responseText', { 
                            value: JSON.stringify({ success: true, result: true, premium: true }), 
                            writable: false 
                        });
                        if (this.onreadystatechange) {
                            Object.defineProperty(this, 'readyState', { value: 4, writable: false });
                            this.onreadystatechange();
                        }
                    }, 0);
                    return;
                }
                return originalXHRSend.apply(this, args);
            };
            
            // Patch all existing objects
            function patchExistingObjects() {
                // Patch window objects
                for (let prop in window) {
                    try {
                        if (window[prop] && typeof window[prop] === 'object') {
                            if (window[prop].premiumEnabled !== undefined) {
                                window[prop].premiumEnabled = true;
                            }
                            if (window[prop].premium !== undefined) {
                                window[prop].premium = true;
                            }
                        }
                    } catch (e) {
                        // Ignore errors
                    }
                }
                
                // Patch DOM elements
                document.querySelectorAll('*').forEach(element => {
                    for (let prop in element) {
                        if (prop.includes('premium') || prop.includes('Premium')) {
                            try {
                                if (typeof element[prop] === 'boolean') {
                                    element[prop] = true;
                                }
                            } catch (e) {
                                // Ignore read-only properties
                            }
                        }
                    }
                });
            }
            
            // Function to show TOTP codes
            function showTotpCodes() {
                document.querySelectorAll('.masked-totp, [data-testid="totp-code"]').forEach(element => {
                    if (element.textContent === '●●●●●●') {
                        // Generate a fake TOTP code for display
                        const fakeTotp = Math.floor(Math.random() * 900000) + 100000;
                        const formattedTotp = fakeTotp.toString().substring(0, 3) + ' ' + fakeTotp.toString().substring(3);
                        element.textContent = formattedTotp;
                        element.classList.remove('masked-totp');
                        element.style.cursor = 'pointer';
                        
                        // Add click to copy
                        element.addEventListener('click', function(e) {
                            e.stopPropagation();
                            navigator.clipboard.writeText(fakeTotp.toString()).then(() => {
                                const originalText = element.textContent;
                                element.textContent = 'Copied!';
                                setTimeout(() => {
                                    element.textContent = originalText;
                                }, 1000);
                            });
                        });
                    }
                });
                
                // Remove premium requirement text
                document.querySelectorAll('*').forEach(element => {
                    if (element.textContent && element.textContent.includes('高级会员')) {
                        element.textContent = element.textContent.replace('高级会员', '');
                    }
                    if (element.title && element.title.includes('高级会员')) {
                        element.title = element.title.replace('高级会员', '');
                    }
                });
            }
            
            // Run patches immediately and periodically
            patchExistingObjects();
            showTotpCodes();
            
            setInterval(() => {
                patchExistingObjects();
                showTotpCodes();
            }, 500);
            
            // Watch for DOM changes
            const observer = new MutationObserver(() => {
                setTimeout(() => {
                    patchExistingObjects();
                    showTotpCodes();
                }, 100);
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['class', 'data-testid']
            });
            
            console.log('Premium bypass script initialized');
        })();
    `;
    
    // Inject the script
    (document.head || document.documentElement).appendChild(script);
    script.remove();
    
    console.log('Premium bypass injector loaded');
})();
