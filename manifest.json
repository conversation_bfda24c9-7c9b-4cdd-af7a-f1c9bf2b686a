{"manifest_version": 3, "minimum_chrome_version": "102.0", "name": "__MSG_extName__", "short_name": "Bitwarden", "version": "2025.6.0", "description": "__MSG_extDesc__", "default_locale": "en", "author": "Bitwarden Inc.", "homepage_url": "https://bitwarden.com", "icons": {"16": "images/icon16.png", "32": "images/icon32.png", "48": "images/icon48.png", "96": "images/icon96.png", "128": "images/icon128.png"}, "content_scripts": [{"all_frames": false, "js": ["content/content-message-handler.js"], "matches": ["*://*/*", "file:///*"], "exclude_matches": ["*://*/*.xml*", "file:///*.xml*"], "run_at": "document_start"}, {"all_frames": true, "css": ["content/autofill.css"], "js": ["content/trigger-autofill-script-injection.js"], "matches": ["*://*/*", "file:///*"], "exclude_matches": ["*://*/*.xml*", "file:///*.xml*"], "run_at": "document_start"}], "background": {"service_worker": "background.js"}, "action": {"default_icon": {"19": "images/icon19.png", "38": "images/icon38.png"}, "default_title": "Bitwarden", "default_popup": "popup/index.html"}, "permissions": ["activeTab", "alarms", "clipboardRead", "clipboardWrite", "contextMenus", "idle", "offscreen", "scripting", "storage", "tabs", "unlimitedStorage", "webNavigation", "webRequest", "webRequestAuthProvider", "notifications"], "optional_permissions": ["nativeMessaging", "privacy"], "host_permissions": ["https://*/*", "http://*/*"], "content_security_policy": {"extension_pages": "script-src 'self' 'wasm-unsafe-eval'; object-src 'self'", "sandbox": "sandbox allow-scripts; script-src 'self'"}, "sandbox": {"pages": ["overlay/menu-button.html", "overlay/menu-list.html"]}, "commands": {"_execute_action": {"suggested_key": {"default": "Ctrl+Shift+Y", "linux": "Ctrl+Shift+U"}, "description": "__MSG_commandOpenPopup__"}, "autofill_login": {"suggested_key": {"default": "Ctrl+Shift+L"}, "description": "__MSG_commandAutofillLoginDesc__"}, "autofill_card": {"description": "__MSG_commandAutofillCardDesc__"}, "autofill_identity": {"description": "__MSG_commandAutofillIdentityDesc__"}, "generate_password": {"suggested_key": {"default": "Ctrl+Shift+9"}, "description": "__MSG_commandGeneratePasswordDesc__"}, "lock_vault": {"description": "__MSG_commandLockVaultDesc__"}}, "web_accessible_resources": [{"resources": ["content/fido2-page-script.js", "notification/bar.html", "images/icon38.png", "images/icon38_locked.png", "overlay/menu-button.html", "overlay/menu-list.html", "overlay/menu.html", "popup/fonts/*"], "matches": ["<all_urls>"]}], "storage": {"managed_schema": "managed_schema.json"}}