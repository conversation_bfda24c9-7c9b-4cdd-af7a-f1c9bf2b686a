@font-face {
  font-family: Roboto;
  src:
    url(../popup/fonts/roboto.woff2) format("woff2 supports variations"),
    url(../popup/fonts/roboto.woff2) format("woff2-variations");
  font-display: swap;
  font-weight: 100 900;
}

.cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed;z-index:1000}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute;z-index:1000}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;display:flex;max-width:100%;max-height:100%;z-index:1000}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);opacity:0;touch-action:manipulation;z-index:1000;transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}@media(prefers-reduced-motion){.cdk-overlay-backdrop{transition-duration:1ms}}.cdk-overlay-backdrop-showing{opacity:1}@media(forced-colors: active){.cdk-overlay-backdrop-showing{opacity:.6}}.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing,.cdk-high-contrast-active .cdk-overlay-transparent-backdrop{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;display:flex;flex-direction:column;min-width:1px;min-height:1px;z-index:1000}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}

@font-face{font-family:"bwi-font";src:url(../popup/fonts/bwi-font.svg) format("svg"),url(../popup/fonts/bwi-font.ttf) format("truetype"),url(../popup/fonts/bwi-font.woff) format("woff"),url(../popup/fonts/bwi-font.woff2) format("woff2");font-weight:normal;font-style:normal;font-display:block}.bwi{font-family:"bwi-font" !important;speak:never;font-style:normal;font-weight:normal;font-variant:normal;text-transform:none;line-height:1;display:inline-block;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.bwi-fw{width:1.2857142857em;text-align:center}.bwi-sm{font-size:.875em}.bwi-lg{font-size:1.3333333333em;line-height:.75em;vertical-align:-15%}.bwi-2x{font-size:2em}.bwi-3x{font-size:3em}.bwi-4x{font-size:4em}.bwi-spin{animation:bwi-spin 2s infinite linear}@keyframes bwi-spin{0%{transform:rotate(0deg)}100%{transform:rotate(359deg)}}.bwi-ul{padding-left:0;margin-left:2.1428571429em;list-style-type:none}.bwi-ul>li{position:relative}.bwi-li{position:absolute;left:-2.1428571429em;width:2.1428571429em;top:.1428571429em;text-align:center}.bwi-li.bwi-lg{left:-1.8571428571em}.bwi-rotate-270{transform:rotate(270deg)}.bwi-angle-down:before{content:""}.bwi-angle-left:before{content:""}.bwi-angle-right:before{content:""}.bwi-angle-up:before{content:""}.bwi-bell:before{content:""}.bwi-billing:before{content:""}.bwi-bitcoin:before{content:""}.bwi-browser-alt:before{content:""}.bwi-browser:before{content:""}.bwi-brush:before{content:""}.bwi-bug:before{content:""}.bwi-business:before{content:""}.bwi-camera:before{content:""}.bwi-check-circle:before{content:""}.bwi-check:before{content:""}.bwi-cli:before{content:""}.bwi-clock:before{content:""}.bwi-close:before{content:""}.bwi-cog-f:before{content:""}.bwi-cog:before{content:""}.bwi-collection:before{content:""}.bwi-collection-shared:before{content:""}.bwi-clone:before{content:""}.bwi-dollar:before{content:""}.bwi-down-solid:before{content:""}.bwi-download:before{content:""}.bwi-drag-and-drop:before{content:""}.bwi-ellipsis-h:before{content:""}.bwi-ellipsis-v:before{content:""}.bwi-envelope:before{content:""}.bwi-error:before{content:""}.bwi-exclamation-triangle:before{content:""}.bwi-external-link:before{content:""}.bwi-eye-slash:before{content:""}.bwi-eye:before{content:""}.bwi-family:before{content:""}.bwi-file-text:before{content:""}.bwi-file:before{content:""}.bwi-files:before{content:""}.bwi-filter:before{content:""}.bwi-folder:before{content:""}.bwi-generate:before{content:""}.bwi-globe:before{content:""}.bwi-hashtag:before{content:""}.bwi-id-card:before{content:""}.bwi-info-circle:before{content:""}.bwi-import:before{content:""}.bwi-key:before{content:""}.bwi-list-alt:before{content:""}.bwi-list:before{content:""}.bwi-lock-encrypted:before{content:""}.bwi-lock-f:before{content:""}.bwi-lock:before{content:""}.bwi-shield:before{content:""}.bwi-minus-circle:before{content:""}.bwi-mobile:before{content:""}.bwi-msp:before{content:""}.bwi-sticky-note:before{content:""}.bwi-numbered-list:before{content:""}.bwi-paperclip:before{content:""}.bwi-passkey:before{content:""}.bwi-pencil-square:before{content:""}.bwi-pencil:before{content:""}.bwi-plus-circle:before{content:""}.bwi-plus:before{content:""}.bwi-popout:before{content:""}.bwi-provider:before{content:""}.bwi-puzzle:before{content:""}.bwi-question-circle:before{content:""}.bwi-refresh:before{content:""}.bwi-search:before{content:""}.bwi-send:before{content:""}.bwi-share:before{content:""}.bwi-sign-in:before{content:""}.bwi-sign-out:before{content:""}.bwi-sliders:before{content:""}.bwi-spinner:before{content:""}.bwi-star-f:before{content:""}.bwi-star:before{content:""}.bwi-tag:before{content:""}.bwi-trash:before{content:""}.bwi-undo:before{content:""}.bwi-universal-access:before{content:""}.bwi-unlock:before{content:""}.bwi-up-down-btn:before{content:""}.bwi-up-solid:before{content:""}.bwi-user-monitor:before{content:""}.bwi-user:before{content:""}.bwi-users:before{content:""}.bwi-vault:before{content:""}.bwi-wireless:before{content:""}.bwi-wrench:before{content:""}.bwi-paypal:before{content:""}.bwi-credit-card:before{content:""}.bwi-desktop:before{content:""}.bwi-archive:before{content:""}.credit-card-icon{display:block;height:19px;width:24px;background-size:contain;background-repeat:no-repeat}.card-visa{background-image:url(../popup/images/visa-light.png)}.card-amex{background-image:url(../popup/images/amex-light.png)}.card-diners-club{background-image:url(../popup/images/diners_club-light.png)}.card-discover{background-image:url(../popup/images/discover-light.png)}.card-jcb{background-image:url(../popup/images/jcb-light.png)}.card-maestro{background-image:url(../popup/images/maestro-light.png)}.card-mastercard{background-image:url(../popup/images/mastercard-light.png)}.card-union-pay{background-image:url(../popup/images/union_pay-light.png)}.card-ru-pay{background-image:url(../popup/images/ru_pay-light.png)}.theme_dark .card-visa{background-image:url(../popup/images/visa-dark.png)}.theme_dark .card-amex{background-image:url(../popup/images/amex-dark.png)}.theme_dark .card-diners-club{background-image:url(../popup/images/diners_club-dark.png)}.theme_dark .card-discover{background-image:url(../popup/images/discover-dark.png)}.theme_dark .card-jcb{background-image:url(../popup/images/jcb-dark.png)}.theme_dark .card-maestro{background-image:url(../popup/images/maestro-dark.png)}.theme_dark .card-mastercard{background-image:url(../popup/images/mastercard-dark.png)}.theme_dark .card-union-pay{background-image:url(../popup/images/union_pay-dark.png)}.theme_dark .card-ru-pay{background-image:url(../popup/images/ru_pay-dark.png)}*{box-sizing:border-box;padding:0;margin:0}html{overflow:hidden;min-height:600px;height:100%}html.body-sm{min-height:500px}html.body-xs{min-height:400px}html.body-xxs{min-height:300px}html.body-3xs{min-height:240px}html.body-full{min-height:unset;width:100%;height:100%}html.body-full body{width:100%}html,body{font-family:Roboto,"Helvetica Neue",Helvetica,Arial,sans-serif;font-size:16px;line-height:1.42857143;-webkit-font-smoothing:antialiased}body{width:380px;height:100%;position:relative;min-height:inherit;overflow:hidden;color:#000;background-color:#f0f0f0}html.theme_light body{color:#000;background-color:#f0f0f0}html.theme_dark body{color:#fff;background-color:#161c26}h1,h2,h3,h4,h5,h6{font-family:Roboto,"Helvetica Neue",Helvetica,Arial,sans-serif;font-size:16px;font-weight:normal}p{margin-bottom:10px}ul,ol{margin-bottom:10px}img{border:none}a:not(popup-page a,popup-tab-navigation a){text-decoration:none}html.theme_light a:not(popup-page a,popup-tab-navigation a){color:#175ddc}html.theme_dark a:not(popup-page a,popup-tab-navigation a){color:#6f9df1}html.theme_light a:not(popup-page a,popup-tab-navigation a):hover,html.theme_light a:not(popup-page a,popup-tab-navigation a):focus{color:rgb(20.1037037037,81.2888888889,192.2962962963)}html.theme_dark a:not(popup-page a,popup-tab-navigation a):hover,html.theme_dark a:not(popup-page a,popup-tab-navigation a):focus{color:rgb(83.1113924051,138.0202531646,238.2886075949)}html.theme_light input:not(bit-form-field input,bit-search input),html.theme_light select:not(bit-form-field select),html.theme_light textarea:not(bit-form-field textarea){color:#000;background-color:#fff}html.theme_dark input:not(bit-form-field input,bit-search input),html.theme_dark select:not(bit-form-field select),html.theme_dark textarea:not(bit-form-field textarea){color:#fff;background-color:#2f343d}input,select,textarea,button:not(bit-chip-select button){font-size:16px;font-family:Roboto,"Helvetica Neue",Helvetica,Arial,sans-serif}html.theme_light input[type*=date]{color-scheme:light}html.theme_dark input[type*=date]{color-scheme:dark}html.theme_light ::-webkit-calendar-picker-indicator{filter:invert(46%) sepia(69%) saturate(6397%) hue-rotate(211deg) brightness(85%) contrast(103%)}html.theme_dark ::-webkit-calendar-picker-indicator{filter:brightness(0) saturate(100%) invert(86%) sepia(19%) saturate(152%) hue-rotate(184deg) brightness(87%) contrast(93%)}::-webkit-calendar-picker-indicator:hover{cursor:pointer}html.theme_light ::-webkit-calendar-picker-indicator:hover{filter:invert(46%) sepia(69%) saturate(6397%) hue-rotate(211deg) brightness(85%) contrast(103%)}html.theme_dark ::-webkit-calendar-picker-indicator:hover{filter:brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(93deg) brightness(103%) contrast(103%)}select{width:100%;padding:.35rem}button{cursor:pointer}textarea{resize:vertical}app-root>div{height:100%}main::-webkit-scrollbar,cdk-virtual-scroll-viewport::-webkit-scrollbar,.vault-select::-webkit-scrollbar{width:10px;height:10px}main::-webkit-scrollbar-track,.vault-select::-webkit-scrollbar-track{background-color:rgba(0,0,0,0)}html.theme_light cdk-virtual-scroll-viewport::-webkit-scrollbar-track{background-color:#f0f0f0}html.theme_dark cdk-virtual-scroll-viewport::-webkit-scrollbar-track{background-color:#161c26}main::-webkit-scrollbar-thumb,cdk-virtual-scroll-viewport::-webkit-scrollbar-thumb,.vault-select::-webkit-scrollbar-thumb{border-radius:10px;margin-right:1px}html.theme_light main::-webkit-scrollbar-thumb,html.theme_light cdk-virtual-scroll-viewport::-webkit-scrollbar-thumb,html.theme_light .vault-select::-webkit-scrollbar-thumb{background-color:rgba(100,100,100,.2)}html.theme_dark main::-webkit-scrollbar-thumb,html.theme_dark cdk-virtual-scroll-viewport::-webkit-scrollbar-thumb,html.theme_dark .vault-select::-webkit-scrollbar-thumb{background-color:#6e788a}html.theme_light main::-webkit-scrollbar-thumb:hover,html.theme_light cdk-virtual-scroll-viewport::-webkit-scrollbar-thumb:hover,html.theme_light .vault-select::-webkit-scrollbar-thumb:hover{background-color:rgba(100,100,100,.4)}html.theme_dark main::-webkit-scrollbar-thumb:hover,html.theme_dark cdk-virtual-scroll-viewport::-webkit-scrollbar-thumb:hover,html.theme_dark .vault-select::-webkit-scrollbar-thumb:hover{background-color:#8d94a5}header:not(bit-callout header,bit-dialog header,popup-page header){height:44px;display:flex}header:not(bit-callout header,bit-dialog header,popup-page header):not(.no-theme){border-bottom:1px solid #000}html.theme_light header:not(bit-callout header,bit-dialog header,popup-page header):not(.no-theme){color:#fff;background-color:#175ddc;border-bottom-color:#175ddc}html.theme_dark header:not(bit-callout header,bit-dialog header,popup-page header):not(.no-theme){color:#fff;background-color:#2f343d;border-bottom-color:#161c26}header:not(bit-callout header,bit-dialog header,popup-page header) .header-content{display:flex;flex:1 1 auto}header:not(bit-callout header,bit-dialog header,popup-page header) .header-content>.right,header:not(bit-callout header,bit-dialog header,popup-page header) .header-content>.right>.right{height:100%}header:not(bit-callout header,bit-dialog header,popup-page header) .left,header:not(bit-callout header,bit-dialog header,popup-page header) .right{flex:1;display:flex;min-width:-webkit-min-content}header:not(bit-callout header,bit-dialog header,popup-page header) .left .header-icon,header:not(bit-callout header,bit-dialog header,popup-page header) .right .header-icon{margin-right:5px}header:not(bit-callout header,bit-dialog header,popup-page header) .right{justify-content:flex-end;align-items:center}header:not(bit-callout header,bit-dialog header,popup-page header) .right app-avatar{max-height:30px;margin-right:5px}header:not(bit-callout header,bit-dialog header,popup-page header) .center{display:flex;align-items:center;text-align:center;min-width:0}header:not(bit-callout header,bit-dialog header,popup-page header) .login-center{margin:auto}header:not(bit-callout header,bit-dialog header,popup-page header) app-pop-out>button,header:not(bit-callout header,bit-dialog header,popup-page header) div>button:not(app-current-account button):not(.home-acc-switcher-btn),header:not(bit-callout header,bit-dialog header,popup-page header) div>a{border:none;padding:0 10px;text-decoration:none;display:flex;flex-direction:row;justify-content:center;align-items:center;height:100%;white-space:pre}html.theme_light header:not(bit-callout header,bit-dialog header,popup-page header) app-pop-out>button:not(.home-acc-switcher-btn):hover,html.theme_light header:not(bit-callout header,bit-dialog header,popup-page header) app-pop-out>button:not(.home-acc-switcher-btn):focus,html.theme_light header:not(bit-callout header,bit-dialog header,popup-page header) div>button:not(app-current-account button):not(.home-acc-switcher-btn):not(.home-acc-switcher-btn):hover,html.theme_light header:not(bit-callout header,bit-dialog header,popup-page header) div>button:not(app-current-account button):not(.home-acc-switcher-btn):not(.home-acc-switcher-btn):focus,html.theme_light header:not(bit-callout header,bit-dialog header,popup-page header) div>a:not(.home-acc-switcher-btn):hover,html.theme_light header:not(bit-callout header,bit-dialog header,popup-page header) div>a:not(.home-acc-switcher-btn):focus{background-color:hsla(0,0%,100%,.1);color:#fff}html.theme_dark header:not(bit-callout header,bit-dialog header,popup-page header) app-pop-out>button:not(.home-acc-switcher-btn):hover,html.theme_dark header:not(bit-callout header,bit-dialog header,popup-page header) app-pop-out>button:not(.home-acc-switcher-btn):focus,html.theme_dark header:not(bit-callout header,bit-dialog header,popup-page header) div>button:not(app-current-account button):not(.home-acc-switcher-btn):not(.home-acc-switcher-btn):hover,html.theme_dark header:not(bit-callout header,bit-dialog header,popup-page header) div>button:not(app-current-account button):not(.home-acc-switcher-btn):not(.home-acc-switcher-btn):focus,html.theme_dark header:not(bit-callout header,bit-dialog header,popup-page header) div>a:not(.home-acc-switcher-btn):hover,html.theme_dark header:not(bit-callout header,bit-dialog header,popup-page header) div>a:not(.home-acc-switcher-btn):focus{background-color:#3c424e;color:#fff}header:not(bit-callout header,bit-dialog header,popup-page header) app-pop-out>button[disabled],header:not(bit-callout header,bit-dialog header,popup-page header) div>button:not(app-current-account button):not(.home-acc-switcher-btn)[disabled],header:not(bit-callout header,bit-dialog header,popup-page header) div>a[disabled]{opacity:.65;cursor:default !important;background-color:inherit !important}header:not(bit-callout header,bit-dialog header,popup-page header) app-pop-out>button i+span,header:not(bit-callout header,bit-dialog header,popup-page header) div>button:not(app-current-account button):not(.home-acc-switcher-btn) i+span,header:not(bit-callout header,bit-dialog header,popup-page header) div>a i+span{margin-left:5px}header:not(bit-callout header,bit-dialog header,popup-page header) app-pop-out{display:flex;padding-right:.5em}header:not(bit-callout header,bit-dialog header,popup-page header) .title{font-weight:bold;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}header:not(bit-callout header,bit-dialog header,popup-page header) .search{padding:7px 10px;width:100%;text-align:left;position:relative;display:flex}header:not(bit-callout header,bit-dialog header,popup-page header) .search .bwi{position:absolute;top:15px;left:20px}html.theme_light header:not(bit-callout header,bit-dialog header,popup-page header) .search .bwi{color:rgb(174.**********,200.**********,246.**********)}html.theme_dark header:not(bit-callout header,bit-dialog header,popup-page header) .search .bwi{color:#bac0ce}header:not(bit-callout header,bit-dialog header,popup-page header) .search input:not(bit-form-field input){width:100%;margin:0;border:none;padding:5px 10px 5px 30px;border-radius:6px}html.theme_light header:not(bit-callout header,bit-dialog header,popup-page header) .search input:not(bit-form-field input){background-color:rgb(19.1382716049,77.3851851852,183.0617283951);color:#fff}html.theme_dark header:not(bit-callout header,bit-dialog header,popup-page header) .search input:not(bit-form-field input){background-color:#3c424e;color:#fff}html.theme_light header:not(bit-callout header,bit-dialog header,popup-page header) .search input:not(bit-form-field input)::selection{background-color:#fff;color:rgb(19.1382716049,77.3851851852,183.0617283951)}html.theme_dark header:not(bit-callout header,bit-dialog header,popup-page header) .search input:not(bit-form-field input)::selection{background-color:#fff;color:#3c424e}header:not(bit-callout header,bit-dialog header,popup-page header) .search input:not(bit-form-field input):focus{border-radius:6px;outline:none}html.theme_light header:not(bit-callout header,bit-dialog header,popup-page header) .search input:not(bit-form-field input):focus{background-color:rgb(18.1728395062,73.4814814815,173.8271604938)}html.theme_dark header:not(bit-callout header,bit-dialog header,popup-page header) .search input:not(bit-form-field input):focus{background-color:#4c525f}html.theme_light header:not(bit-callout header,bit-dialog header,popup-page header) .search input:not(bit-form-field input)::-webkit-input-placeholder{color:rgb(174.**********,200.**********,246.**********)}html.theme_dark header:not(bit-callout header,bit-dialog header,popup-page header) .search input:not(bit-form-field input)::-webkit-input-placeholder{color:#bac0ce}header:not(bit-callout header,bit-dialog header,popup-page header) .search input:not(bit-form-field input)[type=search]::-webkit-search-cancel-button{-webkit-appearance:none;appearance:none;height:15px;width:15px;background-repeat:no-repeat;mask-image:url(../popup/images/close-button-white.svg);-webkit-mask-image:url(../popup/images/close-button-white.svg)}html.theme_light header:not(bit-callout header,bit-dialog header,popup-page header) .search input:not(bit-form-field input)[type=search]::-webkit-search-cancel-button{background-color:#fff}html.theme_dark header:not(bit-callout header,bit-dialog header,popup-page header) .search input:not(bit-form-field input)[type=search]::-webkit-search-cancel-button{background-color:#fff}header:not(bit-callout header,bit-dialog header,popup-page header) .left+.search,header:not(bit-callout header,bit-dialog header,popup-page header) .left+.sr-only+.search{padding-left:0}header:not(bit-callout header,bit-dialog header,popup-page header) .left+.search .bwi,header:not(bit-callout header,bit-dialog header,popup-page header) .left+.sr-only+.search .bwi{left:10px}header:not(bit-callout header,bit-dialog header,popup-page header) .search+.right{margin-left:-10px}.content{padding:15px 5px}app-root{position:absolute;width:100%;height:100%;z-index:980}html.theme_light app-root{background-color:#f0f0f0}html.theme_dark app-root{background-color:#161c26}@media only screen and (min-width: 601px){header,main{padding:0 calc((100% - 500px)/2)}}main:not(popup-page main){position:absolute;top:44px;bottom:0;left:0;right:0;overflow-y:auto;overflow-x:hidden}html.theme_light main:not(popup-page main){background-color:#f0f0f0}html.theme_dark main:not(popup-page main){background-color:#161c26}main:not(popup-page main).no-header{top:0}main:not(popup-page main).flex{display:flex;flex-flow:column;height:calc(100% - 44px)}.center-content,.no-items,.full-loading-spinner{display:flex;justify-content:center;align-items:center;height:100%;flex-direction:column;flex-grow:1}.no-items,.full-loading-spinner{text-align:center;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}html.theme_light .no-items .no-items-image,html.theme_light .full-loading-spinner .no-items-image{content:url(../popup/images/search-desktop-light.svg)}html.theme_dark .no-items .no-items-image,html.theme_dark .full-loading-spinner .no-items-image{content:url(../popup/images/search-desktop-dark.svg)}.no-items .bwi,.full-loading-spinner .bwi{margin-bottom:10px}html.theme_light .no-items .bwi,html.theme_light .full-loading-spinner .bwi{color:#767679}html.theme_dark .no-items .bwi,html.theme_dark .full-loading-spinner .bwi{color:#bac0ce}.cdk-virtual-scroll-viewport{width:100%;height:100%;overflow-y:auto;overflow-x:hidden}.cdk-virtual-scroll-content-wrapper{width:100%}.row{display:flex;margin:0 -15px;width:100%}.col{flex-basis:0;flex-grow:1;padding:0 15px}.box{position:relative;width:100%}.box.first{margin-top:0}.box .box-header{margin:0 10px 5px 10px;text-transform:uppercase;display:flex}html.theme_light .box .box-header{color:#777}html.theme_dark .box .box-header{color:#bac0ce}html.theme_light .box .box-content{background-color:#f0f0f0;border-color:#ddd}html.theme_dark .box .box-content{background-color:#161c26;border-color:#161c26}.box .box-content.box-content-padded{padding:10px 15px}.box .box-content.condensed .box-content-row,.box .box-content .box-content-row.condensed{padding-top:5px;padding-bottom:5px}html.theme_light .box .box-content.no-hover .box-content-row:hover,html.theme_light .box .box-content.no-hover .box-content-row:focus,html.theme_light .box .box-content .box-content-row.no-hover:hover,html.theme_light .box .box-content .box-content-row.no-hover:focus{background-color:#fff !important}html.theme_dark .box .box-content.no-hover .box-content-row:hover,html.theme_dark .box .box-content.no-hover .box-content-row:focus,html.theme_dark .box .box-content .box-content-row.no-hover:hover,html.theme_dark .box .box-content .box-content-row.no-hover:focus{background-color:#2f343d !important}.box .box-content.single-line .box-content-row,.box .box-content .box-content-row.single-line{padding-top:10px;padding-bottom:10px;margin:5px}.box .box-content.row-top-padding{padding-top:10px}.box .box-footer{margin:0 5px 5px 5px;padding:0 10px 5px 10px;font-size:12px}.box .box-footer button.btn{font-size:12px;padding:0}.box .box-footer button.btn.primary{font-size:16px;padding:7px 15px;width:100%}html.theme_light .box .box-footer button.btn.primary:hover{border-color:!important}html.theme_dark .box .box-footer button.btn.primary:hover{border-color:!important}html.theme_light .box .box-footer{color:#777}html.theme_dark .box .box-footer{color:#bac0ce}.box.list{margin:10px 0 20px 0}.box.list .box-content .virtual-scroll-item{display:inline-block;width:100%}.box.list .box-content .box-content-row{text-decoration:none;border-radius:6px}html.theme_light .box.list .box-content .box-content-row{color:#000;background-color:#fff}html.theme_dark .box.list .box-content .box-content-row{color:#fff;background-color:#2f343d}.box.list .box-content .box-content-row.padded{padding-top:10px;padding-bottom:10px}html.theme_light .box.list .box-content .box-content-row.no-hover:hover{background-color:#fff !important}html.theme_dark .box.list .box-content .box-content-row.no-hover:hover{background-color:#2f343d !important}html.theme_light .box.list .box-content .box-content-row:hover,html.theme_light .box.list .box-content .box-content-row:focus,html.theme_light .box.list .box-content .box-content-row.active{background-color:#fbfbfb}html.theme_dark .box.list .box-content .box-content-row:hover,html.theme_dark .box.list .box-content .box-content-row:focus,html.theme_dark .box.list .box-content .box-content-row.active{background-color:#3c424e}.box.list .box-content .box-content-row:focus{border-left:5px solid #000;padding-left:5px}html.theme_light .box.list .box-content .box-content-row:focus{border-left-color:#777}html.theme_dark .box.list .box-content .box-content-row:focus{border-left-color:#bac0ce}.box.list .box-content .box-content-row .action-buttons .row-btn{padding-left:5px;padding-right:5px}.box.list .box-content .box-content-row .text:not(.no-ellipsis),.box.list .box-content .box-content-row .detail{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.box.list .box-content .box-content-row .row-main{display:flex;min-width:0;align-items:normal}.box.list .box-content .box-content-row .row-main .row-main-content{min-width:0}.box.list .box-content.single-line .box-content-row{display:flex;padding-top:10px;padding-bottom:10px;margin:5px;border-radius:6px}.box-content-row{display:block;padding:5px 10px;position:relative;z-index:1;border-radius:6px;margin:3px 5px}html.theme_light .box-content-row{background-color:#fff}html.theme_dark .box-content-row{background-color:#2f343d}.box-content-row:last-child:before{border:none;height:0}.box-content-row.override-last:last-child:before{border-bottom:1px solid #000}html.theme_light .box-content-row.override-last:last-child:before{border-bottom-color:#f0f0f0}html.theme_dark .box-content-row.override-last:last-child:before{border-bottom-color:#4c525f}.box-content-row.last:last-child:before{border-bottom:1px solid #000}html.theme_light .box-content-row.last:last-child:before{border-bottom-color:#f0f0f0}html.theme_dark .box-content-row.last:last-child:before{border-bottom-color:#4c525f}.box-content-row:after{content:"";display:table;clear:both}html.theme_light .box-content-row:hover,html.theme_light .box-content-row:focus,html.theme_light .box-content-row.active{background-color:#fbfbfb}html.theme_dark .box-content-row:hover,html.theme_dark .box-content-row:focus,html.theme_dark .box-content-row.active{background-color:#3c424e}.box-content-row.pre{white-space:pre;overflow-x:auto}.box-content-row.pre-wrap{white-space:pre-wrap;overflow-x:auto}.box-content-row .row-label,.box-content-row label{font-size:12px;display:block;width:100%;margin-bottom:5px}html.theme_light .box-content-row .row-label,html.theme_light .box-content-row label{color:#777}html.theme_dark .box-content-row .row-label,html.theme_dark .box-content-row label{color:#bac0ce}.box-content-row .row-label .sub-label,.box-content-row label .sub-label{margin-left:10px}.box-content-row .flex-label{font-size:12px;display:flex;flex-grow:1;margin-bottom:5px}html.theme_light .box-content-row .flex-label{color:#777}html.theme_dark .box-content-row .flex-label{color:#bac0ce}.box-content-row .flex-label>a{flex-grow:0}.box-content-row .text,.box-content-row .detail{display:block;text-align:left}html.theme_light .box-content-row .text,html.theme_light .box-content-row .detail{color:#000}html.theme_dark .box-content-row .text,html.theme_dark .box-content-row .detail{color:#fff}.box-content-row .detail{font-size:12px}html.theme_light .box-content-row .detail{color:#777}html.theme_dark .box-content-row .detail{color:#bac0ce}.box-content-row .img-right,.box-content-row .txt-right{float:right;margin-left:10px}.box-content-row .row-main{flex-grow:1;min-width:0}.box-content-row.box-content-row-flex,.box-content-row .box-content-row-flex,.box-content-row.box-content-row-checkbox,.box-content-row.box-content-row-link,.box-content-row.box-content-row-input,.box-content-row.box-content-row-slider,.box-content-row.box-content-row-multi{display:flex;align-items:center;word-break:break-all}.box-content-row.box-content-row-flex.box-content-row-word-break,.box-content-row .box-content-row-flex.box-content-row-word-break,.box-content-row.box-content-row-checkbox.box-content-row-word-break,.box-content-row.box-content-row-link.box-content-row-word-break,.box-content-row.box-content-row-input.box-content-row-word-break,.box-content-row.box-content-row-slider.box-content-row-word-break,.box-content-row.box-content-row-multi.box-content-row-word-break{word-break:normal}.box-content-row.box-content-row-multi input:not([type=checkbox]){width:100%}.box-content-row.box-content-row-multi input+label.sr-only+select{margin-top:5px}.box-content-row.box-content-row-multi>a,.box-content-row.box-content-row-multi>button{padding:8px 8px 8px 4px;margin:0}html.theme_light .box-content-row.box-content-row-multi>a,html.theme_light .box-content-row.box-content-row-multi>button{color:#c83522}html.theme_dark .box-content-row.box-content-row-multi>a,html.theme_dark .box-content-row.box-content-row-multi>button{color:#ff8d85}.box-content-row.box-content-row-multi,.box-content-row.box-content-row-newmulti{padding-left:10px}html.theme_light .box-content-row.box-content-row-newmulti{color:#175ddc}html.theme_dark .box-content-row.box-content-row-newmulti{color:#6f9df1}.box-content-row.box-content-row-checkbox,.box-content-row.box-content-row-link,.box-content-row.box-content-row-input,.box-content-row.box-content-row-slider{padding-top:10px;padding-bottom:10px;margin:5px}.box-content-row.box-content-row-checkbox label,.box-content-row.box-content-row-checkbox .row-label,.box-content-row.box-content-row-link label,.box-content-row.box-content-row-link .row-label,.box-content-row.box-content-row-input label,.box-content-row.box-content-row-input .row-label,.box-content-row.box-content-row-slider label,.box-content-row.box-content-row-slider .row-label{font-size:16px;display:block;width:initial;margin-bottom:0}html.theme_light .box-content-row.box-content-row-checkbox label,html.theme_light .box-content-row.box-content-row-checkbox .row-label,html.theme_light .box-content-row.box-content-row-link label,html.theme_light .box-content-row.box-content-row-link .row-label,html.theme_light .box-content-row.box-content-row-input label,html.theme_light .box-content-row.box-content-row-input .row-label,html.theme_light .box-content-row.box-content-row-slider label,html.theme_light .box-content-row.box-content-row-slider .row-label{color:#000}html.theme_dark .box-content-row.box-content-row-checkbox label,html.theme_dark .box-content-row.box-content-row-checkbox .row-label,html.theme_dark .box-content-row.box-content-row-link label,html.theme_dark .box-content-row.box-content-row-link .row-label,html.theme_dark .box-content-row.box-content-row-input label,html.theme_dark .box-content-row.box-content-row-input .row-label,html.theme_dark .box-content-row.box-content-row-slider label,html.theme_dark .box-content-row.box-content-row-slider .row-label{color:#fff}html.theme_light .box-content-row.box-content-row-checkbox>span,html.theme_light .box-content-row.box-content-row-link>span,html.theme_light .box-content-row.box-content-row-input>span,html.theme_light .box-content-row.box-content-row-slider>span{color:#777}html.theme_dark .box-content-row.box-content-row-checkbox>span,html.theme_dark .box-content-row.box-content-row-link>span,html.theme_dark .box-content-row.box-content-row-input>span,html.theme_dark .box-content-row.box-content-row-slider>span{color:#bac0ce}.box-content-row.box-content-row-checkbox>input,.box-content-row.box-content-row-link>input,.box-content-row.box-content-row-input>input,.box-content-row.box-content-row-slider>input{margin:0 0 0 auto;padding:0}.box-content-row.box-content-row-checkbox>*,.box-content-row.box-content-row-link>*,.box-content-row.box-content-row-input>*,.box-content-row.box-content-row-slider>*{margin-right:15px}.box-content-row.box-content-row-checkbox>*:last-child,.box-content-row.box-content-row-link>*:last-child,.box-content-row.box-content-row-input>*:last-child,.box-content-row.box-content-row-slider>*:last-child{margin-right:0}.box-content-row.box-content-row-checkbox-left{justify-content:flex-start}.box-content-row.box-content-row-checkbox-left>input{margin:0 15px 0 0}.box-content-row.box-content-row-input label{white-space:nowrap}.box-content-row.box-content-row-input input{text-align:right}.box-content-row.box-content-row-input input[type=number]{max-width:50px}.box-content-row.box-content-row-slider input[type=range]{height:10px}.box-content-row.box-content-row-slider input[type=number]{width:45px}.box-content-row.box-content-row-slider label{white-space:nowrap}.box-content-row input:not([type=checkbox]):not([type=radio]),.box-content-row textarea{border:none;width:100%;background-color:rgba(0,0,0,0) !important}html.theme_light .box-content-row input:not([type=checkbox]):not([type=radio])::-webkit-input-placeholder,html.theme_light .box-content-row textarea::-webkit-input-placeholder{color:hsl(0,0%,81.6666666667%)}html.theme_dark .box-content-row input:not([type=checkbox]):not([type=radio])::-webkit-input-placeholder,html.theme_dark .box-content-row textarea::-webkit-input-placeholder{color:#bac0ce}.box-content-row input:not([type=checkbox]):not([type=radio]):not([type=file]):focus,.box-content-row textarea:not([type=file]):focus{outline:none}.box-content-row select{width:100%;border:1px solid #000;border-radius:6px;padding:7px 4px}html.theme_light .box-content-row select{border-color:hsl(0,0%,79.6666666667%)}html.theme_dark .box-content-row select{border-color:#4c525f}.box-content-row .action-buttons{display:flex;margin-left:5px}.box-content-row .action-buttons.action-buttons-fixed{align-self:start;margin-top:2px}.box-content-row .action-buttons .row-btn{cursor:pointer;padding:10px 8px;background:none;border:none}html.theme_light .box-content-row .action-buttons .row-btn{color:#175ddc}html.theme_dark .box-content-row .action-buttons .row-btn{color:#bac0ce}html.theme_light .box-content-row .action-buttons .row-btn:hover,html.theme_light .box-content-row .action-buttons .row-btn:focus{color:rgb(18.1728395062,73.4814814815,173.8271604938)}html.theme_dark .box-content-row .action-buttons .row-btn:hover,html.theme_dark .box-content-row .action-buttons .row-btn:focus{color:#fff}.box-content-row .action-buttons .row-btn.disabled,.box-content-row .action-buttons .row-btn[disabled]{cursor:default !important}html.theme_light .box-content-row .action-buttons .row-btn.disabled,html.theme_light .box-content-row .action-buttons .row-btn[disabled]{color:#767679;opacity:1}html.theme_dark .box-content-row .action-buttons .row-btn.disabled,html.theme_dark .box-content-row .action-buttons .row-btn[disabled]{color:#bac0ce;opacity:.5}html.theme_light .box-content-row .action-buttons .row-btn.disabled:hover,html.theme_light .box-content-row .action-buttons .row-btn[disabled]:hover{color:#767679;opacity:1}html.theme_dark .box-content-row .action-buttons .row-btn.disabled:hover,html.theme_dark .box-content-row .action-buttons .row-btn[disabled]:hover{color:#bac0ce;opacity:.5}.box-content-row .action-buttons.no-pad .row-btn{padding-top:0;padding-bottom:0}.box-content-row:not(.box-draggable-row) .action-buttons .row-btn:last-child{margin-right:-3px}.box-content-row.box-draggable-row.box-content-row-checkbox input[type=checkbox]+.drag-handle{margin-left:10px}.box-content-row .drag-handle{cursor:move;padding:10px 2px 10px 8px;user-select:none}html.theme_light .box-content-row .drag-handle{color:#777}html.theme_dark .box-content-row .drag-handle{color:#bac0ce}.box-content-row.cdk-drag-preview{position:relative;display:flex;align-items:center;opacity:.8}html.theme_light .box-content-row.cdk-drag-preview{background-color:#fff}html.theme_dark .box-content-row.cdk-drag-preview{background-color:#2f343d}.box-content-row select.field-type{margin:5px 0 0 25px;width:calc(100% - 25px)}html.theme_light .box-content-row .row-sub-icon,html.theme_light .box-content-row .row-sub-label+i.bwi{color:#767679}html.theme_dark .box-content-row .row-sub-icon,html.theme_dark .box-content-row .row-sub-label+i.bwi{color:#bac0ce}.box-content-row .icon{display:flex;justify-content:center;align-items:center;min-width:34px;margin-left:-5px}html.theme_light .box-content-row .icon{color:#777}html.theme_dark .box-content-row .icon{color:#bac0ce}.box-content-row .icon.icon-small{min-width:25px}.box-content-row .icon img{border-radius:6px;max-height:20px;max-width:20px}.box-content-row .progress{display:flex;height:5px;overflow:hidden;margin:5px -15px -10px}.box-content-row .progress .progress-bar{display:flex;flex-direction:column;justify-content:center;white-space:nowrap;background-color:#175ddc}.box-content-row .radio-group{display:flex;justify-content:flex-start;align-items:center;margin-bottom:5px}.box-content-row .radio-group input{flex-grow:0}.box-content-row .radio-group label{margin:0 0 0 5px;flex-grow:1;font-size:16px;display:block;width:100%}html.theme_light .box-content-row .radio-group label{color:#000}html.theme_dark .box-content-row .radio-group label{color:#fff}.box-content-row .radio-group.align-start{align-items:start;margin-top:10px}.box-content-row .radio-group.align-start label{margin-top:-4px}.truncate{display:inline-block;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}html.theme_light form .box .box-content .box-content-row.no-hover:hover{background-color:rgba(0,0,0,0) !important}html.theme_dark form .box .box-content .box-content-row.no-hover:hover{background-color:rgba(0,0,0,0) !important}.btn{border-radius:6px;padding:7px 15px;border:1px solid #000;font-size:16px;text-align:center;cursor:pointer}html.theme_light .btn{background-color:#fff;border-color:rgb(190.4,190.4,190.4);color:#666}html.theme_dark .btn{background-color:#3c424e;border-color:#4c525f;color:#bac0ce}html.theme_light .btn.primary{color:rgb(19.1382716049,77.3851851852,183.0617283951)}html.theme_dark .btn.primary{color:#6f9df1}html.theme_light .btn.danger{color:rgb(156.4102564103,41.4487179487,26.5897435897)}html.theme_dark .btn.danger{color:#ff8d85}.btn.callout-half{font-weight:bold;max-width:50%}.btn:hover:not([disabled]){cursor:pointer}html.theme_light .btn:hover:not([disabled]){background-color:hsl(0,0%,98.5%);border-color:hsl(0,0%,57.6666666667%);color:hsl(0,0%,30%)}html.theme_dark .btn:hover:not([disabled]){background-color:hsl(220,13.0434782609%,25.5588235294%);border-color:rgb(37.4666666667,40.4245614035,46.8333333333);color:hsl(222,16.9491525424%,66.862745098%)}html.theme_light .btn:hover:not([disabled]).primary{color:rgb(16.2419753086,65.6740740741,155.3580246914)}html.theme_dark .btn:hover:not([disabled]).primary{color:rgb(83.1113924051,138.0202531646,238.2886075949)}html.theme_light .btn:hover:not([disabled]).danger{color:rgb(130.2564102564,34.5179487179,22.1435897436)}html.theme_dark .btn:hover:not([disabled]).danger{color:rgb(255,112.406557377,102.4)}.btn:focus:not([disabled]){cursor:pointer;outline:0}html.theme_light .btn:focus:not([disabled]){background-color:hsl(0,0%,94%);border-color:hsl(0,0%,49.6666666667%)}html.theme_dark .btn:focus:not([disabled]){background-color:hsl(220,13.0434782609%,21.0588235294%);border-color:rgb(19.3333333333,20.8596491228,24.1666666667)}.btn[disabled]{opacity:.65;cursor:default !important}.btn.block{display:block;width:calc(100% - 10px);margin:0 auto}.btn.link,.btn.neutral{border:none !important;background:none !important}.btn.link:focus,.btn.neutral:focus{text-decoration:underline}.action-buttons .btn:focus{outline:auto}button.box-content-row{display:block;width:calc(100% - 10px);text-align:left;border-color:none}html.theme_light button.box-content-row{background-color:#fff}html.theme_dark button.box-content-row{background-color:#2f343d}button{border:none;background:rgba(0,0,0,0);color:inherit}.login-buttons .btn.block{width:100%;margin-bottom:10px}small,.small{font-size:12px}html.theme_light .bg-primary{background-color:#175ddc !important}html.theme_dark .bg-primary{background-color:#6f9df1 !important}html.theme_light .bg-success{background-color:#017e45 !important}html.theme_dark .bg-success{background-color:#52e07c !important}html.theme_light .bg-danger{background-color:#c83522 !important}html.theme_dark .bg-danger{background-color:#ff8d85 !important}html.theme_light .bg-info{background-color:#555 !important}html.theme_dark .bg-info{background-color:#a4b0c6 !important}html.theme_light .bg-warning{background-color:#8b6609 !important}html.theme_dark .bg-warning{background-color:#ffeb66 !important}html.theme_light .text-primary{color:#175ddc !important}html.theme_dark .text-primary{color:#6f9df1 !important}html.theme_light .text-success{color:#017e45 !important}html.theme_dark .text-success{color:#52e07c !important}html.theme_light .text-muted{color:#777 !important}html.theme_dark .text-muted{color:#bac0ce !important}html.theme_light .text-default{color:#000 !important}html.theme_dark .text-default{color:#fff !important}html.theme_light .text-danger{color:#c83522 !important}html.theme_dark .text-danger{color:#ff8d85 !important}html.theme_light .text-info{color:#555 !important}html.theme_dark .text-info{color:#a4b0c6 !important}html.theme_light .text-warning{color:#8b6609 !important}html.theme_dark .text-warning{color:#ffeb66 !important}.text-center{text-align:center}.font-weight-semibold{font-weight:600}p.lead{font-size:18px;margin-bottom:20px;font-weight:normal}.flex-right{margin-left:auto}.flex-bottom{margin-top:auto}.no-margin{margin:0 !important}.display-block{display:block !important}.monospaced{font-family:Menlo,Monaco,Consolas,"Courier New",monospace}.show-whitespace{white-space:pre-wrap}.img-responsive{display:block;max-width:100%;height:auto}.img-rounded{border-radius:6px}.select-index-top{position:relative;z-index:100}.sr-only{position:absolute !important;width:1px !important;height:1px !important;padding:0 !important;margin:-1px !important;overflow:hidden !important;clip:rect(0, 0, 0, 0) !important;border:0 !important}:not(:focus)>.exists-only-on-parent-focus{display:none}.password-wrapper{overflow-wrap:break-word;white-space:pre-wrap;min-width:0}html.theme_light .password-number{color:#007fde}html.theme_dark .password-number{color:#6f9df1}html.theme_light .password-special{color:#c40800}html.theme_dark .password-special{color:#ff8d85}.password-character{display:inline-flex;flex-direction:column;align-items:center;width:30px;height:36px;font-weight:600}html.theme_light .password-character:nth-child(odd){background-color:#f0f0f0}html.theme_dark .password-character:nth-child(odd){background-color:#161c26}.password-count{white-space:nowrap;font-size:8px}html.theme_light .password-count{color:#212529 !important}html.theme_dark .password-count{color:#fff !important}#duo-frame{background:url(../popup/images/loading.svg) 0 0 no-repeat;width:100%;height:470px;margin-bottom:-10px}#duo-frame iframe{width:100%;height:100%;border:none}#web-authn-frame{width:100%;height:40px}#web-authn-frame iframe{border:none;height:100%;width:100%}body.linux-webauthn{width:485px !important}body.linux-webauthn #web-authn-frame iframe{width:375px;margin:0 55px}app-root>#loading{display:flex;text-align:center;justify-content:center;align-items:center;height:100%;width:100%;color:#777}html.theme_light app-root>#loading{color:#777}html.theme_dark app-root>#loading{color:#bac0ce}app-vault-icon,.app-vault-icon{display:flex}.logo-image{margin:0 auto;width:142px;height:21px;background-size:142px 21px;background-repeat:no-repeat}html.theme_light .logo-image{background-image:url(../popup/images/<EMAIL>)}html.theme_dark .logo-image{background-image:url(../popup/images/<EMAIL>)}@media(min-width: 219px){.logo-image{width:189px;height:28px;background-size:189px 28px}}@media(min-width: 314px){.logo-image{width:284px;height:43px;background-size:284px 43px}}[hidden]{display:none !important}.draggable{cursor:move}input[type=password]::-ms-reveal{display:none}.flex{display:flex}.flex.flex-grow>*{flex:1}html.theme_light :not(bit-form-field input)::selection{color:#f0f0f0;background-color:#1252a3}html.theme_dark :not(bit-form-field input)::selection{color:#161c26;background-color:#6f9df1}h1,h2,h3,label,a,button,p,img,.box-header,.box-footer,.callout,.row-label,.modal-title,.overlay-container{user-select:none}h1.user-select,h2.user-select,h3.user-select,label.user-select,a.user-select,button.user-select,p.user-select,img.user-select,.box-header.user-select,.box-footer.user-select,.callout.user-select,.row-label.user-select,.modal-title.user-select,.overlay-container.user-select{user-select:auto}.box-footer button,.box-footer a{line-height:1}@keyframes redraw{0%{opacity:.99}100%{opacity:1}}html.force_redraw{animation:redraw 1s linear infinite}app-vault-icon:not(app-vault-list-items-container app-vault-icon)>div{display:flex;justify-content:center;align-items:center;float:left;height:36px;width:34px;margin-left:-5px}html.browser_safari.safari_height_fix body{height:360px !important}html.browser_safari.safari_height_fix body.body-xs{height:300px !important}html.browser_safari.safari_height_fix body.body-full{height:100% !important}html.browser_safari header .search .bwi{left:20px}html.browser_safari header .left+.search .bwi{left:10px}html.browser_safari .content.login-page{padding-top:100px}html.browser_safari app-root{border-width:1px;border-style:solid;border-color:#000}html.browser_safari.theme_light app-root{border-color:#777}app-home{position:fixed;height:100%;width:100%}app-home .center-content{margin-top:-50px;height:calc(100% + 50px)}app-home img{width:284px;margin:0 auto}app-home p.lead{margin:30px 0}app-home .btn+.btn{margin-top:10px}app-home button.settings-icon{position:absolute;top:10px;left:10px}html.theme_light app-home button.settings-icon{color:#777}html.theme_dark app-home button.settings-icon{color:#bac0ce}app-home button.settings-icon:not(:hover):not(:focus) span{clip:rect(0 0 0 0);clip-path:inset(50%);height:1px;overflow:hidden;position:absolute;white-space:nowrap;width:1px}app-home button.settings-icon:hover,app-home button.settings-icon:focus{text-decoration:none}html.theme_light app-home button.settings-icon:hover,html.theme_light app-home button.settings-icon:focus{color:#175ddc}html.theme_dark app-home button.settings-icon:hover,html.theme_dark app-home button.settings-icon:focus{color:#6f9df1}body.body-sm app-home .center-content,body.body-xs app-home .center-content{margin-top:0;height:100%}body.body-sm app-home p.lead,body.body-xs app-home p.lead{margin:15px 0}body.body-full app-home .center-content{margin-top:-80px;height:calc(100% + 80px)}.createAccountLink{padding:30px 10px 0 10px}.remember-email-check{padding-top:18px;padding-left:10px;padding-bottom:18px}.login-buttons>button{margin:15px 0 15px 0}.useBrowserlink{margin-left:5px;margin-top:20px}.useBrowserlink span{font-weight:700;font-size:12px}.fido2-browser-selector-dropdown{padding:8px;width:100%;box-shadow:0 2px 2px 0 rgba(0,0,0,.14),0 3px 1px -2px rgba(0,0,0,.12),0 1px 5px 0 rgba(0,0,0,.2);border-radius:6px}html.theme_light .fido2-browser-selector-dropdown{background-color:#fff}html.theme_dark .fido2-browser-selector-dropdown{background-color:#2f343d}.fido2-browser-selector-dropdown-item{width:100%;text-align:left;padding:0px 15px 0px 5px;margin-bottom:5px;border-radius:3px;border:1px solid rgba(0,0,0,0);transition:all .2s ease-in-out}html.theme_light .fido2-browser-selector-dropdown-item{color:#000 !important}html.theme_dark .fido2-browser-selector-dropdown-item{color:#fff !important}html.theme_light .fido2-browser-selector-dropdown-item:hover{background-color:#fbfbfb !important}html.theme_dark .fido2-browser-selector-dropdown-item:hover{background-color:#3c424e !important}.fido2-browser-selector-dropdown-item:last-child{margin-bottom:0}.mfaType0{content:url(../popup/images/0.png);max-width:100px}.mfaType2{content:url(../popup/images/2.png);max-width:100px}.mfaType3{content:url(../popup/images/3.png);max-width:100px}.mfaType4{content:url(../popup/images/4.png);max-width:100px}.mfaType6{content:url(../popup/images/6.png);max-width:100px}html.theme_light .mfaType1{content:url(../popup/images/1.png);max-width:100px;max-height:45px}html.theme_dark .mfaType1{content:url(../popup/images/1-w.png);max-width:100px;max-height:45px}html.theme_light .mfaType7{content:url(../popup/images/7.png);max-width:100px}html.theme_dark .mfaType7{content:url(../popup/images/7-w.png);max-width:100px}.ng-select{height:100%}.ng-select.ng-select-opened>.ng-select-container{background:rgba(0,0,0,0)}.ng-select.ng-select-opened>.ng-select-container:hover{box-shadow:none}.ng-select.ng-select-opened>.ng-select-container .ng-arrow{top:-2px;border-color:rgba(0,0,0,0) rgba(0,0,0,0) rgb(var(--color-secondary-700));border-width:0 5px 5px}.ng-select.ng-select-opened>.ng-select-container .ng-arrow:hover{border-color:rgba(0,0,0,0) rgba(0,0,0,0) rgb(var(--color-secondary-700))}.ng-select.ng-select-opened.ng-select-top>.ng-select-container{border-top-right-radius:0;border-top-left-radius:0}.ng-select.ng-select-opened.ng-select-right>.ng-select-container{border-top-right-radius:0;border-bottom-right-radius:0}.ng-select.ng-select-opened.ng-select-bottom>.ng-select-container{border-bottom-right-radius:0;border-bottom-left-radius:0}.ng-select.ng-select-opened.ng-select-left>.ng-select-container{border-top-left-radius:0;border-bottom-left-radius:0}.ng-select.ng-select-focused:not(.ng-select-opened)>.ng-select-container{border-color:rgb(var(--color-primary-700));box-shadow:none}.ng-select.ng-select-disabled>.ng-select-container{background-color:rgb(var(--color-secondary-100))}.ng-select .ng-has-value .ng-placeholder{display:none}.ng-select .ng-select-container{color:rgb(var(--color-text-main));background-color:rgb(var(--color-background));border-radius:.5rem;border:none;height:100%;align-items:center}.ng-select .ng-select-container:hover{box-shadow:none}.ng-select .ng-select-container .ng-value-container{align-items:center;padding:6px 0px 5px 1rem}[dir=rtl] .ng-select .ng-select-container .ng-value-container{padding-right:1rem;padding-left:0}.ng-select .ng-select-container .ng-value-container .ng-placeholder{color:rgb(var(--color-text-muted))}.ng-select .ng-select-container .ng-value-container .ng-input{padding-top:2px}.ng-select .ng-select-container .ng-value-container .ng-input>input{color:rgb(var(--color-text-main))}.ng-select.ng-select-single .ng-select-container{height:100%}.ng-select.ng-select-single .ng-select-container .ng-value-container{display:flex;height:100%}.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input{left:0;padding-left:1rem;padding-right:50px}[dir=rtl] .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input{padding-right:1rem;padding-left:50px}.ng-select.ng-select-multiple.ng-select-disabled>.ng-select-container .ng-value-container .ng-value{background-color:rgb(var(--color-secondary-100));border:0px solid rgb(var(--color-secondary-600))}.ng-select.ng-select-multiple.ng-select-disabled>.ng-select-container .ng-value-container .ng-value .ng-value-label{padding:0 5px}.ng-select.ng-select-multiple .ng-select-container .ng-value-container{height:100%}[dir=rtl] .ng-select.ng-select-multiple .ng-select-container .ng-value-container{padding-left:0}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value{font-size:.9em;color:rgb(var(--color-text-main));background-color:rgba(0,0,0,0);border-radius:2px;margin:4px 5px 4px 0px}[dir=rtl] .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value{margin-right:0;margin-left:5px}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value.ng-value-disabled{background-color:rgb(var(--color-secondary-100))}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value.ng-value-disabled .ng-value-label{padding-left:5px}[dir=rtl] .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value.ng-value-disabled .ng-value-label{padding-left:0;padding-right:5px}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-label{display:inline-block;padding:1px 5px}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon{display:inline-block;padding:1px 5px}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon:hover{background-color:rgb(var(--color-secondary-700))}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon.left{border-right:1px solid rgba(0,0,0,0)}[dir=rtl] .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon.left{border-left:1px solid rgba(0,0,0,0);border-right:none}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon.right{border-left:1px solid rgba(0,0,0,0)}[dir=rtl] .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon.right{border-left:0;border-right:1px solid rgba(0,0,0,0)}.ng-select .ng-clear-wrapper{color:rgb(var(--color-text-muted));padding-top:2.5px;border-radius:.5rem;text-align:center}.ng-select .ng-clear-wrapper:hover .ng-clear{color:rgb(var(--color-text-main))}.ng-select .ng-spinner-zone{padding:5px 5px 0 0}[dir=rtl] .ng-select .ng-spinner-zone{padding:5px 0 0 5px}.ng-select .ng-arrow-wrapper{width:25px;padding-right:5px}[dir=rtl] .ng-select .ng-arrow-wrapper{padding-left:5px;padding-right:0}.ng-select .ng-arrow-wrapper:hover .ng-arrow{border-top-color:rgb(var(--color-secondary-700))}.ng-select .ng-arrow-wrapper .ng-arrow{border-color:rgb(var(--color-text-muted)) rgba(0,0,0,0) rgba(0,0,0,0);border-style:solid;border-width:5px 5px 2.5px}.ng-dropdown-panel{z-index:2050 !important;background-color:rgb(var(--color-background));border:1px solid rgb(var(--color-secondary-600));border-radius:.5rem;box-shadow:none;left:0}.ng-dropdown-panel.ng-select-top{bottom:100%;border-top-right-radius:.5rem;border-top-left-radius:.5rem;border-bottom-color:rgb(var(--color-secondary-600));margin-bottom:-1px}.ng-dropdown-panel.ng-select-top .ng-dropdown-panel-items .ng-option:first-child{border-top-right-radius:.5rem;border-top-left-radius:.5rem}.ng-dropdown-panel.ng-select-right{left:100%;top:0;border-top-right-radius:.5rem;border-bottom-right-radius:.5rem;border-bottom-left-radius:.5rem;border-bottom-color:rgb(var(--color-secondary-600));margin-bottom:-1px}.ng-dropdown-panel.ng-select-right .ng-dropdown-panel-items .ng-option:first-child{border-top-right-radius:.5rem}.ng-dropdown-panel.ng-select-bottom{top:100%;border-bottom-right-radius:.5rem;border-bottom-left-radius:.5rem;border-top-color:rgb(var(--color-secondary-600));margin-top:-1px}.ng-dropdown-panel.ng-select-bottom .ng-dropdown-panel-items .ng-option:last-child{border-bottom-right-radius:.5rem;border-bottom-left-radius:.5rem}.ng-dropdown-panel.ng-select-left{left:-100%;top:0;border-top-left-radius:.5rem;border-bottom-right-radius:.5rem;border-bottom-left-radius:.5rem;border-bottom-color:rgb(var(--color-secondary-600));margin-bottom:-1px}.ng-dropdown-panel.ng-select-left .ng-dropdown-panel-items .ng-option:first-child{border-top-left-radius:.5rem}.ng-dropdown-panel .ng-dropdown-header{border-bottom:1px solid rgb(var(--color-secondary-600));padding:5px 7px}.ng-dropdown-panel .ng-dropdown-footer{border-top:1px solid rgb(var(--color-secondary-600));padding:5px 7px}.ng-dropdown-panel .ng-dropdown-panel-items{border-radius:.5rem;background:rgb(var(--color-background))}.ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup{user-select:none;padding:8px 10px;font-weight:500;color:rgb(var(--color-text-muted));cursor:pointer}.ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup.ng-option-disabled{cursor:default}.ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup.ng-option-marked{background-color:rgb(var(--color-primary-100))}.ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup.ng-option-selected,.ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup.ng-option-selected.ng-option-marked{color:rgb(var(--color-text-muted));background-color:rgba(0,0,0,0);font-weight:600}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option{background-color:rgb(var(--color-background));color:rgb(var(--color-text-main));padding:.375rem .75rem}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected.ng-option-marked{background-color:rgb(var(--color-primary-100))}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected,.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected.ng-option-marked{color:rgb(var(--color-text-main))}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected .ng-option-label,.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected.ng-option-marked .ng-option-label{font-weight:600}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-marked{background-color:rgb(var(--color-primary-100));color:rgb(var(--color-text-main))}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-disabled{color:rgb(var(--color-secondary-300))}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-child{padding-left:22px}[dir=rtl] .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-child{padding-right:22px;padding-left:0}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option .ng-tag-label{font-size:80%;font-weight:400;padding-right:5px}[dir=rtl] .ng-dropdown-panel .ng-dropdown-panel-items .ng-option .ng-tag-label{padding-left:5px;padding-right:0}[dir=rtl] .ng-dropdown-panel{direction:rtl;text-align:right}
.cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}/**
 * Reset styles to be consistent with Bootstrap reset
 * Reassess when Bootstrap is removed and Tailwind preflight is added
*/fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}th {
  text-align: inherit;
  text-align: -webkit-match-parent;
}.bit-popover-arrow{position:absolute;z-index:10;height:1rem;width:1rem;--tw-rotate:45deg;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));border-style:solid;--tw-bg-opacity:1;background-color:rgb(var(--color-background) / var(--tw-bg-opacity, 1))}.bit-popover-right .bit-popover-arrow{left:0.25rem;--tw-translate-x:-50%;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));border-bottom-left-radius:0.125rem;border-bottom-width:1px;border-left-width:1px;border-bottom-color:rgb(var(--color-secondary-300) / var(--tw-border-opacity, 1));--tw-border-opacity:1;border-left-color:rgb(var(--color-secondary-300) / var(--tw-border-opacity, 1))}.bit-popover-left .bit-popover-arrow{right:0.25rem;--tw-translate-x:50%;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));border-top-right-radius:0.125rem;border-right-width:1px;border-top-width:1px;border-right-color:rgb(var(--color-secondary-300) / var(--tw-border-opacity, 1));--tw-border-opacity:1;border-top-color:rgb(var(--color-secondary-300) / var(--tw-border-opacity, 1))}.bit-popover-right-start .bit-popover-arrow,
.bit-popover-left-start .bit-popover-arrow{top:1.5rem;--tw-translate-y:-50%;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.bit-popover-right-center .bit-popover-arrow,
.bit-popover-left-center .bit-popover-arrow{top:50%;--tw-translate-y:-50%;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.bit-popover-right-end .bit-popover-arrow,
.bit-popover-left-end .bit-popover-arrow{bottom:1.5rem;--tw-translate-y:50%;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.bit-popover-below .bit-popover-arrow{top:0.25rem;--tw-translate-y:-50%;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));border-top-left-radius:0.125rem;border-left-width:1px;border-top-width:1px;border-left-color:rgb(var(--color-secondary-300) / var(--tw-border-opacity, 1));--tw-border-opacity:1;border-top-color:rgb(var(--color-secondary-300) / var(--tw-border-opacity, 1))}.bit-popover-above .bit-popover-arrow{bottom:0.25rem;--tw-translate-y:50%;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));border-bottom-right-radius:0.125rem;border-bottom-width:1px;border-right-width:1px;border-bottom-color:rgb(var(--color-secondary-300) / var(--tw-border-opacity, 1));--tw-border-opacity:1;border-right-color:rgb(var(--color-secondary-300) / var(--tw-border-opacity, 1))}.bit-popover-below-start .bit-popover-arrow,
.bit-popover-above-start .bit-popover-arrow{left:1.5rem;--tw-translate-x:-50%;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.bit-popover-below-center .bit-popover-arrow,
.bit-popover-above-center .bit-popover-arrow{left:50%;--tw-translate-x:-50%;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.bit-popover-below-end .bit-popover-arrow,
.bit-popover-above-end .bit-popover-arrow{right:1.5rem;--tw-translate-x:50%;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}:root {
  --bit-toast-width: 19rem;
  --bit-toast-width-full: 96%;
  --bit-toast-top: 4.3rem;
}/* based on angular-toastr css https://github.com/Foxandxss/angular-toastr/blob/cb508fe6801d6b288d3afc525bb40fee1b101650/dist/angular-toastr.css *//* position */.toast-center-center {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}.toast-top-center {
  top: 0;
  right: 0;
  width: 100%;
}.toast-bottom-center {
  bottom: 0;
  right: 0;
  width: 100%;
}.toast-top-full-width {
  top: 0;
  right: 0;
  width: 100%;
}.toast-bottom-full-width {
  bottom: 0;
  right: 0;
  width: 100%;
}.toast-top-left {
  top: 12px;
  left: 12px;
}.toast-top-right {
  top: 12px;
  right: 12px;
}.toast-bottom-right {
  right: 12px;
  bottom: 12px;
}.toast-bottom-left {
  bottom: 12px;
  left: 12px;
}/* toast styles */.toast-title {
  font-weight: bold;
}.toast-message {
  word-wrap: break-word;
}.toast-message a,
.toast-message label {
  color: #FFFFFF;
}.toast-message a:hover {
  color: #CCCCCC;
  text-decoration: none;
}.toast-close-button {
  position: relative;
  right: -0.3em;
  top: -0.3em;
  float: right;
  font-size: 20px;
  font-weight: bold;
  color: #FFFFFF;
  text-shadow: 0 1px 0 #ffffff;
  /* opacity: 0.8; */
}.toast-close-button:hover,
.toast-close-button:focus {
  color: #000000;
  text-decoration: none;
  cursor: pointer;
  opacity: 0.4;
}/*Additional properties for button version
 iOS requires the button element instead of an anchor tag.
 If you want the anchor version, it requires `href="#"`.*/button.toast-close-button {
  padding: 0;
  cursor: pointer;
  background: transparent;
  border: 0;
}.toast-container {
  pointer-events: none;
  position: fixed;
  z-index: 999999;
}.toast-container * {
  box-sizing: border-box;
}.toast-container .ngx-toastr {
  position: relative;
  overflow: hidden;
  margin: 0 0 6px;
  padding: 15px 15px 15px 50px;
  width: 300px;
  border-radius: 3px 3px 3px 3px;
  background-position: 15px center;
  background-repeat: no-repeat;
  background-size: 24px;
  box-shadow: 0 0 12px #999999;
  color: #FFFFFF;
}.toast-container .ngx-toastr:hover {
  box-shadow: 0 0 12px #000000;
  opacity: 1;
  cursor: pointer;
}/* https://github.com/FortAwesome/Font-Awesome-Pro/blob/master/advanced-options/raw-svg/regular/info-circle.svg */.toast-info {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA1MTIgNTEyJyB3aWR0aD0nNTEyJyBoZWlnaHQ9JzUxMic+PHBhdGggZmlsbD0ncmdiKDI1NSwyNTUsMjU1KScgZD0nTTI1NiA4QzExOS4wNDMgOCA4IDExOS4wODMgOCAyNTZjMCAxMzYuOTk3IDExMS4wNDMgMjQ4IDI0OCAyNDhzMjQ4LTExMS4wMDMgMjQ4LTI0OEM1MDQgMTE5LjA4MyAzOTIuOTU3IDggMjU2IDh6bTAgMTEwYzIzLjE5NiAwIDQyIDE4LjgwNCA0MiA0MnMtMTguODA0IDQyLTQyIDQyLTQyLTE4LjgwNC00Mi00MiAxOC44MDQtNDIgNDItNDJ6bTU2IDI1NGMwIDYuNjI3LTUuMzczIDEyLTEyIDEyaC04OGMtNi42MjcgMC0xMi01LjM3My0xMi0xMnYtMjRjMC02LjYyNyA1LjM3My0xMiAxMi0xMmgxMnYtNjRoLTEyYy02LjYyNyAwLTEyLTUuMzczLTEyLTEydi0yNGMwLTYuNjI3IDUuMzczLTEyIDEyLTEyaDY0YzYuNjI3IDAgMTIgNS4zNzMgMTIgMTJ2MTAwaDEyYzYuNjI3IDAgMTIgNS4zNzMgMTIgMTJ2MjR6Jy8+PC9zdmc+);
}/* https://github.com/FortAwesome/Font-Awesome-Pro/blob/master/advanced-options/raw-svg/regular/times-circle.svg */.toast-error {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA1MTIgNTEyJyB3aWR0aD0nNTEyJyBoZWlnaHQ9JzUxMic+PHBhdGggZmlsbD0ncmdiKDI1NSwyNTUsMjU1KScgZD0nTTI1NiA4QzExOSA4IDggMTE5IDggMjU2czExMSAyNDggMjQ4IDI0OCAyNDgtMTExIDI0OC0yNDhTMzkzIDggMjU2IDh6bTEyMS42IDMxMy4xYzQuNyA0LjcgNC43IDEyLjMgMCAxN0wzMzggMzc3LjZjLTQuNyA0LjctMTIuMyA0LjctMTcgMEwyNTYgMzEybC02NS4xIDY1LjZjLTQuNyA0LjctMTIuMyA0LjctMTcgMEwxMzQuNCAzMzhjLTQuNy00LjctNC43LTEyLjMgMC0xN2w2NS42LTY1LTY1LjYtNjUuMWMtNC43LTQuNy00LjctMTIuMyAwLTE3bDM5LjYtMzkuNmM0LjctNC43IDEyLjMtNC43IDE3IDBsNjUgNjUuNyA2NS4xLTY1LjZjNC43LTQuNyAxMi4zLTQuNyAxNyAwbDM5LjYgMzkuNmM0LjcgNC43IDQuNyAxMi4zIDAgMTdMMzEyIDI1Nmw2NS42IDY1LjF6Jy8+PC9zdmc+);
}/* https://github.com/FortAwesome/Font-Awesome-Pro/blob/master/advanced-options/raw-svg/regular/check.svg */.toast-success {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA1MTIgNTEyJyB3aWR0aD0nNTEyJyBoZWlnaHQ9JzUxMic+PHBhdGggZmlsbD0ncmdiKDI1NSwyNTUsMjU1KScgZD0nTTE3My44OTggNDM5LjQwNGwtMTY2LjQtMTY2LjRjLTkuOTk3LTkuOTk3LTkuOTk3LTI2LjIwNiAwLTM2LjIwNGwzNi4yMDMtMzYuMjA0YzkuOTk3LTkuOTk4IDI2LjIwNy05Ljk5OCAzNi4yMDQgMEwxOTIgMzEyLjY5IDQzMi4wOTUgNzIuNTk2YzkuOTk3LTkuOTk3IDI2LjIwNy05Ljk5NyAzNi4yMDQgMGwzNi4yMDMgMzYuMjA0YzkuOTk3IDkuOTk3IDkuOTk3IDI2LjIwNiAwIDM2LjIwNGwtMjk0LjQgMjk0LjQwMWMtOS45OTggOS45OTctMjYuMjA3IDkuOTk3LTM2LjIwNC0uMDAxeicvPjwvc3ZnPg==);
}/* https://github.com/FortAwesome/Font-Awesome-Pro/blob/master/advanced-options/raw-svg/regular/exclamation-triangle.svg */.toast-warning {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA1NzYgNTEyJyB3aWR0aD0nNTc2JyBoZWlnaHQ9JzUxMic+PHBhdGggZmlsbD0ncmdiKDI1NSwyNTUsMjU1KScgZD0nTTU2OS41MTcgNDQwLjAxM0M1ODcuOTc1IDQ3Mi4wMDcgNTY0LjgwNiA1MTIgNTI3Ljk0IDUxMkg0OC4wNTRjLTM2LjkzNyAwLTU5Ljk5OS00MC4wNTUtNDEuNTc3LTcxLjk4N0wyNDYuNDIzIDIzLjk4NWMxOC40NjctMzIuMDA5IDY0LjcyLTMxLjk1MSA4My4xNTQgMGwyMzkuOTQgNDE2LjAyOHpNMjg4IDM1NGMtMjUuNDA1IDAtNDYgMjAuNTk1LTQ2IDQ2czIwLjU5NSA0NiA0NiA0NiA0Ni0yMC41OTUgNDYtNDYtMjAuNTk1LTQ2LTQ2LTQ2em0tNDMuNjczLTE2NS4zNDZsNy40MTggMTM2Yy4zNDcgNi4zNjQgNS42MDkgMTEuMzQ2IDExLjk4MiAxMS4zNDZoNDguNTQ2YzYuMzczIDAgMTEuNjM1LTQuOTgyIDExLjk4Mi0xMS4zNDZsNy40MTgtMTM2Yy4zNzUtNi44NzQtNS4wOTgtMTIuNjU0LTExLjk4Mi0xMi42NTRoLTYzLjM4M2MtNi44ODQgMC0xMi4zNTYgNS43OC0xMS45ODEgMTIuNjU0eicvPjwvc3ZnPg==);
}.toast-container.toast-top-center .ngx-toastr,
.toast-container.toast-bottom-center .ngx-toastr {
  width: 300px;
  margin-left: auto;
  margin-right: auto;
}.toast-container.toast-top-full-width .ngx-toastr,
.toast-container.toast-bottom-full-width .ngx-toastr {
  width: 96%;
  margin-left: auto;
  margin-right: auto;
}.ngx-toastr {
  background-color: #030303;
  pointer-events: auto;
}.toast-success {
  background-color: #51A351;
}.toast-error {
  background-color: #BD362F;
}.toast-info {
  background-color: #2F96B4;
}.toast-warning {
  background-color: #F89406;
}.toast-progress {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 4px;
  background-color: #000000;
  opacity: 0.4;
}/* Responsive Design */@media all and (max-width: 240px) {
  .toast-container .ngx-toastr.div {
    padding: 8px 8px 8px 50px;
    width: 11em;
  }
  .toast-container .toast-close-button {
    right: -0.2em;
    top: -0.2em;
  }
}@media all and (min-width: 241px) and (max-width: 480px) {
  .toast-container .ngx-toastr.div {
    padding: 8px 8px 8px 50px;
    width: 18em;
  }
  .toast-container .toast-close-button {
    right: -0.2em;
    top: -0.2em;
  }
}@media all and (min-width: 481px) and (max-width: 768px) {
  .toast-container .ngx-toastr.div {
    padding: 15px 15px 15px 50px;
    width: 25em;
  }
}/* Override all default styles from `ngx-toaster` */.toast-container .ngx-toastr {
  all: unset;
  display: block;
  width: var(--bit-toast-width);

  /* Needed to make hover states work in Electron, since the toast appears in the draggable region. */
  -webkit-app-region: no-drag;
}/* Disable hover styles */.toast-container .ngx-toastr:hover {
  box-shadow: none;
}.toast-container.toast-bottom-full-width .ngx-toastr {
  width: var(--bit-toast-width-full);
  margin-left: auto;
  margin-right: auto;
}.toast-container.toast-top-full-width {
  top: var(--bit-toast-top);
}/**
 * Tailwind doesn't have a good way to style search-cancel-button.
 */bit-search input[type="search"]::-webkit-search-cancel-button {
  -webkit-appearance: none;
  appearance: none;
  height: 21px;
  width: 21px;
  margin: 0;
  cursor: pointer;
  background-repeat: no-repeat;
  mask-image: url(../popup/images/close-button.svg);
  -webkit-mask-image: url(../popup/images/close-button.svg);
  background-color: rgba(var(--color-text-muted));
}bit-search input[type="search"]::-webkit-search-cancel-button:hover {
  background-color: rgba(var(--color-text-main));
}:root {
  --color-transparent-hover: rgb(0 0 0 / 0.02);
  --color-shadow: 168 179 200;

  --color-background: 255 255 255;
  --color-background-alt: 243 246 249;
  --color-background-alt2: 23 92 219;
  --color-background-alt3: 26 65 172;
  --color-background-alt4: 2 15 102;

  --color-primary-100: 219 229 246;
  --color-primary-300: 121 161 233;
  --color-primary-600: 23 93 220;
  --color-primary-700: 26 65 172;

  --color-secondary-100: 230 233 239;
  --color-secondary-300: 168 179 200;
  --color-secondary-500: 90 109 145;
  --color-secondary-600: 83 99 131;
  --color-secondary-700: 63 75 99;

  --color-info-100: 219 229 246;
  --color-info-600: 121 161 233;
  --color-info-700: 13 36 123;

  --color-warning-100: 255 244 212;
  --color-warning-600: 255 191 0;
  --color-warning-700: 142 64 0;

  --color-danger-100: 255 236 239;
  --color-danger-600: 203 38 58;
  --color-danger-700: 149 27 42;

  --color-success-100: 213 243 216;
  --color-success-600: 12 128 24;
  --color-success-700: 8 81 15;

  --color-notification-100: 255 225 247;
  --color-notification-600: 192 17 118;

  --color-art-primary: 2 15 102;
  --color-art-accent: 44 221 223;

  --color-text-main: 27 32 41;
  --color-text-muted: 96 109 145;
  --color-text-contrast: 255 255 255;
  --color-text-alt2: 255 255 255;
  --color-text-code: 192 17 118;

  --color-marketing-logo: 23 93 220;

  --tw-ring-offset-color: #ffffff;
}.theme_light {
  /* should be left empty as white is the default */
}.theme_dark {
  --color-transparent-hover: rgb(255 255 255 / 0.02);
  --color-shadow: 0 0 0;

  --color-background: 32 39 51;
  --color-background-alt: 18 26 39;
  --color-background-alt2: 47 52 61;
  --color-background-alt3: 48 57 70;
  --color-background-alt4: 18 26 39;

  --color-primary-100: 26 39 78;
  --color-primary-300: 26 65 172;
  --color-primary-600: 101 171 255;
  --color-primary-700: 170 195 239;

  --color-secondary-100: 48 57 70;
  --color-secondary-300: 82 91 106;
  --color-secondary-500: 121 128 142;
  --color-secondary-600: 143 152 166;
  --color-secondary-700: 158 167 181;

  --color-success-100: 8 81 15;
  --color-success-600: 107 241 120;
  --color-success-700: 213 243 216;

  --color-danger-100: 149 27 42;
  --color-danger-600: 255 78 99;
  --color-danger-700: 255 236 239;

  --color-warning-100: 142 64 0;
  --color-warning-600: 255 191 0;
  --color-warning-700: 255 244 212;

  --color-info-100: 13 36 123;
  --color-info-600: 121 161 233;
  --color-info-700: 219 229 246;

  --color-notification-100: 117 37 83;
  --color-notification-600: 255 143 208;

  --color-art-primary: 243 246 249;
  --color-art-accent: 44 221 233;

  --color-text-main: 243 246 249;
  --color-text-muted: 136 152 181;
  --color-text-contrast: 18 26 39;
  --color-text-alt2: 255 255 255;
  --color-text-code: 255 143 208;

  --color-marketing-logo: 255 255 255;

  --tw-ring-offset-color: #1f242e;
}/**
 * tw-break-words does not work with table cells:
 * https://github.com/tailwindlabs/tailwindcss/issues/835
 */td.tw-break-words {
  overflow-wrap: anywhere;
}/**
 * tw-list-none hides summary arrow in Firefox & Chrome but not Safari:
 * https://github.com/tailwindlabs/tailwindcss/issues/924#issuecomment-915509785
 */summary.tw-list-none::marker,
summary.tw-list-none::-webkit-details-marker {
  display: none;
}/**
 * Arbitrary values can't be used with `text-align`:
 * https://github.com/tailwindlabs/tailwindcss/issues/802#issuecomment-849013311
 */.tw-text-unset {
  text-align: unset;
}/**
 * Bootstrap uses z-index: 1050 for modals, dialogs and drag-and-drop previews should appear above them.
 * When bootstrap is removed, test if these styles are still needed and that overlays display properly over other content.
 * CL-483
 */.cdk-drag-preview,
.cdk-overlay-container,
.cdk-global-overlay-wrapper,
.cdk-overlay-connected-position-bounding-box,
.cdk-overlay-backdrop,
.cdk-overlay-pane {
  z-index: 2000 !important;
}.cdk-global-scrollblock {
  position: relative;
  height: 100%;
  overflow: hidden;
}*, ::before, ::after{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;}* --tw-ring-offset-color,::before --tw-ring-offset-color,::after --tw-ring-offset-color{-d-e-f-a-u-l-t:rgb(var(--color-background) / <alpha-value>);alt:rgb(var(--color-background-alt) / <alpha-value>);alt2:rgb(var(--color-background-alt2) / <alpha-value>);alt3:rgb(var(--color-background-alt3) / <alpha-value>);alt4:rgb(var(--color-background-alt4) / <alpha-value>)}*, ::before, ::after{--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;}::backdrop --tw-ring-offset-color{-d-e-f-a-u-l-t:rgb(var(--color-background) / <alpha-value>);alt:rgb(var(--color-background-alt) / <alpha-value>);alt2:rgb(var(--color-background-alt2) / <alpha-value>);alt3:rgb(var(--color-background-alt3) / <alpha-value>);alt4:rgb(var(--color-background-alt4) / <alpha-value>)}::backdrop{--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }.tw-container{width:100%}@media (min-width: 640px){.tw-container{max-width:640px}}@media (min-width: 768px){.tw-container{max-width:768px}}@media (min-width: 1024px){.tw-container{max-width:1024px}}@media (min-width: 1280px){.tw-container{max-width:1280px}}@media (min-width: 1536px){.tw-container{max-width:1536px}}/** Safari Support */html.browser_safari .tw-styled-scrollbar::-webkit-scrollbar{overflow:auto}html.browser_safari .tw-styled-scrollbar::-webkit-scrollbar-thumb{border-radius:0.5rem;border-width:4px;border-style:solid;border-color:transparent;--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-500) / var(--tw-bg-opacity, 1));background-clip:content-box}html.browser_safari .tw-styled-scrollbar::-webkit-scrollbar-track{--tw-bg-opacity:1;background-color:rgb(var(--color-background-alt) / var(--tw-bg-opacity, 1))}html.browser_safari .tw-styled-scrollbar::-webkit-scrollbar-thumb:hover{--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-600) / var(--tw-bg-opacity, 1))}/* FireFox & Chrome support */html:not(.browser_safari) .tw-styled-scrollbar {
    scrollbar-color: rgb(var(--color-secondary-500)) rgb(var(--color-background-alt));
  }.tw-sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border-width:0}.tw-pointer-events-none{pointer-events:none}.tw-pointer-events-auto{pointer-events:auto}.tw-invisible{visibility:hidden}.tw-fixed{position:fixed}.tw-absolute{position:absolute}.tw-relative{position:relative}.tw-sticky{position:sticky}.tw-inset-0{inset:0px}.tw-inset-y-0{top:0px;bottom:0px}.tw-bottom-0{bottom:0px}.tw-bottom-\[-1px\]{bottom:-1px}.tw-left-0{left:0px}.tw-left-1\/2{left:50%}.tw-left-2{left:0.5rem}.tw-left-\[calc\(50\%\+5px\)\]{left:calc(50% + 5px)}.tw-right-\[1px\]{right:1px}.tw-top-0{top:0px}.tw-top-1\.5{top:0.375rem}.tw-top-1\/2{top:50%}.tw-top-\[1px\]{top:1px}.tw-top-\[3px\]{top:3px}.tw-z-10{z-index:10}.tw-z-20{z-index:20}.tw-z-30{z-index:30}.tw-z-50{z-index:50}.tw-col-span-12{grid-column:span 12 / span 12}.tw-col-span-6{grid-column:span 6 / span 6}.\!tw-m-0{margin:0px !important}.-tw-m-1\.5{margin:-0.375rem}.-tw-m-6{margin:-1.5rem}.tw-m-0{margin:0px}.tw-m-auto{margin:auto}.-tw-mx-0\.5{margin-left:-0.125rem;margin-right:-0.125rem}.-tw-mx-3{margin-left:-0.75rem;margin-right:-0.75rem}.tw-mx-0\.5{margin-left:0.125rem;margin-right:0.125rem}.tw-mx-1{margin-left:0.25rem;margin-right:0.25rem}.tw-mx-1\.5{margin-left:0.375rem;margin-right:0.375rem}.tw-mx-6{margin-left:1.5rem;margin-right:1.5rem}.tw-mx-auto{margin-left:auto;margin-right:auto}.tw-my-2{margin-top:0.5rem;margin-bottom:0.5rem}.tw-my-4{margin-top:1rem;margin-bottom:1rem}.tw-my-6{margin-top:1.5rem;margin-bottom:1.5rem}.\!tw-mb-0{margin-bottom:0px !important}.\!tw-mb-0\.5{margin-bottom:0.125rem !important}.\!tw-mb-1{margin-bottom:0.25rem !important}.\!tw-mb-5{margin-bottom:1.25rem !important}.\!tw-me-2{margin-inline-end:0.5rem !important}.-tw-mb-0\.5{margin-bottom:-0.125rem}.-tw-ml-1{margin-left:-0.25rem}.-tw-ms-3{margin-inline-start:-0.75rem}.tw--mb-px{margin-bottom:-1px}.tw-mb-0{margin-bottom:0px}.tw-mb-1{margin-bottom:0.25rem}.tw-mb-1\.5{margin-bottom:0.375rem}.tw-mb-12{margin-bottom:3rem}.tw-mb-2{margin-bottom:0.5rem}.tw-mb-3{margin-bottom:0.75rem}.tw-mb-4{margin-bottom:1rem}.tw-mb-5{margin-bottom:1.25rem}.tw-mb-6{margin-bottom:1.5rem}.tw-me-1{margin-inline-end:0.25rem}.tw-me-1\.5{margin-inline-end:0.375rem}.tw-me-2{margin-inline-end:0.5rem}.tw-me-3\.5{margin-inline-end:0.875rem}.tw-me-4{margin-inline-end:1rem}.tw-ml-1{margin-left:0.25rem}.tw-ml-1\.5{margin-left:0.375rem}.tw-ml-2{margin-left:0.5rem}.tw-ml-5{margin-left:1.25rem}.tw-ml-8{margin-left:2rem}.tw-ml-auto{margin-left:auto}.tw-mr-1{margin-right:0.25rem}.tw-mr-2{margin-right:0.5rem}.tw-mr-\[5px\]{margin-right:5px}.tw-ms-0\.5{margin-inline-start:0.125rem}.tw-ms-16{margin-inline-start:4rem}.tw-ms-2{margin-inline-start:0.5rem}.tw-ms-7{margin-inline-start:1.75rem}.tw-ms-auto{margin-inline-start:auto}.tw-mt-0{margin-top:0px}.tw-mt-0\.5{margin-top:0.125rem}.tw-mt-1{margin-top:0.25rem}.tw-mt-10{margin-top:2.5rem}.tw-mt-2{margin-top:0.5rem}.tw-mt-3{margin-top:0.75rem}.tw-mt-4{margin-top:1rem}.tw-mt-5{margin-top:1.25rem}.tw-mt-56{margin-top:14rem}.tw-mt-6{margin-top:1.5rem}.tw-mt-8{margin-top:2rem}.tw-mt-auto{margin-top:auto}.tw-mt-px{margin-top:1px}.tw-box-border{box-sizing:border-box}.tw-box-content{box-sizing:content-box}.tw-line-clamp-2{overflow:hidden;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2}.tw-block{display:block}.tw-inline-block{display:inline-block}.tw-inline{display:inline}.tw-flex{display:flex}.tw-inline-flex{display:inline-flex}.tw-grid{display:grid}.tw-hidden{display:none}.tw-size-2\.5{width:0.625rem;height:0.625rem}.tw-size-6{width:1.5rem;height:1.5rem}.tw-size-7{width:1.75rem;height:1.75rem}.tw-size-9{width:2.25rem;height:2.25rem}.tw-size-\[15px\]{width:15px;height:15px}.tw-size-full{width:100%;height:100%}.tw-h-1{height:0.25rem}.tw-h-10{height:2.5rem}.tw-h-16{height:4rem}.tw-h-24{height:6rem}.tw-h-4{height:1rem}.tw-h-40{height:10rem}.tw-h-6{height:1.5rem}.tw-h-7{height:1.75rem}.tw-h-9{height:2.25rem}.tw-h-\[1\.12rem\]{height:1.12rem}.tw-h-\[500px\]{height:500px}.tw-h-\[52px\]{height:52px}.tw-h-\[640px\]{height:640px}.tw-h-auto{height:auto}.tw-h-full{height:100%}.tw-h-px{height:1px}.tw-h-screen{height:100vh}.tw-max-h-40{max-height:10rem}.tw-max-h-80{max-height:20rem}.tw-max-h-\[90vh\]{max-height:90vh}.tw-max-h-screen{max-height:100vh}.tw-min-h-11{min-height:2.75rem}.tw-min-h-6{min-height:1.5rem}.tw-min-h-60{min-height:15rem}.tw-min-h-9{min-height:2.25rem}.tw-min-h-\[1\.85rem\]{min-height:1.85rem}.tw-min-h-full{min-height:100%}.tw-min-h-screen{min-height:100vh}.tw-w-0{width:0px}.tw-w-10{width:2.5rem}.tw-w-16{width:4rem}.tw-w-24{width:6rem}.tw-w-32{width:8rem}.tw-w-40{width:10rem}.tw-w-6{width:1.5rem}.tw-w-60{width:15rem}.tw-w-7{width:1.75rem}.tw-w-72{width:18rem}.tw-w-96{width:24rem}.tw-w-\[1\.12rem\]{width:1.12rem}.tw-w-\[128px\]{width:128px}.tw-w-\[23rem\]{width:23rem}.tw-w-\[3\.75rem\]{width:3.75rem}.tw-w-\[380px\]{width:380px}.tw-w-\[43\%\]{width:43%}.tw-w-\[480px\]{width:480px}.tw-w-\[600px\]{width:600px}.tw-w-\[900px\]{width:900px}.tw-w-full{width:100%}.tw-w-screen{width:100vw}.tw-min-w-0{min-width:0px}.tw-min-w-10{min-width:2.5rem}.tw-min-w-16{min-width:4rem}.tw-min-w-3{min-width:0.75rem}.tw-min-w-32{min-width:8rem}.tw-min-w-\[--bit-toast-width\]{min-width:var(--bit-toast-width)}.tw-min-w-max{min-width:-moz-max-content;min-width:max-content}.tw-max-w-24{max-width:6rem}.tw-max-w-32{max-width:8rem}.tw-max-w-3xl{max-width:48rem}.tw-max-w-40{max-width:10rem}.tw-max-w-4xl{max-width:56rem}.tw-max-w-52{max-width:13rem}.tw-max-w-64{max-width:16rem}.tw-max-w-80{max-width:20rem}.tw-max-w-90vw{max-width:90vw}.tw-max-w-\[--bit-toast-width\]{max-width:var(--bit-toast-width)}.tw-max-w-\[500px\]{max-width:500px}.tw-max-w-fit{max-width:-moz-fit-content;max-width:fit-content}.tw-max-w-full{max-width:100%}.tw-max-w-md{max-width:28rem}.tw-max-w-screen-sm{max-width:640px}.tw-max-w-sm{max-width:24rem}.tw-max-w-xl{max-width:36rem}.tw-flex-1{flex:1 1 0%}.tw-flex-initial{flex:0 1 auto}.tw-flex-none{flex:none}.tw-shrink{flex-shrink:1}.tw-shrink-0{flex-shrink:0}.tw-flex-grow{flex-grow:1}.tw-grow{flex-grow:1}.tw-table-auto{table-layout:auto}.tw-table-fixed{table-layout:fixed}.tw-border-collapse{border-collapse:collapse}.-tw-translate-y-\[0\.675rem\]{--tw-translate-y:-0.675rem;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.tw--translate-x-1\/2{--tw-translate-x:-50%;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.tw--translate-y-1\/2{--tw-translate-y:-50%;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.tw-scale-100{--tw-scale-x:1;--tw-scale-y:1;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.tw-transform{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.tw-cursor-auto{cursor:auto}.tw-cursor-default{cursor:default}.tw-cursor-move{cursor:move}.tw-cursor-not-allowed{cursor:not-allowed}.tw-cursor-pointer{cursor:pointer}.tw-cursor-text{cursor:text}.tw-select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.tw-select-all{-webkit-user-select:all;-moz-user-select:all;user-select:all}.tw-resize-none{resize:none}.tw-list-disc{list-style-type:disc}.tw-list-none{list-style-type:none}.tw-appearance-none{-webkit-appearance:none;-moz-appearance:none;appearance:none}.tw-grid-cols-12{grid-template-columns:repeat(12, minmax(0, 1fr))}.tw-grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr))}.tw-flex-row{flex-direction:row}.tw-flex-col{flex-direction:column}.tw-flex-wrap{flex-wrap:wrap}.tw-items-start{align-items:flex-start}.tw-items-end{align-items:flex-end}.tw-items-center{align-items:center}.tw-items-baseline{align-items:baseline}.tw-justify-start{justify-content:flex-start}.tw-justify-end{justify-content:flex-end}.tw-justify-center{justify-content:center}.tw-justify-between{justify-content:space-between}.tw-justify-around{justify-content:space-around}.tw-gap-1{gap:0.25rem}.tw-gap-1\.5{gap:0.375rem}.tw-gap-2{gap:0.5rem}.tw-gap-3{gap:0.75rem}.tw-gap-4{gap:1rem}.tw-gap-6{gap:1.5rem}.tw-gap-8{gap:2rem}.tw-space-x-2 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(0.5rem * var(--tw-space-x-reverse));margin-left:calc(0.5rem * calc(1 - var(--tw-space-x-reverse)))}.tw-space-y-2 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(0.5rem * var(--tw-space-y-reverse))}.tw-space-y-3 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(0.75rem * var(--tw-space-y-reverse))}.tw-self-end{align-self:flex-end}.tw-self-center{align-self:center}.tw-overflow-auto{overflow:auto}.tw-overflow-hidden{overflow:hidden}.tw-overflow-visible{overflow:visible}.tw-overflow-y-auto{overflow-y:auto}.tw-overflow-y-hidden{overflow-y:hidden}.tw-overscroll-none{overscroll-behavior:none}.tw-truncate{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.tw-text-ellipsis{text-overflow:ellipsis}.tw-hyphens-auto{-webkit-hyphens:auto;hyphens:auto}.tw-whitespace-normal{white-space:normal}.tw-whitespace-nowrap{white-space:nowrap}.tw-whitespace-pre-wrap{white-space:pre-wrap}.tw-text-wrap{text-wrap:wrap}.tw-break-words{overflow-wrap:break-word}.tw-break-all{word-break:break-all}.tw-rounded{border-radius:0.25rem}.tw-rounded-2xl{border-radius:1rem}.tw-rounded-3xl{border-radius:1.5rem}.tw-rounded-full{border-radius:9999px}.tw-rounded-lg{border-radius:0.5rem}.tw-rounded-md{border-radius:0.375rem}.tw-rounded-xl{border-radius:0.75rem}.\!tw-rounded-e-lg{border-start-end-radius:0.5rem !important;border-end-end-radius:0.5rem !important}.\!tw-rounded-s-lg{border-start-start-radius:0.5rem !important;border-end-start-radius:0.5rem !important}.tw-rounded-b-md{border-bottom-right-radius:0.375rem;border-bottom-left-radius:0.375rem}.tw-rounded-b-none{border-bottom-right-radius:0px;border-bottom-left-radius:0px}.tw-rounded-e-lg{border-start-end-radius:0.5rem;border-end-end-radius:0.5rem}.tw-rounded-s-lg{border-start-start-radius:0.5rem;border-end-start-radius:0.5rem}.tw-rounded-t-lg{border-top-left-radius:0.5rem;border-top-right-radius:0.5rem}.tw-rounded-t-none{border-top-left-radius:0px;border-top-right-radius:0px}.tw-border{border-width:1px}.tw-border-0{border-width:0px}.tw-border-2{border-width:2px}.tw-border-\[0\.5px\]{border-width:0.5px}.tw-border-x{border-left-width:1px;border-right-width:1px}.tw-border-x-0{border-left-width:0px;border-right-width:0px}.tw-border-y{border-top-width:1px;border-bottom-width:1px}.tw-border-b{border-bottom-width:1px}.tw-border-b-2{border-bottom-width:2px}.tw-border-l{border-left-width:1px}.tw-border-l-0{border-left-width:0px}.tw-border-r{border-right-width:1px}.tw-border-r-0{border-right-width:0px}.tw-border-s{border-inline-start-width:1px}.tw-border-t{border-top-width:1px}.tw-border-t-0{border-top-width:0px}.tw-border-t-4{border-top-width:4px}.\!tw-border-solid{border-style:solid !important}.tw-border-solid{border-style:solid}.tw-border-none{border-style:none}.\!tw-border-secondary-700{--tw-border-opacity:1 !important;border-color:rgb(var(--color-secondary-700) / var(--tw-border-opacity, 1)) !important}.tw-border-black{--tw-border-opacity:1;border-color:rgb(0 0 0 / var(--tw-border-opacity, 1))}.tw-border-danger-600{--tw-border-opacity:1;border-color:rgb(var(--color-danger-600) / var(--tw-border-opacity, 1))}.tw-border-danger-700{--tw-border-opacity:1;border-color:rgb(var(--color-danger-700) / var(--tw-border-opacity, 1))}.tw-border-info-700{--tw-border-opacity:1;border-color:rgb(var(--color-info-700) / var(--tw-border-opacity, 1))}.tw-border-notification-600{--tw-border-opacity:1;border-color:rgb(var(--color-notification-600) / var(--tw-border-opacity, 1))}.tw-border-primary-600{--tw-border-opacity:1;border-color:rgb(var(--color-primary-600) / var(--tw-border-opacity, 1))}.tw-border-primary-700{--tw-border-opacity:1;border-color:rgb(var(--color-primary-700) / var(--tw-border-opacity, 1))}.tw-border-secondary-100{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-100) / var(--tw-border-opacity, 1))}.tw-border-secondary-300{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-300) / var(--tw-border-opacity, 1))}.tw-border-secondary-300\/50{border-color:rgb(var(--color-secondary-300) / 0.5)}.tw-border-secondary-500{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-500) / var(--tw-border-opacity, 1))}.tw-border-secondary-600{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-600) / var(--tw-border-opacity, 1))}.tw-border-secondary-700{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-700) / var(--tw-border-opacity, 1))}.tw-border-success-700{--tw-border-opacity:1;border-color:rgb(var(--color-success-700) / var(--tw-border-opacity, 1))}.tw-border-text-muted{--tw-border-opacity:1;border-color:rgb(var(--color-text-muted) / var(--tw-border-opacity, 1))}.tw-border-transparent{border-color:transparent}.tw-border-warning-700{--tw-border-opacity:1;border-color:rgb(var(--color-warning-700) / var(--tw-border-opacity, 1))}.tw-border-x-secondary-300{--tw-border-opacity:1;border-left-color:rgb(var(--color-secondary-300) / var(--tw-border-opacity, 1));border-right-color:rgb(var(--color-secondary-300) / var(--tw-border-opacity, 1))}.tw-border-b-background{--tw-border-opacity:1;border-bottom-color:rgb(var(--color-background) / var(--tw-border-opacity, 1))}.tw-border-b-danger-700{--tw-border-opacity:1;border-bottom-color:rgb(var(--color-danger-700) / var(--tw-border-opacity, 1))}.tw-border-b-info-700{--tw-border-opacity:1;border-bottom-color:rgb(var(--color-info-700) / var(--tw-border-opacity, 1))}.tw-border-b-secondary-300{--tw-border-opacity:1;border-bottom-color:rgb(var(--color-secondary-300) / var(--tw-border-opacity, 1))}.tw-border-b-shadow{--tw-border-opacity:1;border-bottom-color:rgb(var(--color-shadow) / var(--tw-border-opacity, 1))}.tw-border-b-success-700{--tw-border-opacity:1;border-bottom-color:rgb(var(--color-success-700) / var(--tw-border-opacity, 1))}.tw-border-b-transparent{border-bottom-color:transparent}.tw-border-b-warning-700{--tw-border-opacity:1;border-bottom-color:rgb(var(--color-warning-700) / var(--tw-border-opacity, 1))}.tw-border-t-primary-600{--tw-border-opacity:1;border-top-color:rgb(var(--color-primary-600) / var(--tw-border-opacity, 1))}.tw-border-t-secondary-300{--tw-border-opacity:1;border-top-color:rgb(var(--color-secondary-300) / var(--tw-border-opacity, 1))}.tw-border-t-secondary-500{--tw-border-opacity:1;border-top-color:rgb(var(--color-secondary-500) / var(--tw-border-opacity, 1))}.\!tw-bg-background{--tw-bg-opacity:1 !important;background-color:rgb(var(--color-background) / var(--tw-bg-opacity, 1)) !important}.tw-bg-background{--tw-bg-opacity:1;background-color:rgb(var(--color-background) / var(--tw-bg-opacity, 1))}.tw-bg-background-alt{--tw-bg-opacity:1;background-color:rgb(var(--color-background-alt) / var(--tw-bg-opacity, 1))}.tw-bg-background-alt2{--tw-bg-opacity:1;background-color:rgb(var(--color-background-alt2) / var(--tw-bg-opacity, 1))}.tw-bg-background-alt3{--tw-bg-opacity:1;background-color:rgb(var(--color-background-alt3) / var(--tw-bg-opacity, 1))}.tw-bg-background-alt4{--tw-bg-opacity:1;background-color:rgb(var(--color-background-alt4) / var(--tw-bg-opacity, 1))}.tw-bg-black{--tw-bg-opacity:1;background-color:rgb(0 0 0 / var(--tw-bg-opacity, 1))}.tw-bg-danger-100{--tw-bg-opacity:1;background-color:rgb(var(--color-danger-100) / var(--tw-bg-opacity, 1))}.tw-bg-danger-600{--tw-bg-opacity:1;background-color:rgb(var(--color-danger-600) / var(--tw-bg-opacity, 1))}.tw-bg-info-100{--tw-bg-opacity:1;background-color:rgb(var(--color-info-100) / var(--tw-bg-opacity, 1))}.tw-bg-notification-100{--tw-bg-opacity:1;background-color:rgb(var(--color-notification-100) / var(--tw-bg-opacity, 1))}.tw-bg-notification-600{--tw-bg-opacity:1;background-color:rgb(var(--color-notification-600) / var(--tw-bg-opacity, 1))}.tw-bg-primary-100{--tw-bg-opacity:1;background-color:rgb(var(--color-primary-100) / var(--tw-bg-opacity, 1))}.tw-bg-primary-600{--tw-bg-opacity:1;background-color:rgb(var(--color-primary-600) / var(--tw-bg-opacity, 1))}.tw-bg-secondary-100{--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-100) / var(--tw-bg-opacity, 1))}.tw-bg-secondary-300{--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-300) / var(--tw-bg-opacity, 1))}.tw-bg-success-100{--tw-bg-opacity:1;background-color:rgb(var(--color-success-100) / var(--tw-bg-opacity, 1))}.tw-bg-success-600{--tw-bg-opacity:1;background-color:rgb(var(--color-success-600) / var(--tw-bg-opacity, 1))}.tw-bg-text-contrast{--tw-bg-opacity:1;background-color:rgb(var(--color-text-contrast) / var(--tw-bg-opacity, 1))}.tw-bg-text-main\/30{background-color:rgb(var(--color-text-main) / 0.3)}.tw-bg-text-muted{--tw-bg-opacity:1;background-color:rgb(var(--color-text-muted) / var(--tw-bg-opacity, 1))}.tw-bg-transparent{background-color:transparent}.tw-bg-warning-100{--tw-bg-opacity:1;background-color:rgb(var(--color-warning-100) / var(--tw-bg-opacity, 1))}.tw-bg-warning-600{--tw-bg-opacity:1;background-color:rgb(var(--color-warning-600) / var(--tw-bg-opacity, 1))}.tw-bg-opacity-0{--tw-bg-opacity:0}.tw-bg-opacity-30{--tw-bg-opacity:0.3}.tw-bg-opacity-80{--tw-bg-opacity:0.8}.tw-bg-clip-padding{background-clip:padding-box}.tw-fill-art-accent{fill:rgb(var(--color-art-accent) / 1)}.tw-fill-art-primary{fill:rgb(var(--color-art-primary) / 1)}.tw-fill-background{fill:rgb(var(--color-background) / 1)}.tw-fill-danger-600{fill:rgb(var(--color-danger-600) / 1)}.tw-fill-marketing-logo{fill:rgb(var(--color-marketing-logo) / 1)}.tw-fill-none{fill:none}.tw-fill-primary-100{fill:rgb(var(--color-primary-100) / 1)}.tw-fill-primary-600{fill:rgb(var(--color-primary-600) / 1)}.tw-fill-secondary-300{fill:rgb(var(--color-secondary-300) / 1)}.tw-fill-secondary-600{fill:rgb(var(--color-secondary-600) / 1)}.tw-stroke-art-accent{stroke:rgb(var(--color-art-accent) / 1)}.tw-stroke-art-primary{stroke:rgb(var(--color-art-primary) / 1)}.tw-stroke-danger-600{stroke:rgb(var(--color-danger-600) / 1)}.tw-stroke-secondary-600{stroke:rgb(var(--color-secondary-600) / 1)}.tw-stroke-text-main{stroke:rgb(var(--color-text-main) / 1)}.\!tw-p-0{padding:0px !important}.tw-p-0{padding:0px}.tw-p-0\.5{padding:0.125rem}.tw-p-1{padding:0.25rem}.tw-p-10{padding:2.5rem}.tw-p-2{padding:0.5rem}.tw-p-3{padding:0.75rem}.tw-p-4{padding:1rem}.tw-p-6{padding:1.5rem}.\!tw-px-1{padding-left:0.25rem !important;padding-right:0.25rem !important}.\!tw-py-0{padding-top:0px !important;padding-bottom:0px !important}.tw-px-0{padding-left:0px;padding-right:0px}.tw-px-0\.5{padding-left:0.125rem;padding-right:0.125rem}.tw-px-1{padding-left:0.25rem;padding-right:0.25rem}.tw-px-2{padding-left:0.5rem;padding-right:0.5rem}.tw-px-2\.5{padding-left:0.625rem;padding-right:0.625rem}.tw-px-3{padding-left:0.75rem;padding-right:0.75rem}.tw-px-4{padding-left:1rem;padding-right:1rem}.tw-px-5{padding-left:1.25rem;padding-right:1.25rem}.tw-px-6{padding-left:1.5rem;padding-right:1.5rem}.tw-py-0{padding-top:0px;padding-bottom:0px}.tw-py-0\.5{padding-top:0.125rem;padding-bottom:0.125rem}.tw-py-1{padding-top:0.25rem;padding-bottom:0.25rem}.tw-py-1\.5{padding-top:0.375rem;padding-bottom:0.375rem}.tw-py-10{padding-top:2.5rem;padding-bottom:2.5rem}.tw-py-2{padding-top:0.5rem;padding-bottom:0.5rem}.tw-py-3{padding-top:0.75rem;padding-bottom:0.75rem}.tw-py-4{padding-top:1rem;padding-bottom:1rem}.tw-py-5{padding-top:1.25rem;padding-bottom:1.25rem}.\!tw-pt-0{padding-top:0px !important}.tw-pb-0{padding-bottom:0px}.tw-pb-1{padding-bottom:0.25rem}.tw-pb-1\.5{padding-bottom:0.375rem}.tw-pb-4{padding-bottom:1rem}.tw-pb-5{padding-bottom:1.25rem}.tw-pb-6{padding-bottom:1.5rem}.tw-pb-\[2px\]{padding-bottom:2px}.tw-pb-\[3px\]{padding-bottom:3px}.tw-pe-1{padding-inline-end:0.25rem}.tw-pe-2{padding-inline-end:0.5rem}.tw-pe-3{padding-inline-end:0.75rem}.tw-pe-4{padding-inline-end:1rem}.tw-pl-0{padding-left:0px}.tw-pl-1{padding-left:0.25rem}.tw-pl-1\.5{padding-left:0.375rem}.tw-pl-3{padding-left:0.75rem}.tw-pl-4{padding-left:1rem}.tw-pl-5{padding-left:1.25rem}.tw-pl-7{padding-left:1.75rem}.tw-pr-1{padding-right:0.25rem}.tw-pr-2{padding-right:0.5rem}.tw-ps-0{padding-inline-start:0px}.tw-ps-1{padding-inline-start:0.25rem}.tw-ps-2{padding-inline-start:0.5rem}.tw-ps-3{padding-inline-start:0.75rem}.tw-ps-4{padding-inline-start:1rem}.tw-ps-6{padding-inline-start:1.5rem}.tw-ps-8{padding-inline-start:2rem}.tw-ps-9{padding-inline-start:2.25rem}.tw-pt-0{padding-top:0px}.tw-pt-1{padding-top:0.25rem}.tw-pt-12{padding-top:3rem}.tw-pt-2{padding-top:0.5rem}.tw-pt-3{padding-top:0.75rem}.tw-pt-4{padding-top:1rem}.tw-pt-5{padding-top:1.25rem}.tw-pt-6{padding-top:1.5rem}.tw-pt-7{padding-top:1.75rem}.tw-pt-8{padding-top:2rem}.tw-text-left{text-align:left}.tw-text-center{text-align:center}.tw-text-start{text-align:start}.tw-align-baseline{vertical-align:baseline}.tw-align-top{vertical-align:top}.tw-align-middle{vertical-align:middle}.tw-align-bottom{vertical-align:bottom}.tw-align-text-top{vertical-align:text-top}.tw-align-sub{vertical-align:sub}.tw-font-mono{font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace}.\!tw-text-2xl{font-size:1.5rem !important;line-height:2rem !important}.\!tw-text-3xl{font-size:1.75rem !important;line-height:2rem !important}.\!tw-text-base{font-size:1rem !important;line-height:1.5rem !important}.\!tw-text-lg{font-size:1.125rem !important;line-height:1.75rem !important}.\!tw-text-sm{font-size:0.875rem !important;line-height:1.25rem !important}.\!tw-text-xl{font-size:1.25rem !important;line-height:1.75rem !important}.\!tw-text-xs{font-size:0.75rem !important;line-height:1rem !important}.tw-text-2xl{font-size:1.5rem;line-height:2rem}.tw-text-3xl{font-size:1.75rem;line-height:2rem}.tw-text-\[0\.625rem\]{font-size:0.625rem}.tw-text-\[8px\]{font-size:8px}.tw-text-\[length\:inherit\]{font-size:inherit}.tw-text-base{font-size:1rem;line-height:1.5rem}.tw-text-lg{font-size:1.125rem;line-height:1.75rem}.tw-text-sm{font-size:0.875rem;line-height:1.25rem}.tw-text-xl{font-size:1.25rem;line-height:1.75rem}.tw-text-xs{font-size:0.75rem;line-height:1rem}.\!tw-font-semibold{font-weight:600 !important}.tw-font-bold{font-weight:700}.tw-font-medium{font-weight:500}.tw-font-normal{font-weight:400}.tw-font-semibold{font-weight:600}.tw-capitalize{text-transform:capitalize}.tw-italic{font-style:italic}.tw-leading-3{line-height:.75rem}.tw-leading-5{line-height:1.25rem}.tw-leading-7{line-height:1.75rem}.tw-leading-9{line-height:2.25rem}.tw-leading-none{line-height:1}.tw-leading-normal{line-height:1.5}.tw-leading-snug{line-height:1.375}.\!tw-text-\[inherit\]{color:inherit !important}.\!tw-text-alt2{--tw-text-opacity:1 !important;color:rgb(var(--color-text-alt2) / var(--tw-text-opacity, 1)) !important}.\!tw-text-contrast{--tw-text-opacity:1 !important;color:rgb(var(--color-text-contrast) / var(--tw-text-opacity, 1)) !important}.\!tw-text-danger{--tw-text-opacity:1 !important;color:rgb(var(--color-danger-600) / var(--tw-text-opacity, 1)) !important}.\!tw-text-danger-600{--tw-text-opacity:1 !important;color:rgb(var(--color-danger-600) / var(--tw-text-opacity, 1)) !important}.\!tw-text-danger-700{--tw-text-opacity:1 !important;color:rgb(var(--color-danger-700) / var(--tw-text-opacity, 1)) !important}.\!tw-text-info-700{--tw-text-opacity:1 !important;color:rgb(var(--color-info-700) / var(--tw-text-opacity, 1)) !important}.\!tw-text-main{--tw-text-opacity:1 !important;color:rgb(var(--color-text-main) / var(--tw-text-opacity, 1)) !important}.\!tw-text-muted{--tw-text-opacity:1 !important;color:rgb(var(--color-text-muted) / var(--tw-text-opacity, 1)) !important}.\!tw-text-notification-600{--tw-text-opacity:1 !important;color:rgb(var(--color-notification-600) / var(--tw-text-opacity, 1)) !important}.\!tw-text-primary-600{--tw-text-opacity:1 !important;color:rgb(var(--color-primary-600) / var(--tw-text-opacity, 1)) !important}.\!tw-text-primary-700{--tw-text-opacity:1 !important;color:rgb(var(--color-primary-700) / var(--tw-text-opacity, 1)) !important}.\!tw-text-secondary-300{--tw-text-opacity:1 !important;color:rgb(var(--color-secondary-300) / var(--tw-text-opacity, 1)) !important}.\!tw-text-secondary-700{--tw-text-opacity:1 !important;color:rgb(var(--color-secondary-700) / var(--tw-text-opacity, 1)) !important}.\!tw-text-success-700{--tw-text-opacity:1 !important;color:rgb(var(--color-success-700) / var(--tw-text-opacity, 1)) !important}.\!tw-text-warning-700{--tw-text-opacity:1 !important;color:rgb(var(--color-warning-700) / var(--tw-text-opacity, 1)) !important}.tw-text-\[color\:inherit\]{color:inherit}.tw-text-alt2{--tw-text-opacity:1;color:rgb(var(--color-text-alt2) / var(--tw-text-opacity, 1))}.tw-text-code{--tw-text-opacity:1;color:rgb(var(--color-text-code) / var(--tw-text-opacity, 1))}.tw-text-contrast{--tw-text-opacity:1;color:rgb(var(--color-text-contrast) / var(--tw-text-opacity, 1))}.tw-text-danger{--tw-text-opacity:1;color:rgb(var(--color-danger-600) / var(--tw-text-opacity, 1))}.tw-text-info{--tw-text-opacity:1;color:rgb(var(--color-info-600) / var(--tw-text-opacity, 1))}.tw-text-main{--tw-text-opacity:1;color:rgb(var(--color-text-main) / var(--tw-text-opacity, 1))}.tw-text-muted{--tw-text-opacity:1;color:rgb(var(--color-text-muted) / var(--tw-text-opacity, 1))}.tw-text-notification-600{--tw-text-opacity:1;color:rgb(var(--color-notification-600) / var(--tw-text-opacity, 1))}.tw-text-primary-600{--tw-text-opacity:1;color:rgb(var(--color-primary-600) / var(--tw-text-opacity, 1))}.tw-text-success{--tw-text-opacity:1;color:rgb(var(--color-success-600) / var(--tw-text-opacity, 1))}.tw-text-warning{--tw-text-opacity:1;color:rgb(var(--color-warning-600) / var(--tw-text-opacity, 1))}.\!tw-no-underline{text-decoration-line:none !important}.tw-no-underline{text-decoration-line:none}.tw-placeholder-text-muted::-moz-placeholder{--tw-placeholder-opacity:1;color:rgb(var(--color-text-muted) / var(--tw-placeholder-opacity, 1))}.tw-placeholder-text-muted::placeholder{--tw-placeholder-opacity:1;color:rgb(var(--color-text-muted) / var(--tw-placeholder-opacity, 1))}.tw-opacity-0{opacity:0}.tw-shadow-lg{--tw-shadow:0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.tw-shadow-md{--tw-shadow:0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.tw-shadow-xl{--tw-shadow:0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.tw-outline-none{outline:2px solid transparent;outline-offset:2px}.tw-ring{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)}.tw-ring-2{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)}.tw-ring-inset{--tw-ring-inset:inset}.tw-ring-primary-600{--tw-ring-opacity:1;--tw-ring-color:rgb(var(--color-primary-600) / var(--tw-ring-opacity, 1))}.tw-ring-text-alt2{--tw-ring-opacity:1;--tw-ring-color:rgb(var(--color-text-alt2) / var(--tw-ring-opacity, 1))}.tw-ring-text-contrast{--tw-ring-opacity:1;--tw-ring-color:rgb(var(--color-text-contrast) / var(--tw-ring-opacity, 1))}.tw-ring-offset-1{--tw-ring-offset-width:1px}.tw-ring-offset{--tw-ring-offset-color:rgb(var(--color-background) / 1)}.tw-transition{transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.tw-transition-all{transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.tw-transition-colors{transition-property:color, background-color, border-color, text-decoration-color, fill, stroke;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.tw-duration-200{transition-duration:200ms}.tw-ease-in-out{transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1)}.before\:tw-absolute::before{content:var(--tw-content);position:absolute}.before\:-tw-inset-\[2px\]::before{content:var(--tw-content);inset:-2px}.before\:-tw-inset-\[3px\]::before{content:var(--tw-content);inset:-3px}.before\:tw-inset-0::before{content:var(--tw-content);inset:0px}.before\:tw-inset-\[2px\]::before{content:var(--tw-content);inset:2px}.before\:-tw-inset-x-\[0\.1em\]::before{content:var(--tw-content);left:-0.1em;right:-0.1em}.before\:-tw-inset-y-\[0\.125rem\]::before{content:var(--tw-content);top:-0.125rem;bottom:-0.125rem}.before\:-tw-inset-y-\[0\.25rem\]::before{content:var(--tw-content);top:-0.25rem;bottom:-0.25rem}.before\:tw-block::before{content:var(--tw-content);display:block}.before\:tw-rounded-full::before{content:var(--tw-content);border-radius:9999px}.before\:tw-rounded-lg::before{content:var(--tw-content);border-radius:0.5rem}.before\:tw-rounded-md::before{content:var(--tw-content);border-radius:0.375rem}.before\:tw-ring::before{content:var(--tw-content);--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)}.before\:tw-ring-2::before{content:var(--tw-content);--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)}.before\:tw-ring-transparent::before{content:var(--tw-content);--tw-ring-color:transparent}.before\:tw-transition::before{content:var(--tw-content);transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.before\:tw-content-\[\'\'\]::before{--tw-content:'';content:var(--tw-content)}.after\:tw-absolute::after{content:var(--tw-content);position:absolute}.after\:tw-left-0::after{content:var(--tw-content);left:0px}.after\:tw-top-\[50\%\]::after{content:var(--tw-content);top:50%}.after\:tw-h-\[2px\]::after{content:var(--tw-content);height:2px}.after\:tw-w-full::after{content:var(--tw-content);width:100%}.after\:-tw-translate-y-\[50\%\]::after{content:var(--tw-content);--tw-translate-y:-50%;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.after\:tw-bg-secondary-300::after{content:var(--tw-content);--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-300) / var(--tw-bg-opacity, 1))}.after\:tw-content-\[\'\'\]::after{--tw-content:'';content:var(--tw-content)}.first\:-tw-mt-3:first-child{margin-top:-0.75rem}.last\:tw-mb-0:last-child{margin-bottom:0px}.last\:tw-border-0:last-child{border-width:0px}.last\:tw-pe-3:last-child{padding-inline-end:0.75rem}.odd\:tw-bg-secondary-100:nth-child(odd){--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-100) / var(--tw-bg-opacity, 1))}.even\:tw-bg-background:nth-child(even){--tw-bg-opacity:1;background-color:rgb(var(--color-background) / var(--tw-bg-opacity, 1))}.last-of-type\:tw-mb-0:last-of-type{margin-bottom:0px}.checked\:tw-border-2:checked{border-width:2px}.checked\:tw-border-primary-600:checked{--tw-border-opacity:1;border-color:rgb(var(--color-primary-600) / var(--tw-border-opacity, 1))}.checked\:tw-bg-primary-600:checked{--tw-bg-opacity:1;background-color:rgb(var(--color-primary-600) / var(--tw-bg-opacity, 1))}.checked\:tw-bg-text-contrast:checked{--tw-bg-opacity:1;background-color:rgb(var(--color-text-contrast) / var(--tw-bg-opacity, 1))}.checked\:before\:tw-bg-primary-600:checked::before{content:var(--tw-content);--tw-bg-opacity:1;background-color:rgb(var(--color-primary-600) / var(--tw-bg-opacity, 1))}.checked\:before\:tw-bg-text-contrast:checked::before{content:var(--tw-content);--tw-bg-opacity:1;background-color:rgb(var(--color-text-contrast) / var(--tw-bg-opacity, 1))}.checked\:before\:tw-mask-position-\[center\]:checked::before{content:var(--tw-content);-webkit-mask-position:center;mask-position:center}.checked\:before\:tw-mask-repeat-\[no-repeat\]:checked::before{content:var(--tw-content);-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat}.indeterminate\:tw-border-primary-600:indeterminate{--tw-border-opacity:1;border-color:rgb(var(--color-primary-600) / var(--tw-border-opacity, 1))}.indeterminate\:tw-bg-primary-600:indeterminate{--tw-bg-opacity:1;background-color:rgb(var(--color-primary-600) / var(--tw-bg-opacity, 1))}.indeterminate\:before\:tw-bg-text-contrast:indeterminate::before{content:var(--tw-content);--tw-bg-opacity:1;background-color:rgb(var(--color-text-contrast) / var(--tw-bg-opacity, 1))}.indeterminate\:before\:tw-mask-image-\[var\(--indeterminate-mask-image\)\]:indeterminate::before{content:var(--tw-content);-webkit-mask-image:var(--indeterminate-mask-image);mask-image:var(--indeterminate-mask-image)}.indeterminate\:before\:tw-mask-position-\[center\]:indeterminate::before{content:var(--tw-content);-webkit-mask-position:center;mask-position:center}.indeterminate\:before\:tw-mask-repeat-\[no-repeat\]:indeterminate::before{content:var(--tw-content);-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat}.empty\:tw-ms-0:empty{margin-inline-start:0px}.empty\:tw-hidden:empty{display:none}.disabled\:tw-cursor-auto:disabled{cursor:auto}.disabled\:tw-cursor-default:disabled{cursor:default}.disabled\:tw-cursor-not-allowed:disabled{cursor:not-allowed}.disabled\:tw-border:disabled{border-width:1px}.disabled\:tw-border-0:disabled{border-width:0px}.disabled\:tw-border-secondary-300:disabled{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-300) / var(--tw-border-opacity, 1))}.disabled\:tw-bg-secondary-100:disabled{--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-100) / var(--tw-bg-opacity, 1))}.disabled\:tw-bg-secondary-300:disabled{--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-300) / var(--tw-bg-opacity, 1))}.disabled\:\!tw-text-muted:disabled{--tw-text-opacity:1 !important;color:rgb(var(--color-text-muted) / var(--tw-text-opacity, 1)) !important}.disabled\:\!tw-text-secondary-300:disabled{--tw-text-opacity:1 !important;color:rgb(var(--color-secondary-300) / var(--tw-text-opacity, 1)) !important}.disabled\:tw-no-underline:disabled{text-decoration-line:none}.disabled\:tw-opacity-60:disabled{opacity:0.6}.checked\:disabled\:tw-border-secondary-100:disabled:checked{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-100) / var(--tw-border-opacity, 1))}.checked\:disabled\:tw-border-secondary-600:disabled:checked{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-600) / var(--tw-border-opacity, 1))}.checked\:disabled\:tw-bg-background:disabled:checked{--tw-bg-opacity:1;background-color:rgb(var(--color-background) / var(--tw-bg-opacity, 1))}.checked\:disabled\:tw-bg-secondary-100:disabled:checked{--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-100) / var(--tw-bg-opacity, 1))}.checked\:disabled\:before\:tw-bg-secondary-600:disabled:checked::before{content:var(--tw-content);--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-600) / var(--tw-bg-opacity, 1))}.checked\:disabled\:before\:tw-bg-text-muted:disabled:checked::before{content:var(--tw-content);--tw-bg-opacity:1;background-color:rgb(var(--color-text-muted) / var(--tw-bg-opacity, 1))}.indeterminate\:disabled\:tw-border-secondary-100:disabled:indeterminate{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-100) / var(--tw-border-opacity, 1))}.indeterminate\:disabled\:tw-bg-secondary-100:disabled:indeterminate{--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-100) / var(--tw-bg-opacity, 1))}.indeterminate\:disabled\:before\:tw-bg-text-muted:disabled:indeterminate::before{content:var(--tw-content);--tw-bg-opacity:1;background-color:rgb(var(--color-text-muted) / var(--tw-bg-opacity, 1))}.tw-group\/toggle:first-of-type .group-first-of-type\/toggle\:tw-rounded-s-full{border-start-start-radius:9999px;border-end-start-radius:9999px}.tw-group\/toggle:first-of-type .group-first-of-type\/toggle\:tw-border-l{border-left-width:1px}.tw-group\/toggle:last-of-type .group-last-of-type\/toggle\:tw-rounded-e-full{border-start-end-radius:9999px;border-end-end-radius:9999px}.tw-group\/bit-form-field:focus-within .group-focus-within\/bit-form-field\:tw-border-2{border-width:2px}.tw-group\/bit-form-field:focus-within .group-focus-within\/bit-form-field\:tw-border-x-0{border-left-width:0px;border-right-width:0px}.tw-group\/bit-form-field:focus-within .group-focus-within\/bit-form-field\:tw-border-l-0{border-left-width:0px}.tw-group\/bit-form-field:focus-within .group-focus-within\/bit-form-field\:tw-border-r-0{border-right-width:0px}.tw-group\/bit-form-field:focus-within .group-focus-within\/bit-form-field\:tw-border-t-0{border-top-width:0px}.tw-group\/bit-form-field:focus-within .group-focus-within\/bit-form-field\:tw-border-primary-600{--tw-border-opacity:1;border-color:rgb(var(--color-primary-600) / var(--tw-border-opacity, 1))}.tw-group\/bit-form-field:focus-within .group-focus-within\/bit-form-field\:tw-outline-none{outline:2px solid transparent;outline-offset:2px}.tw-group\/vault-section-header:hover .group-hover\/vault-section-header\:tw-inline-block{display:inline-block}.tw-group\/vault-section-header:hover .group-hover\/vault-section-header\:tw-hidden{display:none}.tw-group\/bit-form-field:hover .group-hover\/bit-form-field\:tw-border-danger-700{--tw-border-opacity:1;border-color:rgb(var(--color-danger-700) / var(--tw-border-opacity, 1))}.tw-group\/bit-form-field:hover .group-hover\/bit-form-field\:tw-border-primary-600{--tw-border-opacity:1;border-color:rgb(var(--color-primary-600) / var(--tw-border-opacity, 1))}.tw-group\/tab-nav-btn:hover .group-hover\/tab-nav-btn\:tw-fill-primary-600{fill:rgb(var(--color-primary-600) / 1)}.tw-group\/chip-select:hover .group-hover\/chip-select\:tw-text-secondary-700{--tw-text-opacity:1;color:rgb(var(--color-secondary-700) / var(--tw-text-opacity, 1))}.tw-group\/tab:hover .group-hover\/tab\:tw-underline{text-decoration-line:underline}.tw-group\/bit-form-field:focus-within:hover .group-focus-within\/bit-form-field\:group-hover\/bit-form-field\:tw-border-primary-600{--tw-border-opacity:1;border-color:rgb(var(--color-primary-600) / var(--tw-border-opacity, 1))}.tw-group\/vault-section-header:focus-visible .group-focus-visible\/vault-section-header\:tw-inline-block{display:inline-block}.tw-group\/vault-section-header:focus-visible .group-focus-visible\/vault-section-header\:tw-hidden{display:none}.tw-peer\/toggle-input:checked ~ .peer-checked\/toggle-input\:tw-border-primary-600{--tw-border-opacity:1;border-color:rgb(var(--color-primary-600) / var(--tw-border-opacity, 1))}.tw-peer\/toggle-input:checked ~ .peer-checked\/toggle-input\:tw-bg-primary-600{--tw-bg-opacity:1;background-color:rgb(var(--color-primary-600) / var(--tw-bg-opacity, 1))}.tw-peer\/toggle-input:checked ~ .peer-checked\/toggle-input\:\!tw-text-contrast{--tw-text-opacity:1 !important;color:rgb(var(--color-text-contrast) / var(--tw-text-opacity, 1)) !important}.tw-peer\/toggle-input:focus-visible ~ .peer-focus-visible\/toggle-input\:tw-z-10{z-index:10}.tw-peer\/toggle-input:focus-visible ~ .peer-focus-visible\/toggle-input\:tw-border-primary-600{--tw-border-opacity:1;border-color:rgb(var(--color-primary-600) / var(--tw-border-opacity, 1))}.tw-peer\/toggle-input:focus-visible ~ .peer-focus-visible\/toggle-input\:tw-bg-primary-600{--tw-bg-opacity:1;background-color:rgb(var(--color-primary-600) / var(--tw-bg-opacity, 1))}.tw-peer\/toggle-input:focus-visible ~ .peer-focus-visible\/toggle-input\:\!tw-text-contrast{--tw-text-opacity:1 !important;color:rgb(var(--color-text-contrast) / var(--tw-text-opacity, 1)) !important}.tw-peer\/toggle-input:focus-visible ~ .peer-focus-visible\/toggle-input\:tw-outline-none{outline:2px solid transparent;outline-offset:2px}.tw-peer\/toggle-input:focus-visible ~ .peer-focus-visible\/toggle-input\:tw-ring{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)}.tw-peer\/toggle-input:focus-visible ~ .peer-focus-visible\/toggle-input\:tw-ring-primary-600{--tw-ring-opacity:1;--tw-ring-color:rgb(var(--color-primary-600) / var(--tw-ring-opacity, 1))}.tw-peer\/toggle-input:focus-visible ~ .peer-focus-visible\/toggle-input\:tw-ring-offset-2{--tw-ring-offset-width:2px}.has-\[input\:-moz-read-only\:not\(\[hidden\]\)\]\:tw-bg-secondary-100:has(input:-moz-read-only:not([hidden])){--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-100) / var(--tw-bg-opacity, 1))}.has-\[input\:read-only\:not\(\[hidden\]\)\]\:tw-bg-secondary-100:has(input:read-only:not([hidden])){--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-100) / var(--tw-bg-opacity, 1))}.has-\[textarea\:-moz-read-only\:not\(\[hidden\]\)\]\:tw-bg-secondary-100:has(textarea:-moz-read-only:not([hidden])){--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-100) / var(--tw-bg-opacity, 1))}.has-\[textarea\:read-only\:not\(\[hidden\]\)\]\:tw-bg-secondary-100:has(textarea:read-only:not([hidden])){--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-100) / var(--tw-bg-opacity, 1))}.has-\[bit-multi-select\]\:tw-p-0:has(bit-multi-select){padding:0px}.has-\[bit-select\]\:tw-p-0:has(bit-select){padding:0px}.has-\[\+_\*_bit-card\]\:tw-px-1:has(+ * bit-card){padding-left:0.25rem;padding-right:0.25rem}.has-\[\+_\*_bit-item\]\:tw-px-1:has(+ * bit-item){padding-left:0.25rem;padding-right:0.25rem}.has-\[\+_bit-card\]\:tw-px-1:has(+ bit-card){padding-left:0.25rem;padding-right:0.25rem}.has-\[\+_bit-item\]\:tw-px-1:has(+ bit-item){padding-left:0.25rem;padding-right:0.25rem}.has-\[\+_\*_bit-card\]\:tw-pb-1:has(+ * bit-card){padding-bottom:0.25rem}.has-\[\+_\*_bit-item\]\:tw-pb-1:has(+ * bit-item){padding-bottom:0.25rem}.has-\[\+_bit-card\]\:tw-pb-1:has(+ bit-card){padding-bottom:0.25rem}.has-\[\+_bit-item\]\:tw-pb-1:has(+ bit-item){padding-bottom:0.25rem}.has-\[\:focus-visible\]\:tw-ring:has(:focus-visible){--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)}.has-\[\:focus-visible\]\:tw-ring-primary-600:has(:focus-visible){--tw-ring-opacity:1;--tw-ring-color:rgb(var(--color-primary-600) / var(--tw-ring-opacity, 1))}.tw-group\/bit-form-field:has(bit-label) .group-has-\[bit-label\]\/bit-form-field\:tw-block{display:block}.tw-group\/bit-form-field:has(input:-moz-read-only):hover .group-has-\[input\:-moz-read-only\]\/bit-form-field\:group-hover\/bit-form-field\:tw-border-secondary-500{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-500) / var(--tw-border-opacity, 1))}.tw-group\/bit-form-field:has(input:read-only):hover .group-has-\[input\:read-only\]\/bit-form-field\:group-hover\/bit-form-field\:tw-border-secondary-500{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-500) / var(--tw-border-opacity, 1))}.tw-group\/bit-form-field:has(textarea:-moz-read-only):hover .group-has-\[textarea\:-moz-read-only\]\/bit-form-field\:group-hover\/bit-form-field\:tw-border-secondary-500{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-500) / var(--tw-border-opacity, 1))}.tw-group\/bit-form-field:has(textarea:read-only):hover .group-has-\[textarea\:read-only\]\/bit-form-field\:group-hover\/bit-form-field\:tw-border-secondary-500{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-500) / var(--tw-border-opacity, 1))}.aria-expanded\:tw-bg-text-muted[aria-expanded="true"]{--tw-bg-opacity:1;background-color:rgb(var(--color-text-muted) / var(--tw-bg-opacity, 1))}.aria-expanded\:\!tw-text-contrast[aria-expanded="true"]{--tw-text-opacity:1 !important;color:rgb(var(--color-text-contrast) / var(--tw-text-opacity, 1)) !important}.active\:\!tw-ring-0:active{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important}.active\:\!tw-ring-offset-0:active{--tw-ring-offset-width:0px !important}.active\:\!tw-ring-0.tw-test-active{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important}.active\:\!tw-ring-offset-0.tw-test-active{--tw-ring-offset-width:0px !important}.hover\:tw-border-2:hover{border-width:2px}.hover\:\!tw-border-secondary-700:hover{--tw-border-opacity:1 !important;border-color:rgb(var(--color-secondary-700) / var(--tw-border-opacity, 1)) !important}.hover\:tw-border-danger-600:hover{--tw-border-opacity:1;border-color:rgb(var(--color-danger-600) / var(--tw-border-opacity, 1))}.hover\:tw-border-danger-700:hover{--tw-border-opacity:1;border-color:rgb(var(--color-danger-700) / var(--tw-border-opacity, 1))}.hover\:tw-border-info-600:hover{--tw-border-opacity:1;border-color:rgb(var(--color-info-600) / var(--tw-border-opacity, 1))}.hover\:tw-border-notification-600:hover{--tw-border-opacity:1;border-color:rgb(var(--color-notification-600) / var(--tw-border-opacity, 1))}.hover\:tw-border-primary-600:hover{--tw-border-opacity:1;border-color:rgb(var(--color-primary-600) / var(--tw-border-opacity, 1))}.hover\:tw-border-primary-700:hover{--tw-border-opacity:1;border-color:rgb(var(--color-primary-700) / var(--tw-border-opacity, 1))}.hover\:tw-border-secondary-600:hover{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-600) / var(--tw-border-opacity, 1))}.hover\:tw-border-secondary-700:hover{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-700) / var(--tw-border-opacity, 1))}.hover\:tw-border-success-600:hover{--tw-border-opacity:1;border-color:rgb(var(--color-success-600) / var(--tw-border-opacity, 1))}.hover\:tw-border-text-alt2:hover{--tw-border-opacity:1;border-color:rgb(var(--color-text-alt2) / var(--tw-border-opacity, 1))}.hover\:tw-border-text-contrast:hover{--tw-border-opacity:1;border-color:rgb(var(--color-text-contrast) / var(--tw-border-opacity, 1))}.hover\:tw-border-text-main:hover{--tw-border-opacity:1;border-color:rgb(var(--color-text-main) / var(--tw-border-opacity, 1))}.hover\:tw-border-warning-600:hover{--tw-border-opacity:1;border-color:rgb(var(--color-warning-600) / var(--tw-border-opacity, 1))}.hover\:tw-border-t-primary-700:hover{--tw-border-opacity:1;border-top-color:rgb(var(--color-primary-700) / var(--tw-border-opacity, 1))}.hover\:tw-bg-background-alt:hover{--tw-bg-opacity:1;background-color:rgb(var(--color-background-alt) / var(--tw-bg-opacity, 1))}.hover\:tw-bg-danger-600:hover{--tw-bg-opacity:1;background-color:rgb(var(--color-danger-600) / var(--tw-bg-opacity, 1))}.hover\:tw-bg-info-600:hover{--tw-bg-opacity:1;background-color:rgb(var(--color-info-600) / var(--tw-bg-opacity, 1))}.hover\:tw-bg-notification-600:hover{--tw-bg-opacity:1;background-color:rgb(var(--color-notification-600) / var(--tw-bg-opacity, 1))}.hover\:tw-bg-primary-100:hover{--tw-bg-opacity:1;background-color:rgb(var(--color-primary-100) / var(--tw-bg-opacity, 1))}.hover\:tw-bg-primary-300\/60:hover{background-color:rgb(var(--color-primary-300) / 0.6)}.hover\:tw-bg-primary-600:hover{--tw-bg-opacity:1;background-color:rgb(var(--color-primary-600) / var(--tw-bg-opacity, 1))}.hover\:tw-bg-primary-700:hover{--tw-bg-opacity:1;background-color:rgb(var(--color-primary-700) / var(--tw-bg-opacity, 1))}.hover\:tw-bg-secondary-100:hover{--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-100) / var(--tw-bg-opacity, 1))}.hover\:tw-bg-secondary-600:hover{--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-600) / var(--tw-bg-opacity, 1))}.hover\:tw-bg-secondary-700:hover{--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-700) / var(--tw-bg-opacity, 1))}.hover\:tw-bg-success-600:hover{--tw-bg-opacity:1;background-color:rgb(var(--color-success-600) / var(--tw-bg-opacity, 1))}.hover\:tw-bg-text-muted:hover{--tw-bg-opacity:1;background-color:rgb(var(--color-text-muted) / var(--tw-bg-opacity, 1))}.hover\:tw-bg-transparent:hover{background-color:transparent}.hover\:tw-bg-transparent-hover:hover{background-color:var(--color-transparent-hover)}.hover\:tw-bg-warning-600:hover{--tw-bg-opacity:1;background-color:rgb(var(--color-warning-600) / var(--tw-bg-opacity, 1))}.hover\:\!tw-text-alt2:hover{--tw-text-opacity:1 !important;color:rgb(var(--color-text-alt2) / var(--tw-text-opacity, 1)) !important}.hover\:\!tw-text-black:hover{--tw-text-opacity:1 !important;color:rgb(0 0 0 / var(--tw-text-opacity, 1)) !important}.hover\:\!tw-text-contrast:hover{--tw-text-opacity:1 !important;color:rgb(var(--color-text-contrast) / var(--tw-text-opacity, 1)) !important}.hover\:\!tw-text-danger-600:hover{--tw-text-opacity:1 !important;color:rgb(var(--color-danger-600) / var(--tw-text-opacity, 1)) !important}.hover\:\!tw-text-main:hover{--tw-text-opacity:1 !important;color:rgb(var(--color-text-main) / var(--tw-text-opacity, 1)) !important}.hover\:\!tw-text-primary-700:hover{--tw-text-opacity:1 !important;color:rgb(var(--color-primary-700) / var(--tw-text-opacity, 1)) !important}.hover\:\!tw-text-secondary-300:hover{--tw-text-opacity:1 !important;color:rgb(var(--color-secondary-300) / var(--tw-text-opacity, 1)) !important}.hover\:tw-text-alt2:hover{--tw-text-opacity:1;color:rgb(var(--color-text-alt2) / var(--tw-text-opacity, 1))}.hover\:tw-text-main:hover{--tw-text-opacity:1;color:rgb(var(--color-text-main) / var(--tw-text-opacity, 1))}.hover\:tw-text-primary-600:hover{--tw-text-opacity:1;color:rgb(var(--color-primary-600) / var(--tw-text-opacity, 1))}.hover\:tw-underline:hover{text-decoration-line:underline}.hover\:tw-no-underline:hover{text-decoration-line:none}.hover\:tw-decoration-1:hover{text-decoration-thickness:1px}.hover\:tw-outline:hover{outline-style:solid}.hover\:tw-outline-1:hover{outline-width:1px}.hover\:tw-outline-offset-1:hover{outline-offset:1px}.checked\:hover\:tw-border-2:hover:checked{border-width:2px}.checked\:hover\:tw-border-primary-700:hover:checked{--tw-border-opacity:1;border-color:rgb(var(--color-primary-700) / var(--tw-border-opacity, 1))}.checked\:hover\:tw-bg-primary-700:hover:checked{--tw-bg-opacity:1;background-color:rgb(var(--color-primary-700) / var(--tw-bg-opacity, 1))}.checked\:hover\:before\:tw-bg-primary-700:hover:checked::before{content:var(--tw-content);--tw-bg-opacity:1;background-color:rgb(var(--color-primary-700) / var(--tw-bg-opacity, 1))}.indeterminate\:hover\:tw-border-primary-700:hover:indeterminate{--tw-border-opacity:1;border-color:rgb(var(--color-primary-700) / var(--tw-border-opacity, 1))}.indeterminate\:hover\:tw-bg-primary-700:hover:indeterminate{--tw-bg-opacity:1;background-color:rgb(var(--color-primary-700) / var(--tw-bg-opacity, 1))}.disabled\:hover\:tw-border:hover:disabled{border-width:1px}.disabled\:hover\:tw-border-primary-600:hover:disabled{--tw-border-opacity:1;border-color:rgb(var(--color-primary-600) / var(--tw-border-opacity, 1))}.disabled\:hover\:tw-border-secondary-300:hover:disabled{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-300) / var(--tw-border-opacity, 1))}.disabled\:hover\:tw-border-secondary-500:hover:disabled{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-500) / var(--tw-border-opacity, 1))}.disabled\:hover\:tw-border-text-muted:hover:disabled{--tw-border-opacity:1;border-color:rgb(var(--color-text-muted) / var(--tw-border-opacity, 1))}.disabled\:hover\:tw-border-transparent:hover:disabled{border-color:transparent}.hover\:disabled\:tw-border-transparent:disabled:hover{border-color:transparent}.disabled\:hover\:tw-bg-background:hover:disabled{--tw-bg-opacity:1;background-color:rgb(var(--color-background) / var(--tw-bg-opacity, 1))}.disabled\:hover\:tw-bg-primary-600:hover:disabled{--tw-bg-opacity:1;background-color:rgb(var(--color-primary-600) / var(--tw-bg-opacity, 1))}.disabled\:hover\:tw-bg-secondary-100:hover:disabled{--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-100) / var(--tw-bg-opacity, 1))}.disabled\:hover\:tw-bg-secondary-300:hover:disabled{--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-300) / var(--tw-bg-opacity, 1))}.disabled\:hover\:tw-bg-transparent:hover:disabled{background-color:transparent}.disabled\:hover\:\!tw-text-muted:hover:disabled{--tw-text-opacity:1 !important;color:rgb(var(--color-text-muted) / var(--tw-text-opacity, 1)) !important}.disabled\:hover\:\!tw-text-secondary-300:hover:disabled{--tw-text-opacity:1 !important;color:rgb(var(--color-secondary-300) / var(--tw-text-opacity, 1)) !important}.disabled\:hover\:tw-no-underline:hover:disabled{text-decoration-line:none}.checked\:disabled\:hover\:tw-border-2:hover:disabled:checked{border-width:2px}.checked\:disabled\:hover\:tw-border-secondary-100:hover:disabled:checked{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-100) / var(--tw-border-opacity, 1))}.checked\:disabled\:hover\:tw-border-secondary-600:hover:disabled:checked{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-600) / var(--tw-border-opacity, 1))}.checked\:disabled\:hover\:before\:tw-bg-secondary-600:hover:disabled:checked::before{content:var(--tw-content);--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-600) / var(--tw-bg-opacity, 1))}.aria-expanded\:hover\:tw-border-secondary-700:hover[aria-expanded="true"]{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-700) / var(--tw-border-opacity, 1))}.aria-expanded\:hover\:tw-bg-secondary-700:hover[aria-expanded="true"]{--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-700) / var(--tw-bg-opacity, 1))}.hover\:tw-border-2.tw-test-hover{border-width:2px}.hover\:\!tw-border-secondary-700.tw-test-hover{--tw-border-opacity:1 !important;border-color:rgb(var(--color-secondary-700) / var(--tw-border-opacity, 1)) !important}.hover\:tw-border-danger-600.tw-test-hover{--tw-border-opacity:1;border-color:rgb(var(--color-danger-600) / var(--tw-border-opacity, 1))}.hover\:tw-border-danger-700.tw-test-hover{--tw-border-opacity:1;border-color:rgb(var(--color-danger-700) / var(--tw-border-opacity, 1))}.hover\:tw-border-info-600.tw-test-hover{--tw-border-opacity:1;border-color:rgb(var(--color-info-600) / var(--tw-border-opacity, 1))}.hover\:tw-border-notification-600.tw-test-hover{--tw-border-opacity:1;border-color:rgb(var(--color-notification-600) / var(--tw-border-opacity, 1))}.hover\:tw-border-primary-600.tw-test-hover{--tw-border-opacity:1;border-color:rgb(var(--color-primary-600) / var(--tw-border-opacity, 1))}.hover\:tw-border-primary-700.tw-test-hover{--tw-border-opacity:1;border-color:rgb(var(--color-primary-700) / var(--tw-border-opacity, 1))}.hover\:tw-border-secondary-600.tw-test-hover{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-600) / var(--tw-border-opacity, 1))}.hover\:tw-border-secondary-700.tw-test-hover{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-700) / var(--tw-border-opacity, 1))}.hover\:tw-border-success-600.tw-test-hover{--tw-border-opacity:1;border-color:rgb(var(--color-success-600) / var(--tw-border-opacity, 1))}.hover\:tw-border-text-alt2.tw-test-hover{--tw-border-opacity:1;border-color:rgb(var(--color-text-alt2) / var(--tw-border-opacity, 1))}.hover\:tw-border-text-contrast.tw-test-hover{--tw-border-opacity:1;border-color:rgb(var(--color-text-contrast) / var(--tw-border-opacity, 1))}.hover\:tw-border-text-main.tw-test-hover{--tw-border-opacity:1;border-color:rgb(var(--color-text-main) / var(--tw-border-opacity, 1))}.hover\:tw-border-warning-600.tw-test-hover{--tw-border-opacity:1;border-color:rgb(var(--color-warning-600) / var(--tw-border-opacity, 1))}.hover\:tw-border-t-primary-700.tw-test-hover{--tw-border-opacity:1;border-top-color:rgb(var(--color-primary-700) / var(--tw-border-opacity, 1))}.hover\:tw-bg-background-alt.tw-test-hover{--tw-bg-opacity:1;background-color:rgb(var(--color-background-alt) / var(--tw-bg-opacity, 1))}.hover\:tw-bg-danger-600.tw-test-hover{--tw-bg-opacity:1;background-color:rgb(var(--color-danger-600) / var(--tw-bg-opacity, 1))}.hover\:tw-bg-info-600.tw-test-hover{--tw-bg-opacity:1;background-color:rgb(var(--color-info-600) / var(--tw-bg-opacity, 1))}.hover\:tw-bg-notification-600.tw-test-hover{--tw-bg-opacity:1;background-color:rgb(var(--color-notification-600) / var(--tw-bg-opacity, 1))}.hover\:tw-bg-primary-100.tw-test-hover{--tw-bg-opacity:1;background-color:rgb(var(--color-primary-100) / var(--tw-bg-opacity, 1))}.hover\:tw-bg-primary-300\/60.tw-test-hover{background-color:rgb(var(--color-primary-300) / 0.6)}.hover\:tw-bg-primary-600.tw-test-hover{--tw-bg-opacity:1;background-color:rgb(var(--color-primary-600) / var(--tw-bg-opacity, 1))}.hover\:tw-bg-primary-700.tw-test-hover{--tw-bg-opacity:1;background-color:rgb(var(--color-primary-700) / var(--tw-bg-opacity, 1))}.hover\:tw-bg-secondary-100.tw-test-hover{--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-100) / var(--tw-bg-opacity, 1))}.hover\:tw-bg-secondary-600.tw-test-hover{--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-600) / var(--tw-bg-opacity, 1))}.hover\:tw-bg-secondary-700.tw-test-hover{--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-700) / var(--tw-bg-opacity, 1))}.hover\:tw-bg-success-600.tw-test-hover{--tw-bg-opacity:1;background-color:rgb(var(--color-success-600) / var(--tw-bg-opacity, 1))}.hover\:tw-bg-text-muted.tw-test-hover{--tw-bg-opacity:1;background-color:rgb(var(--color-text-muted) / var(--tw-bg-opacity, 1))}.hover\:tw-bg-transparent.tw-test-hover{background-color:transparent}.hover\:tw-bg-transparent-hover.tw-test-hover{background-color:var(--color-transparent-hover)}.hover\:tw-bg-warning-600.tw-test-hover{--tw-bg-opacity:1;background-color:rgb(var(--color-warning-600) / var(--tw-bg-opacity, 1))}.hover\:\!tw-text-alt2.tw-test-hover{--tw-text-opacity:1 !important;color:rgb(var(--color-text-alt2) / var(--tw-text-opacity, 1)) !important}.hover\:\!tw-text-black.tw-test-hover{--tw-text-opacity:1 !important;color:rgb(0 0 0 / var(--tw-text-opacity, 1)) !important}.hover\:\!tw-text-contrast.tw-test-hover{--tw-text-opacity:1 !important;color:rgb(var(--color-text-contrast) / var(--tw-text-opacity, 1)) !important}.hover\:\!tw-text-danger-600.tw-test-hover{--tw-text-opacity:1 !important;color:rgb(var(--color-danger-600) / var(--tw-text-opacity, 1)) !important}.hover\:\!tw-text-main.tw-test-hover{--tw-text-opacity:1 !important;color:rgb(var(--color-text-main) / var(--tw-text-opacity, 1)) !important}.hover\:\!tw-text-primary-700.tw-test-hover{--tw-text-opacity:1 !important;color:rgb(var(--color-primary-700) / var(--tw-text-opacity, 1)) !important}.hover\:\!tw-text-secondary-300.tw-test-hover{--tw-text-opacity:1 !important;color:rgb(var(--color-secondary-300) / var(--tw-text-opacity, 1)) !important}.hover\:tw-text-alt2.tw-test-hover{--tw-text-opacity:1;color:rgb(var(--color-text-alt2) / var(--tw-text-opacity, 1))}.hover\:tw-text-main.tw-test-hover{--tw-text-opacity:1;color:rgb(var(--color-text-main) / var(--tw-text-opacity, 1))}.hover\:tw-text-primary-600.tw-test-hover{--tw-text-opacity:1;color:rgb(var(--color-primary-600) / var(--tw-text-opacity, 1))}.hover\:tw-underline.tw-test-hover{text-decoration-line:underline}.hover\:tw-no-underline.tw-test-hover{text-decoration-line:none}.hover\:tw-decoration-1.tw-test-hover{text-decoration-thickness:1px}.hover\:tw-outline.tw-test-hover{outline-style:solid}.hover\:tw-outline-1.tw-test-hover{outline-width:1px}.hover\:tw-outline-offset-1.tw-test-hover{outline-offset:1px}.checked\:hover\:tw-border-2.tw-test-hover:checked{border-width:2px}.checked\:hover\:tw-border-primary-700.tw-test-hover:checked{--tw-border-opacity:1;border-color:rgb(var(--color-primary-700) / var(--tw-border-opacity, 1))}.checked\:hover\:tw-bg-primary-700.tw-test-hover:checked{--tw-bg-opacity:1;background-color:rgb(var(--color-primary-700) / var(--tw-bg-opacity, 1))}.checked\:hover\:before\:tw-bg-primary-700.tw-test-hover:checked::before{content:var(--tw-content);--tw-bg-opacity:1;background-color:rgb(var(--color-primary-700) / var(--tw-bg-opacity, 1))}.indeterminate\:hover\:tw-border-primary-700.tw-test-hover:indeterminate{--tw-border-opacity:1;border-color:rgb(var(--color-primary-700) / var(--tw-border-opacity, 1))}.indeterminate\:hover\:tw-bg-primary-700.tw-test-hover:indeterminate{--tw-bg-opacity:1;background-color:rgb(var(--color-primary-700) / var(--tw-bg-opacity, 1))}.disabled\:hover\:tw-border.tw-test-hover:disabled{border-width:1px}.disabled\:hover\:tw-border-primary-600.tw-test-hover:disabled{--tw-border-opacity:1;border-color:rgb(var(--color-primary-600) / var(--tw-border-opacity, 1))}.disabled\:hover\:tw-border-secondary-300.tw-test-hover:disabled{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-300) / var(--tw-border-opacity, 1))}.disabled\:hover\:tw-border-secondary-500.tw-test-hover:disabled{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-500) / var(--tw-border-opacity, 1))}.disabled\:hover\:tw-border-text-muted.tw-test-hover:disabled{--tw-border-opacity:1;border-color:rgb(var(--color-text-muted) / var(--tw-border-opacity, 1))}.disabled\:hover\:tw-border-transparent.tw-test-hover:disabled{border-color:transparent}.hover\:disabled\:tw-border-transparent:disabled.tw-test-hover{border-color:transparent}.disabled\:hover\:tw-bg-background.tw-test-hover:disabled{--tw-bg-opacity:1;background-color:rgb(var(--color-background) / var(--tw-bg-opacity, 1))}.disabled\:hover\:tw-bg-primary-600.tw-test-hover:disabled{--tw-bg-opacity:1;background-color:rgb(var(--color-primary-600) / var(--tw-bg-opacity, 1))}.disabled\:hover\:tw-bg-secondary-100.tw-test-hover:disabled{--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-100) / var(--tw-bg-opacity, 1))}.disabled\:hover\:tw-bg-secondary-300.tw-test-hover:disabled{--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-300) / var(--tw-bg-opacity, 1))}.disabled\:hover\:tw-bg-transparent.tw-test-hover:disabled{background-color:transparent}.disabled\:hover\:\!tw-text-muted.tw-test-hover:disabled{--tw-text-opacity:1 !important;color:rgb(var(--color-text-muted) / var(--tw-text-opacity, 1)) !important}.disabled\:hover\:\!tw-text-secondary-300.tw-test-hover:disabled{--tw-text-opacity:1 !important;color:rgb(var(--color-secondary-300) / var(--tw-text-opacity, 1)) !important}.disabled\:hover\:tw-no-underline.tw-test-hover:disabled{text-decoration-line:none}.checked\:disabled\:hover\:tw-border-2.tw-test-hover:disabled:checked{border-width:2px}.checked\:disabled\:hover\:tw-border-secondary-100.tw-test-hover:disabled:checked{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-100) / var(--tw-border-opacity, 1))}.checked\:disabled\:hover\:tw-border-secondary-600.tw-test-hover:disabled:checked{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-600) / var(--tw-border-opacity, 1))}.checked\:disabled\:hover\:before\:tw-bg-secondary-600.tw-test-hover:disabled:checked::before{content:var(--tw-content);--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-600) / var(--tw-bg-opacity, 1))}.aria-expanded\:hover\:tw-border-secondary-700.tw-test-hover[aria-expanded="true"]{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-700) / var(--tw-border-opacity, 1))}.aria-expanded\:hover\:tw-bg-secondary-700.tw-test-hover[aria-expanded="true"]{--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-700) / var(--tw-bg-opacity, 1))}.focus\:tw-z-10:focus{z-index:10}.focus\:tw-border-primary-600:focus{--tw-border-opacity:1;border-color:rgb(var(--color-primary-600) / var(--tw-border-opacity, 1))}.focus\:tw-outline-none:focus{outline:2px solid transparent;outline-offset:2px}.focus\:tw-ring-1:focus{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)}.focus\:tw-ring-inset:focus{--tw-ring-inset:inset}.focus\:tw-ring-primary-600:focus{--tw-ring-opacity:1;--tw-ring-color:rgb(var(--color-primary-600) / var(--tw-ring-opacity, 1))}.focus\:hover\:tw-border-primary-600:hover:focus{--tw-border-opacity:1;border-color:rgb(var(--color-primary-600) / var(--tw-border-opacity, 1))}.focus\:hover\:tw-border-primary-600.tw-test-hover:focus{--tw-border-opacity:1;border-color:rgb(var(--color-primary-600) / var(--tw-border-opacity, 1))}.focus\:tw-z-10.tw-test-focus{z-index:10}.focus\:tw-border-primary-600.tw-test-focus{--tw-border-opacity:1;border-color:rgb(var(--color-primary-600) / var(--tw-border-opacity, 1))}.focus\:tw-outline-none.tw-test-focus{outline:2px solid transparent;outline-offset:2px}.focus\:tw-ring-1.tw-test-focus{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)}.focus\:tw-ring-inset.tw-test-focus{--tw-ring-inset:inset}.focus\:tw-ring-primary-600.tw-test-focus{--tw-ring-opacity:1;--tw-ring-color:rgb(var(--color-primary-600) / var(--tw-ring-opacity, 1))}.focus\:hover\:tw-border-primary-600:hover.tw-test-focus{--tw-border-opacity:1;border-color:rgb(var(--color-primary-600) / var(--tw-border-opacity, 1))}.focus\:hover\:tw-border-primary-600.tw-test-hover.tw-test-focus{--tw-border-opacity:1;border-color:rgb(var(--color-primary-600) / var(--tw-border-opacity, 1))}.focus-within\:tw-pointer-events-auto:focus-within{pointer-events:auto}.focus-within\:tw-opacity-100:focus-within{opacity:1}.focus-within\:tw-pointer-events-auto.tw-test-focus-within{pointer-events:auto}.focus-within\:tw-opacity-100.tw-test-focus-within{opacity:1}.focus-visible\:tw-z-10:focus-visible{z-index:10}.focus-visible\:tw-z-50:focus-visible{z-index:50}.focus-visible\:tw-rounded-lg:focus-visible{border-radius:0.5rem}.focus-visible\:tw-border-primary-600:focus-visible{--tw-border-opacity:1;border-color:rgb(var(--color-primary-600) / var(--tw-border-opacity, 1))}.focus-visible\:tw-border-t-primary-700:focus-visible{--tw-border-opacity:1;border-top-color:rgb(var(--color-primary-700) / var(--tw-border-opacity, 1))}.focus-visible\:\!tw-text-primary-700:focus-visible{--tw-text-opacity:1 !important;color:rgb(var(--color-primary-700) / var(--tw-text-opacity, 1)) !important}.focus-visible\:tw-text-info-700:focus-visible{--tw-text-opacity:1;color:rgb(var(--color-info-700) / var(--tw-text-opacity, 1))}.focus-visible\:tw-underline:focus-visible{text-decoration-line:underline}.focus-visible\:tw-decoration-1:focus-visible{text-decoration-thickness:1px}.focus-visible\:tw-outline-none:focus-visible{outline:2px solid transparent;outline-offset:2px}.focus-visible\:tw-ring:focus-visible{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)}.focus-visible\:tw-ring-2:focus-visible{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)}.focus-visible\:tw-ring-inset:focus-visible{--tw-ring-inset:inset}.focus-visible\:tw-ring-primary-600:focus-visible{--tw-ring-opacity:1;--tw-ring-color:rgb(var(--color-primary-600) / var(--tw-ring-opacity, 1))}.focus-visible\:tw-ring-text-alt2:focus-visible{--tw-ring-opacity:1;--tw-ring-color:rgb(var(--color-text-alt2) / var(--tw-ring-opacity, 1))}.focus-visible\:tw-ring-offset-2:focus-visible{--tw-ring-offset-width:2px}.focus-visible\:tw-ring-offset-secondary-300:focus-visible{--tw-ring-offset-color:rgb(var(--color-secondary-300) / 1)}.focus-visible\:before\:\!tw-ring-0:focus-visible::before{content:var(--tw-content);--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important}.focus-visible\:before\:tw-ring-2:focus-visible::before{content:var(--tw-content);--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)}.focus-visible\:before\:tw-ring-primary-600:focus-visible::before{content:var(--tw-content);--tw-ring-opacity:1;--tw-ring-color:rgb(var(--color-primary-600) / var(--tw-ring-opacity, 1))}.focus-visible\:before\:tw-ring-primary-700:focus-visible::before{content:var(--tw-content);--tw-ring-opacity:1;--tw-ring-color:rgb(var(--color-primary-700) / var(--tw-ring-opacity, 1))}.focus-visible\:before\:tw-ring-text-alt2:focus-visible::before{content:var(--tw-content);--tw-ring-opacity:1;--tw-ring-color:rgb(var(--color-text-alt2) / var(--tw-ring-opacity, 1))}.focus-visible\:before\:tw-ring-text-contrast:focus-visible::before{content:var(--tw-content);--tw-ring-opacity:1;--tw-ring-color:rgb(var(--color-text-contrast) / var(--tw-ring-opacity, 1))}.focus-visible\:before\:tw-ring-text-main:focus-visible::before{content:var(--tw-content);--tw-ring-opacity:1;--tw-ring-color:rgb(var(--color-text-main) / var(--tw-ring-opacity, 1))}.focus-visible\:hover\:tw-border-transparent:hover:focus-visible{border-color:transparent}.focus-visible\:hover\:tw-border-transparent.tw-test-hover:focus-visible{border-color:transparent}.focus-visible\:tw-z-10.tw-test-focus-visible{z-index:10}.focus-visible\:tw-z-50.tw-test-focus-visible{z-index:50}.focus-visible\:tw-rounded-lg.tw-test-focus-visible{border-radius:0.5rem}.focus-visible\:tw-border-primary-600.tw-test-focus-visible{--tw-border-opacity:1;border-color:rgb(var(--color-primary-600) / var(--tw-border-opacity, 1))}.focus-visible\:tw-border-t-primary-700.tw-test-focus-visible{--tw-border-opacity:1;border-top-color:rgb(var(--color-primary-700) / var(--tw-border-opacity, 1))}.focus-visible\:\!tw-text-primary-700.tw-test-focus-visible{--tw-text-opacity:1 !important;color:rgb(var(--color-primary-700) / var(--tw-text-opacity, 1)) !important}.focus-visible\:tw-text-info-700.tw-test-focus-visible{--tw-text-opacity:1;color:rgb(var(--color-info-700) / var(--tw-text-opacity, 1))}.focus-visible\:tw-underline.tw-test-focus-visible{text-decoration-line:underline}.focus-visible\:tw-decoration-1.tw-test-focus-visible{text-decoration-thickness:1px}.focus-visible\:tw-outline-none.tw-test-focus-visible{outline:2px solid transparent;outline-offset:2px}.focus-visible\:tw-ring.tw-test-focus-visible{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)}.focus-visible\:tw-ring-2.tw-test-focus-visible{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)}.focus-visible\:tw-ring-inset.tw-test-focus-visible{--tw-ring-inset:inset}.focus-visible\:tw-ring-primary-600.tw-test-focus-visible{--tw-ring-opacity:1;--tw-ring-color:rgb(var(--color-primary-600) / var(--tw-ring-opacity, 1))}.focus-visible\:tw-ring-text-alt2.tw-test-focus-visible{--tw-ring-opacity:1;--tw-ring-color:rgb(var(--color-text-alt2) / var(--tw-ring-opacity, 1))}.focus-visible\:tw-ring-offset-2.tw-test-focus-visible{--tw-ring-offset-width:2px}.focus-visible\:tw-ring-offset-secondary-300.tw-test-focus-visible{--tw-ring-offset-color:rgb(var(--color-secondary-300) / 1)}.focus-visible\:before\:\!tw-ring-0.tw-test-focus-visible::before{content:var(--tw-content);--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important}.focus-visible\:before\:tw-ring-2.tw-test-focus-visible::before{content:var(--tw-content);--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)}.focus-visible\:before\:tw-ring-primary-600.tw-test-focus-visible::before{content:var(--tw-content);--tw-ring-opacity:1;--tw-ring-color:rgb(var(--color-primary-600) / var(--tw-ring-opacity, 1))}.focus-visible\:before\:tw-ring-primary-700.tw-test-focus-visible::before{content:var(--tw-content);--tw-ring-opacity:1;--tw-ring-color:rgb(var(--color-primary-700) / var(--tw-ring-opacity, 1))}.focus-visible\:before\:tw-ring-text-alt2.tw-test-focus-visible::before{content:var(--tw-content);--tw-ring-opacity:1;--tw-ring-color:rgb(var(--color-text-alt2) / var(--tw-ring-opacity, 1))}.focus-visible\:before\:tw-ring-text-contrast.tw-test-focus-visible::before{content:var(--tw-content);--tw-ring-opacity:1;--tw-ring-color:rgb(var(--color-text-contrast) / var(--tw-ring-opacity, 1))}.focus-visible\:before\:tw-ring-text-main.tw-test-focus-visible::before{content:var(--tw-content);--tw-ring-opacity:1;--tw-ring-color:rgb(var(--color-text-main) / var(--tw-ring-opacity, 1))}.focus-visible\:hover\:tw-border-transparent:hover.tw-test-focus-visible{border-color:transparent}.focus-visible\:hover\:tw-border-transparent.tw-test-hover.tw-test-focus-visible{border-color:transparent}.tw-bit-compact .bit-compact\:tw-m-0{margin:0px}.tw-bit-compact .bit-compact\:tw-mb-0{margin-bottom:0px}.tw-bit-compact .bit-compact\:tw-mb-3{margin-bottom:0.75rem}.tw-bit-compact .bit-compact\:tw-mb-4{margin-bottom:1rem}.tw-bit-compact .bit-compact\:tw-p-2{padding:0.5rem}.tw-bit-compact .bit-compact\:tw-px-2{padding-left:0.5rem;padding-right:0.5rem}.tw-bit-compact .bit-compact\:tw-py-1{padding-top:0.25rem;padding-bottom:0.25rem}.tw-bit-compact .bit-compact\:tw-py-1\.5{padding-top:0.375rem;padding-bottom:0.375rem}.tw-bit-compact .bit-compact\:tw-py-3{padding-top:0.75rem;padding-bottom:0.75rem}.tw-bit-compact .bit-compact\:tw-pl-3{padding-left:0.75rem}.tw-bit-compact .bit-compact\:tw-ring-inset{--tw-ring-inset:inset}@media (prefers-reduced-motion: no-preference){.motion-safe\:tw-transition-colors{transition-property:color, background-color, border-color, text-decoration-color, fill, stroke;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}}@media (min-width: 640px){.sm\:tw-mb-10{margin-bottom:2.5rem}.sm\:tw-mb-6{margin-bottom:1.5rem}.sm\:tw-mr-4{margin-right:1rem}.sm\:tw-mt-6{margin-top:1.5rem}.sm\:tw-block{display:block}.sm\:tw-hidden{display:none}.sm\:tw-min-w-\[28rem\]{min-width:28rem}.sm\:tw-max-w-28{max-width:7rem}.sm\:tw-grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr))}.sm\:tw-border{border-width:1px}.sm\:tw-border-solid{border-style:solid}.sm\:tw-border-secondary-300{--tw-border-opacity:1;border-color:rgb(var(--color-secondary-300) / var(--tw-border-opacity, 1))}.sm\:tw-bg-background{--tw-bg-opacity:1;background-color:rgb(var(--color-background) / var(--tw-bg-opacity, 1))}.sm\:tw-p-8{padding:2rem}.sm\:tw-text-base{font-size:1rem;line-height:1.5rem}}@media (min-width: 768px){.md\:tw-sticky{position:sticky}.md\:tw-ms-0{margin-inline-start:0px}.md\:tw-hidden{display:none}.md\:tw-h-20{height:5rem}.md\:tw-w-20{width:5rem}.md\:tw-max-w-32{max-width:8rem}.md\:tw-bg-opacity-0{--tw-bg-opacity:0}}@media (min-width: 1024px){.lg\:tw-grid-cols-4{grid-template-columns:repeat(4, minmax(0, 1fr))}}.\[\&\:has\(\[data-item-main-content\]_button\:hover\2c \[data-item-main-content\]_a\:hover\)\]\:tw-cursor-pointer:has([data-item-main-content] button:hover,[data-item-main-content] a:hover){cursor:pointer}.\[\&\:has\(\[data-item-main-content\]_button\:hover\2c \[data-item-main-content\]_a\:hover\)\]\:tw-bg-primary-100:has([data-item-main-content] button:hover,[data-item-main-content] a:hover){--tw-bg-opacity:1;background-color:rgb(var(--color-primary-100) / var(--tw-bg-opacity, 1))}.\[\&\:is\(\:hover\2c \:focus-visible\)\]\:tw-rounded-b-md:is(:hover,:focus-visible){border-bottom-right-radius:0.375rem;border-bottom-left-radius:0.375rem}.\[\&\:is\(\:hover\2c \:focus-visible\)\]\:tw-border-b-transparent:is(:hover,:focus-visible){border-bottom-color:transparent}.\[\&\:is\(input\2c textarea\)\:disabled\]\:tw-bg-secondary-100:is(input,textarea):disabled{--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-100) / var(--tw-bg-opacity, 1))}.\[\&\:not\(\:has\(button\:enabled\)\)\:has\(input\:-moz-read-only\)\]\:tw-bg-secondary-100:not(:has(button:enabled)):has(input:-moz-read-only){--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-100) / var(--tw-bg-opacity, 1))}.\[\&\:not\(\:has\(button\:enabled\)\)\:has\(input\:read-only\)\]\:tw-bg-secondary-100:not(:has(button:enabled)):has(input:read-only){--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-100) / var(--tw-bg-opacity, 1))}.\[\&\:not\(\:has\(button\:enabled\)\)\:has\(textarea\:-moz-read-only\)\]\:tw-bg-secondary-100:not(:has(button:enabled)):has(textarea:-moz-read-only){--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-100) / var(--tw-bg-opacity, 1))}.\[\&\:not\(\:has\(button\:enabled\)\)\:has\(textarea\:read-only\)\]\:tw-bg-secondary-100:not(:has(button:enabled)):has(textarea:read-only){--tw-bg-opacity:1;background-color:rgb(var(--color-secondary-100) / var(--tw-bg-opacity, 1))}.\[\&\:not\(\:indeterminate\)\]\:checked\:before\:tw-mask-image-\[var\(--mask-image\)\]:checked:not(:indeterminate)::before{content:var(--tw-content);-webkit-mask-image:var(--mask-image);mask-image:var(--mask-image)}.\[\&\:not\(\:last-child\)\]\:tw-pe-0:not(:last-child){padding-inline-end:0px}@media (min-width: 768px){.\[\&\:not\(bit-dialog_\*\)\:not\(popup-page_\*\)\]\:md\:tw-mb-12:not(bit-dialog *):not(popup-page *){margin-bottom:3rem}}.\[\&\:not\(bit-form-control_\*\)\]\:focus-visible\:tw-ring-2:focus-visible:not(bit-form-control *){--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)}.\[\&\:not\(bit-form-control_\*\)\]\:focus-visible\:tw-ring-primary-600:focus-visible:not(bit-form-control *){--tw-ring-opacity:1;--tw-ring-color:rgb(var(--color-primary-600) / var(--tw-ring-opacity, 1))}.\[\&\:not\(bit-form-control_\*\)\]\:focus-visible\:tw-ring-offset-2:focus-visible:not(bit-form-control *){--tw-ring-offset-width:2px}.\[\&\:not\(bit-form-control_\*\)\]\:focus-visible\:tw-ring-2.tw-test-focus-visible:not(bit-form-control *){--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)}.\[\&\:not\(bit-form-control_\*\)\]\:focus-visible\:tw-ring-primary-600.tw-test-focus-visible:not(bit-form-control *){--tw-ring-opacity:1;--tw-ring-color:rgb(var(--color-primary-600) / var(--tw-ring-opacity, 1))}.\[\&\:not\(bit-form-control_\*\)\]\:focus-visible\:tw-ring-offset-2.tw-test-focus-visible:not(bit-form-control *){--tw-ring-offset-width:2px}.\[\&\:not\(bit-layout_\*\)\]\:tw-rounded-lg:not(bit-layout *){border-radius:0.5rem}.\[\&\:not\(bit-layout_\*\)\]\:tw-border-b-shadow:not(bit-layout *){--tw-border-opacity:1;border-bottom-color:rgb(var(--color-shadow) / var(--tw-border-opacity, 1))}.tw-bit-compact .bit-compact\:\[\&\:not\(bit-layout_\*\)\]\:tw-rounded-none:not(bit-layout *){border-radius:0px}.tw-bit-compact .bit-compact\:\[\&\:not\(bit-layout_\*\)\]\:first-of-type\:tw-rounded-t-lg:first-of-type:not(bit-layout *){border-top-left-radius:0.5rem;border-top-right-radius:0.5rem}.tw-bit-compact .bit-compact\:\[\&\:not\(bit-layout_\*\)\]\:last-of-type\:tw-rounded-b-lg:last-of-type:not(bit-layout *){border-bottom-right-radius:0.5rem;border-bottom-left-radius:0.5rem}.\[\&\>\*\:\:selection\]\:tw-bg-primary-700>*::-moz-selection{--tw-bg-opacity:1;background-color:rgb(var(--color-primary-700) / var(--tw-bg-opacity, 1))}.\[\&\>\*\:\:selection\]\:tw-bg-primary-700>*::selection{--tw-bg-opacity:1;background-color:rgb(var(--color-primary-700) / var(--tw-bg-opacity, 1))}.\[\&\>\*\:\:selection\]\:tw-text-contrast>*::-moz-selection{--tw-text-opacity:1;color:rgb(var(--color-text-contrast) / var(--tw-text-opacity, 1))}.\[\&\>\*\:\:selection\]\:tw-text-contrast>*::selection{--tw-text-opacity:1;color:rgb(var(--color-text-contrast) / var(--tw-text-opacity, 1))}.\[\&\>\*\:focus-visible\:\:before\]\:\!tw-ring-text-alt2>*:focus-visible::before{--tw-ring-opacity:1 !important;--tw-ring-color:rgb(var(--color-text-alt2) / var(--tw-ring-opacity, 1)) !important}.\[\&\>\*\:hover\]\:\!tw-border-text-alt2>*:hover{--tw-border-opacity:1 !important;border-color:rgb(var(--color-text-alt2) / var(--tw-border-opacity, 1)) !important}.\[\&\>\*\]\:tw-mb-0>*{margin-bottom:0px}.\[\&\>\*\]\:tw-h-full>*{height:100%}.\[\&\>\*\]\:tw-flex-1>*{flex:1 1 0%}.\[\&\>\*\]\:tw-overflow-y-auto>*{overflow-y:auto}.\[\&\>\*\]\:tw-p-0>*{padding:0px}.\[\&\>\*\]\:tw-align-top>*{vertical-align:top}.\[\&\>\*\]\:\!tw-text-alt2>*{--tw-text-opacity:1 !important;color:rgb(var(--color-text-alt2) / var(--tw-text-opacity, 1)) !important}.\[\&\>\*\]\:tw-text-alt2>*{--tw-text-opacity:1;color:rgb(var(--color-text-alt2) / var(--tw-text-opacity, 1))}.\[\&\>\*\]\:tw-text-main>*{--tw-text-opacity:1;color:rgb(var(--color-text-main) / var(--tw-text-opacity, 1))}.\[\&\>\.cdk-virtual-scroll-content-wrapper\]\:\[contain\:layout_style\]>.cdk-virtual-scroll-content-wrapper{contain:layout style}.\[\&\>button\:not\(\[bit-item-content\]\)\]\:after\:tw-content-\[\'\'\]>button:not([bit-item-content])::after{--tw-content:'';content:var(--tw-content)}.\[\&\>button\[bitlink\]\:focus-visible\:before\]\:\!tw-ring-text-main>button[bitlink]:focus-visible:before{--tw-ring-opacity:1 !important;--tw-ring-color:rgb(var(--color-text-main) / var(--tw-ring-opacity, 1)) !important}.\[\&\>button\]\:tw-relative>button{position:relative}.\[\&\>button\]\:after\:tw-absolute>button::after{content:var(--tw-content);position:absolute}.\[\&\>button\]\:after\:tw-bottom-\[-0\.80rem\]>button::after{content:var(--tw-content);bottom:-0.80rem}.\[\&\>button\]\:after\:tw-left-\[-0\.25rem\]>button::after{content:var(--tw-content);left:-0.25rem}.\[\&\>button\]\:after\:tw-right-\[-0\.25rem\]>button::after{content:var(--tw-content);right:-0.25rem}.\[\&\>button\]\:after\:tw-top-\[-0\.8rem\]>button::after{content:var(--tw-content);top:-0.8rem}.\[\&\>button\]\:after\:tw-block>button::after{content:var(--tw-content);display:block}.tw-bit-compact .bit-compact\:\[\&\>button\]\:after\:tw-bottom-\[-0\.7rem\]>button::after{content:var(--tw-content);bottom:-0.7rem}.tw-bit-compact .bit-compact\:\[\&\>button\]\:after\:tw-top-\[-0\.7rem\]>button::after{content:var(--tw-content);top:-0.7rem}.\[\&\>img\]\:tw-block>img{display:block}.\[\&\>label\:hover\]\:tw-border-2>label:hover{border-width:2px}.\[\&\>label\:hover\]\:checked\:tw-border-primary-700:checked>label:hover{--tw-border-opacity:1;border-color:rgb(var(--color-primary-700) / var(--tw-border-opacity, 1))}.\[\&\>label\:hover\]\:checked\:tw-bg-primary-700:checked>label:hover{--tw-bg-opacity:1;background-color:rgb(var(--color-primary-700) / var(--tw-bg-opacity, 1))}.\[\&\>label\:hover\]\:indeterminate\:tw-border-primary-700:indeterminate>label:hover{--tw-border-opacity:1;border-color:rgb(var(--color-primary-700) / var(--tw-border-opacity, 1))}.\[\&\>label\:hover\]\:indeterminate\:tw-bg-primary-700:indeterminate>label:hover{--tw-bg-opacity:1;background-color:rgb(var(--color-primary-700) / var(--tw-bg-opacity, 1))}.\[\&\>label\]\:tw-border-2>label{border-width:2px}.\[\&_bit-form-field\:last-of-type\]\:tw-mb-0 bit-form-field:last-of-type{margin-bottom:0px}.\[\&_bit-hint\]\:tw-mt-0 bit-hint{margin-top:0px}.\[\&_p\]\:tw-mb-0 p{margin-bottom:0px}.\[\&_rect\]\:tw-fill-primary-600 rect{fill:rgb(var(--color-primary-600) / 1)}.\[\&_rect\]\:hover\:tw-fill-text-muted:hover rect{fill:rgb(var(--color-text-muted) / 1)}.\[\&_rect\]\:hover\:tw-fill-text-muted.tw-test-hover rect{fill:rgb(var(--color-text-muted) / 1)}


/*# sourceMappingURL=main.css.map*/