{"version": 3, "file": "popup/main.css", "mappings": "AAAA;EACE,mBAAmB;EACnB;;sEAEyD;EACzD,kBAAkB;EAClB,oBAAoB;AACtB;;ACPA,mDAAmD,mBAAmB,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,uBAAuB,cAAc,CAAC,YAAY,CAAC,6BAA6B,YAAY,CAAC,4BAA4B,YAAY,CAAC,iBAAiB,CAAC,YAAY,CAAC,kBAAkB,iBAAiB,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,YAAY,CAAC,cAAc,CAAC,eAAe,CAAC,YAAY,CAAC,sBAAsB,iBAAiB,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,yCAAyC,CAAC,SAAS,CAAC,yBAAyB,CAAC,YAAY,CAAC,yDAAyD,CAAC,+BAA+B,sBAAsB,uBAAuB,CAAC,CAAC,8BAA8B,SAAS,CAAC,8BAA8B,8BAA8B,UAAU,CAAC,CAAC,2BAA2B,0BAA0B,CAAC,kCAAkC,mDAAmD,CAAC,iBAAiB,CAAC,SAAS,CAAC,2HAA2H,SAAS,CAAC,kBAAkB,CAAC,qCAAqC,eAAe,CAAC,6CAA6C,iBAAiB,CAAC,YAAY,CAAC,qBAAqB,CAAC,aAAa,CAAC,cAAc,CAAC,YAAY,CAAC,wBAAwB,cAAc,CAAC,UAAU,CAAC,iBAAiB;;ACYh4C,WCRR,sBACE,oOAEE,mBAIF,kBACA,mBACA,MAIF,iCAEE,YACA,kBACA,mBACA,oBACA,oBACA,cACA,qBACA,mCAEA,kCACA,SAIF,oBACE,kBACA,SAIF,gBACE,SAGF,wBACE,kBACA,oBACA,SAGF,aACE,SAGF,aACE,SAGF,aACE,WAIF,qCACE,qBAGF,GACE,sBACE,MAEF,wBACE,UAKJ,cACE,2BACA,qBACA,YACA,iBACE,SAIJ,iBACE,qBACA,qBACA,kBACA,kBACA,gBACA,oBACE,iBAKJ,wBACE,wBAwGA,WApGM,wBAoGN,WApGM,yBAoGN,WApGM,sBAoGN,WApGM,kBAoGN,WApGM,qBAoGN,WApGM,qBAoGN,WApGM,yBAoGN,WApGM,qBAoGN,WApGM,mBAoGN,WApGM,iBAoGN,WApGM,sBAoGN,WApGM,oBAoGN,WApGM,0BAoGN,WApGM,mBAoGN,WApGM,iBAoGN,WApGM,mBAoGN,WApGM,mBAoGN,WApGM,mBAoGN,WApGM,iBAoGN,WApGM,wBAoGN,WApGM,+BAoGN,WApGM,mBAoGN,WApGM,oBAoGN,WApGM,wBAoGN,WApGM,sBAoGN,WApGM,2BAoGN,WApGM,wBAoGN,WApGM,wBAoGN,WApGM,sBAoGN,WApGM,mBAoGN,WApGM,kCAoGN,WApGM,2BAoGN,WApGM,uBAoGN,WApGM,iBAoGN,WApGM,oBAoGN,WApGM,uBAoGN,WApGM,kBAoGN,WApGM,mBAoGN,WApGM,oBAoGN,WApGM,oBAoGN,WApGM,sBAoGN,WApGM,mBAoGN,WApGM,qBAoGN,WApGM,qBAoGN,WApGM,yBAoGN,WApGM,oBAoGN,WApGM,iBAoGN,WApGM,sBAoGN,WApGM,kBAoGN,WApGM,4BAoGN,WApGM,oBAoGN,WApGM,kBAoGN,WApGM,oBAoGN,WApGM,0BAoGN,WApGM,oBAoGN,WApGM,iBAoGN,WApGM,yBAoGN,WApGM,2BAoGN,WApGM,uBAoGN,WApGM,qBAoGN,WApGM,2BAoGN,WApGM,oBAoGN,WApGM,yBAoGN,WApGM,kBAoGN,WApGM,oBAoGN,WApGM,sBAoGN,WApGM,oBAoGN,WApGM,6BAoGN,WApGM,qBAoGN,WApGM,oBAoGN,WApGM,kBAoGN,WApGM,mBAoGN,WApGM,qBAoGN,WApGM,sBAoGN,WApGM,qBAoGN,WApGM,qBAoGN,WApGM,oBAoGN,WApGM,kBAoGN,WApGM,iBAoGN,WApGM,mBAoGN,WApGM,kBAoGN,WApGM,8BAoGN,WApGM,oBAoGN,WApGM,yBAoGN,WApGM,sBAoGN,WApGM,0BAoGN,WApGM,kBAoGN,WApGM,mBAoGN,WApGM,mBAoGN,WApGM,sBAoGN,WApGM,oBAoGN,WApGM,oBAoGN,WApGM,yBAoGN,WApGM,qBAoGN,WApGM,qBAoGN,WApGM,mBC7ER,aACE,YACA,WACA,wBACA,4BACA,YAIA,wDACE,YADF,wDACE,mBADF,wDACE,gBADF,wDACE,WADF,wDACE,eADF,wDACE,kBADF,yDACE,iBADF,yDACE,cADF,yDACE,wBAMA,yDACE,wBADF,yDACE,+BADF,yDACE,4BADF,yDACE,uBADF,yDACE,2BADF,yDACE,8BADF,yDACE,6BADF,yDACE,0BADF,yDACE,GCxCN,qBACE,UACA,SACA,MAGF,eACE,iBACA,YACA,cAEA,gBACE,cAGF,gBACE,eAGF,gBACE,eAGF,gBACE,gBAGF,gBACE,WACA,YACA,qBAEA,UACE,WAKN,8DCtCyB,eAER,uBAYE,mCD6BjB,MAGF,WACE,YACA,kBACA,mBACA,gBACA,WC5CW,yBAuBM,uBAoJf,UD1HA,yBACA,sBCyHA,UD1HA,yBACA,mBAIJ,8DC7DyB,eAER,mBDmEf,GAGF,kBACE,OAGF,kBAEE,KAGF,WACE,4CAGF,oBACE,6DC4FE,aDzFA,4DCyFA,aDzFA,qICyFA,qDDnFE,mICmFF,sDDnFE,6KCmFF,UD1EA,sBACA,0KCyEA,UD1EA,yBACA,0DAIJ,cC3GiB,+DAFQ,oCAkLrB,kBD3DA,mCC2DA,iBD3DA,sDC2DA,+FDrDA,qDCqDA,0HDrDA,2CAIJ,cAIE,4DC6CE,+FD/CA,2DC+CA,yHD/CA,QAKJ,UACE,eACA,QAGF,cACE,UAGF,eACE,cAGF,WACE,yGAGF,UAGE,YACA,sEAGF,8BAEE,uECgBE,wBDXA,sECWA,wBDXA,2HAIJ,kBAGE,iBACA,8KCGE,6cDKE,6LCLF,wBDKE,oEAKN,WACE,aACA,mFAEA,4BACE,oGCfA,UDkBE,yBACA,4BACA,mGCpBF,UDkBE,yBACA,4BACA,oFAIJ,YACE,cACA,4LAGF,WAEE,oJAGF,MAEE,aACA,8BACA,8KACA,gBACE,2EAIJ,wBACE,mBACA,sFACA,eACE,iBACA,4EAIJ,YACE,mBACA,kBACA,YACA,kFAGF,WACE,0SAGF,WAGE,eACA,qBACA,aACA,mBACA,uBACA,mBACA,YACA,gBACA,q4BC3EA,mCDgFI,WACA,+3BCjFJ,wBDgFI,WACA,wUAIJ,WACE,0BACA,oCACA,+TAGF,eACE,gFAIJ,YACE,mBACA,2EAGF,gBACE,mBACA,gBACA,uBACA,4EAGF,gBACE,WACA,gBACA,kBACA,aACA,iFAEA,iBACE,SACA,UACA,kGCtHF,uDDyHI,iGCzHJ,aDyHI,4GAIJ,UACE,SACA,YACA,0BACA,kBCtSU,6HAqKZ,gEDqII,WACA,4HCtIJ,wBDqII,WACA,wICtIJ,qBD4IM,sDACA,uIC7IN,qBD4IM,cACA,kHAIJ,iBCtTU,aDwTR,mICnJJ,gEDsJM,kICtJN,wBDsJM,wJCtJN,uDD4JM,uJC5JN,aD4JM,uJAIJ,uBACE,gBACA,YACA,WACA,4BACA,oDACA,4DACA,wKCvKJ,qBDyKM,uKCzKN,qBDyKM,4KAMR,cAEE,sLAEA,SACE,mFAIJ,iBACE,UAIJ,gBACE,UAGF,iBACE,WACA,YACA,YACA,2BCrME,wBDuMA,0BCvMA,wBDuMA,2CAKJ,YACE,gCAEE,4BAIJ,iBACE,SACA,SACA,OACA,QACA,gBACA,kBACA,4CC1NE,wBD6NA,2CC7NA,wBD6NA,qCAGF,KACE,gCAGF,YACE,iBACA,yBACA,iDAIJ,YAGE,uBACA,mBACA,YACA,sBACA,YACA,iCAGF,iBAEE,kBACA,QACA,SACA,gCACA,mGC5PE,gDDgQE,iGChQF,gDDgQE,2CAIJ,kBACE,6ECrQA,aDwQE,2ECxQF,aDwQE,8BAMN,UACE,YACA,gBACA,kBACA,qCAGF,UACE,ME1cF,YACE,eACA,WACA,MAGF,YACE,YACA,eACA,MCPF,iBACE,WACA,YAEA,YACE,kBAGF,sBACE,yBACA,aACA,mCFuKA,UEpKE,kCFoKF,aEpKE,oCFoKF,wBE9JE,kBACA,mCF6JF,wBE9JE,qBACA,sCAGF,iBACE,2FAGF,eAEE,mBACA,6QFmJF,gCE3IM,yQF2IN,mCE3IM,+FAKN,gBAEE,oBACA,WACA,mCAGF,gBACE,kBAIJ,oBACE,wBACA,eFpDc,6BEuDd,cFvDc,UEyDZ,qCAGF,cFhEa,iBEkEX,WACA,4DF6GF,uBEzGM,2DFyGN,uBEzGM,mCFyGN,UEnGE,kCFmGF,aEnGE,WAIJ,oBACE,6CAEE,oBACE,WACA,yCAGF,oBACE,kBF/EQ,0DAqKZ,UEjFM,sBACA,yDFgFN,UEjFM,yBACA,gDAGF,gBACE,oBACA,yEF2EN,gCErEU,wEFqEV,mCErEU,+LFqEV,wBE5DQ,4LF4DR,wBE5DQ,+CAIJ,0BACE,iBACA,gEFsDN,sBEnDQ,+DFmDR,yBEnDQ,kEAKF,gBACE,kBACA,iHAIJ,kBAEE,gBACA,uBACA,mDAGF,YACE,YACA,mBACA,qEAEA,WACE,qDAMJ,YACE,iBACA,oBACA,WACA,kBFpJM,kBE4JhB,aACE,iBACA,kBACA,UACA,kBFhKc,eEkKd,mCFGE,mHEIA,WACE,SACA,kDAIJ,4BACE,mEFXA,2BEaE,kEFbF,2BEaE,yCAIJ,4BACE,0DFlBA,2BEoBE,yDFpBF,2BEoBE,wBAIJ,UACE,cACA,WACA,0HF3BA,wBEkCE,uHFlCF,wBEkCE,sBAIJ,eACE,gBACA,2BAGF,oBACE,gBACA,oDAGF,cF5NgB,cE+Nd,WACA,kBACA,sFFrDA,UEwDE,oFFxDF,aEwDE,0EAGF,gBACE,8BAIJ,cF5OgB,aE8Od,YACA,kBACA,+CFpEA,UEuEE,8CFvEF,aEuEE,gCAGF,WACE,iDAIJ,aAEE,gBACA,mFFlFA,UEqFE,iFFrFF,UEqFE,0BAIJ,cFrQgB,2CA4Kd,UE6FE,0CF7FF,aE6FE,yDAIJ,WAEE,iBACA,4BAGF,WACE,YACA,oRAGF,YAOE,mBACA,qBACA,idAEA,iBACE,mEAKF,UACE,mEAGF,cACE,wFAGF,uBAEE,SACA,0HFxIF,aE2II,wHF3IJ,aE2II,kFAKN,iBAEE,4DFlJA,aEuJE,2DFvJF,aEuJE,gKAIJ,gBAIE,oBACA,WACA,mYAEA,cFnVa,cEsVX,cACA,gBACA,2gBFxKF,UE2KI,mgBF3KJ,UE2KI,wPF3KJ,UEiLI,oPFjLJ,aEiLI,wLAIJ,iBACE,UACA,wKAGF,iBACE,oNAEA,cACE,gDAKN,0BACE,sDAEA,iBACE,8CAKF,kBACE,8CAGF,gBACE,2DAEA,cACE,2DAMJ,WACE,4DAGF,UACE,+CAGF,kBACE,yFAIJ,WAEE,WACA,0CACA,iLF3OA,8BE+OI,+KF/OJ,aE+OI,uIAIJ,YACE,yBAIJ,UACE,sBACA,kBF/ZY,gBEiaZ,0CF5PA,qCE+PE,yCF/PF,oBE+PE,kCAIJ,YACE,gBACA,uDAEA,gBACE,eACA,2CAGF,cACE,iBACA,gBACA,YACA,4DFhRF,aEmRI,2DFnRJ,aEmRI,mIFnRJ,qDEyRM,iIFzRN,UEyRM,wGAIJ,yBAaE,0IF1SJ,aEgSM,UACA,wIFjSN,aEgSM,WACA,sJFjSN,aEsSQ,UACA,oJFvSR,aEsSQ,WACA,kDAOR,aACE,iBACA,8EAKF,iBACE,+FAMA,gBACE,+BAKN,WACE,0BACA,iBACA,gDFrUA,UEwUE,+CFxUF,aEwUE,mCAIJ,iBACE,aACA,mBACA,WACA,oDFhVA,qBEmVE,mDFnVF,wBEmVE,oCAIJ,mBACE,wBACA,wGFzVA,aE+VE,sGF/VF,aE+VE,wBAIJ,YACE,uBACA,mBACA,eACA,iBACA,yCFxWA,UE2WE,wCF3WF,aE2WE,mCAGF,cACE,4BAGF,iBFvhBY,gBEyhBV,eACA,4BAIJ,YACE,WACA,gBACA,uBACA,0CAEA,YACE,sBACA,uBACA,mBACA,yBF9hBU,+BEmiBd,YACE,2BACA,mBACA,kBACA,qCAEA,WACE,qCAGF,gBACE,YACA,eFpkBW,cEskBX,WACA,sDFvZF,UE0ZI,qDF1ZJ,UE0ZI,2CAIJ,iBACE,gBACA,iDAEA,eACE,WAMR,oBACE,mBACA,gBACA,uBACA,yEF7aE,yCEubU,wEFvbV,yCEubU,MCzmBd,iBHagB,iBGXd,sBACA,eHDe,kBGGf,eACA,uBH4KE,qBGzKA,oCACA,WACA,sBHuKA,wBGzKA,qBACA,cACA,+BHuKA,qDGlKE,8BHkKF,aGlKE,8BHkKF,qDG5JE,6BH4JF,aG5JE,mBAIJ,gBACE,cACA,4BAGF,cACE,6CHkJA,gCG/IE,sCACA,oBACA,4CH6IF,uDG/IE,4DACA,4CACA,qDH6IF,qDGxII,oDHwIJ,sDGxII,oDHwIJ,qDGlII,mDHkIJ,kCGlII,4BAKN,cACE,UACA,6CH2HA,8BGxHE,sCACA,4CHuHF,uDGxHE,4DACA,gBAIJ,WACE,0BACA,YAGF,aACE,wBACA,cACA,wBAGF,sBAEE,2BACA,oCAEA,yBACE,4BAOF,YACE,wBAKN,aACE,wBACA,gBACA,kBACA,yCHiFE,qBG9EA,wCH8EA,wBG9EA,QAIJ,WACE,yBACA,cACA,2BAIA,UACE,mBACA,cCjHJ,cJMkB,8BA4Kd,mCI3KA,6BJ2KA,mCI3KA,8BJ2KA,mCIrKA,6BJqKA,mCIrKA,6BJqKA,mCI/JA,4BJ+JA,mCI/JA,2BJ+JA,gCIzJA,0BJyJA,mCIzJA,8BJyJA,mCInJA,6BJmJA,mCInJA,gCJmJA,wBI7IA,+BJ6IA,wBI7IA,gCJ6IA,wBIvIA,+BJuIA,wBIvIA,8BJuIA,qBIjIA,6BJiIA,wBIjIA,gCJiIA,qBI3HA,+BJ2HA,qBI3HA,+BJ2HA,wBIrHA,8BJqHA,wBIrHA,6BJqHA,qBI/GA,4BJ+GA,wBI/GA,gCJ+GA,wBIzGA,+BJyGA,wBIzGA,cAIJ,iBACE,uBAGF,eACE,QAGF,cJlFkB,mBIoFhB,mBACA,aAGF,gBACE,cAGF,eACE,YAGF,mBACE,gBAGF,wBACE,aAGF,yDJ1GwB,kBI8GxB,oBACE,iBAGF,aACE,eACA,YACA,cAGF,iBJ5GgB,mBIgHhB,iBACE,YACA,UAGF,4BACE,qBACA,sBACA,qBACA,uBACA,2BACA,iCACA,oBACA,2CAGF,YACE,mBAGF,wBACE,qBACA,YACA,mCJ8BE,aIzBA,kCJyBA,aIzBA,oCJyBA,aInBA,mCJmBA,aInBA,qBAIJ,mBACE,sBACA,mBACA,WACA,YACA,gBACA,qDJSE,wBILE,oDJKF,wBILE,iBAKN,kBACE,cACA,kCJFE,wBIKA,iCJLA,qBIKA,YAIJ,iEACE,WACA,aACA,oBACA,mBAEA,UACE,YACA,YACA,kBAIJ,UACE,YACA,yBAEA,WACE,YACA,WACA,qBAIJ,sBACE,6CAEE,WACE,cACA,mBAKN,YACE,kBACA,uBACA,mBACA,YACA,WACA,WJ/MW,oCA8JT,UIqDA,mCJrDA,aIqDA,gCAIJ,YAEE,aAGF,aACE,YACA,YACA,2BACA,4BACA,8BJnEE,yDIqEA,6BJrEA,yDIqEA,0BAEF,YATF,WAUI,YACA,2BACA,2BAEF,YAdF,WAeI,YACA,2BACA,WAIJ,uBACE,YAGF,WACE,kCAGF,YACE,OAGF,YACE,mBAGE,MACE,wDJpGF,aIgHA,yBACA,uDJjHA,aIgHA,yBACA,2GAIJ,gBAcE,mRAEA,gBACE,kCAKJ,aAEE,mBAKF,GACE,WACE,MAEF,SACE,oBAGJ,mCACE,uEAIF,YACE,uBACA,mBACA,WACA,YACA,WACA,iBACA,4CCtVE,uBACE,oDAEA,uBACE,sDAGF,sBACE,yCAMJ,SACE,+CAGF,SACE,yCAKF,iBACE,8BAIJ,gBACE,mBACA,kBACA,0CAGF,iBACE,UCtCJ,cACE,YACA,WACA,0BAEA,gBACE,yBACA,cAGF,WACE,cACA,iBAGF,aACE,oBAGF,eACE,+BAGF,iBACE,SACA,UACA,gDNwJA,UMrJE,+CNqJF,aMrJE,4DAIA,kBACE,qBACA,WACA,gBACA,kBACA,mBACA,UACA,yEAIJ,oBAEE,2GNoIF,aMjII,yGNiIJ,aMjII,6EASJ,YACE,YACA,2DAGF,aACE,yCAOF,gBACE,yBACA,oBAKN,wBACE,uBAGF,gBACE,kBACA,oBACA,uBAGF,oBACE,iBAGF,eACE,gBACA,sBAEA,eACE,eN3Fc,kCMgGlB,WAIE,WACA,iGAEE,kBNhGY,mDAqKZ,qBM1EA,kDN0EA,wBM1EA,uCAWJ,UAIE,gBACA,yBACA,kBACA,kBACA,+BACA,+BACA,wDNqDE,qBM7DA,uDN6DA,qBM7DA,8DN6DA,mCMjDE,6DNiDF,mCMjDE,kDAIJ,eACE,WCrIF,gDACE,gBACA,WAFF,gDACE,gBACA,WAFF,gDACE,gBACA,WAFF,gDACE,gBACA,WAFF,gDACE,gBACA,4BP+KA,gDOzKA,gBACA,gBACA,2BPuKA,gDOzKA,gBACA,gBACA,4BPuKA,gDOjKA,gBACA,2BPgKA,gDOjKA,gBACA,YCqBJ,WApBmB,kDAuBf,wBACE,wDACA,eA3BiB,4DA8BjB,QACE,yEACA,uBACA,kEACA,wEACE,gEAKJ,yBACE,yBACA,kEAIF,yBACE,6BACA,mEAIF,4BACE,4BACA,iEAIF,wBACE,4BACA,0EAKJ,0CA7EkB,gBAYC,oDAuEnB,gDAjFsB,0CAqFxB,YACE,iCAEF,iCAzFuB,8CAIV,oBADW,YA0FtB,YAhFe,mBAkFf,uCACA,eArFmB,qDAwFnB,kBACE,yBACA,+DA3GK,kBAoBoB,eA0FvB,qEAEF,kCA9FkB,+DAkGlB,eACE,qEACA,iCAhHiB,kDAuHrB,WA1Ge,sEA4Gb,YACE,YACA,gFACA,MACE,kBA/GqB,mBAiHrB,0FArIC,kBAoBoB,kBAoHnB,qGAQN,gDAzIoB,iDA2IlB,qHACA,aACE,wEAKJ,WACE,kFA1JG,cA4JD,kFAEF,cAzIoB,kCAfH,+BAKJ,kBAuJX,uBACA,4FAnKC,cAqKC,gBACA,oGAEF,gDAjKgB,oHAmKd,gBACE,8HA3KH,cA6KK,kBACA,kGAIN,oBACE,gBACA,iGAEF,oBACE,gBACA,uGACA,gDAtJY,sGAyJZ,oCACE,gHA7LH,mCA+LK,kBACA,uGAGJ,mCACE,iHApMH,aAsMK,qCACA,8BAQd,kCA7LsB,kBA+LpB,oBAxMsB,kBA6MtB,8CAJA,iCA9KkB,6BAoLpB,mBACE,uCAzNO,mBA4NL,8BAGJ,UACE,kBACA,wCAjOO,gBAmOL,gBACA,8CAGA,gDApMkB,wCAwMpB,qEACE,mBACA,2BACA,oBAKN,uBACE,8CA1Oa,iDA4Ob,oBA7OwB,gBAQH,OAwOrB,kCACA,WACE,8BAlPsB,iFADP,mBAuPf,kFAGI,6BAzPkB,iEAgQxB,SACE,MACA,8BAlQsB,qHADP,mBAwQf,oFAGI,6BA1QkB,qCAgRxB,QACE,iCAjRsB,iFADP,gBAsRf,oFAGI,gCAxRkB,mEA+RxB,UACE,MACA,6BAjSsB,qHADP,mBAuSf,mFAGI,4BAzSkB,wCA+SxB,uDACE,gBACA,wCAEF,oDACE,gBACA,6CAEF,mBAvTwB,wCACX,0DAyTX,gBACE,iBACA,gBACA,mCA5S6B,eA8S7B,6EACA,cACE,2EAEF,8CA9Ta,0KAiUb,kCArT6B,+BAfd,gBAwUb,wDAGJ,6CA5UW,kCAJU,uBAmVnB,4FACA,8CA5Ua,sKA+Ub,iCAvVmB,wMA0VjB,eACE,yEAGJ,8CAtVa,kCARM,2EAkWnB,qCA1U+B,wEA6U/B,iBACE,kFA5WG,kBA8WD,eACA,sEAGJ,aACE,gBACA,K;ACtXR,qBAAqB,QAAQ,CAAC,kBAAkB,CAAC,UAAU,CAAC,WAAW,CAAC,eAAe,CAAC,SAAS,CAAC,iBAAiB,CAAC,SAAS,CAAC,kBAAkB,CAAC,SAAS,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,MAAM,CAAC,+BAA+B,SAAS,CAAC,OAAO,CCA/P,+BAA+B,WAAW,CAAC,yCAAyC,wBAAwB,CAAC,iCAAiC,CAAC,sBAAsB,CAAC,0BAA0B,CAAC,iDAAiD,wBAAwB,CAAC,iCAAiC,CAAC,mBAAmB,CAAC,yCAAyC,IAAI,CAAC,CAAC,uCAAuC,IAAI,CAAC,CAAC,oDAAoD,8CAA8C,CAAC,0DAA0D,4CAA4C,CCAtmB;;;CAGC,CAED;EACE,YAAY;EACZ,UAAU;EACV,SAAS;EACT,SAAS;AACX,CAEA;EACE,mBAAmB;EACnB,gCAAgC;AAClC,CCdE,oCAAsF,CAAtF,UAAsF,CAAtF,WAAsF,CAAtF,UAAsF,CAAtF,iBAAsF,CAAtF,8LAAsF,CAAtF,kBAAsF,CAAtF,iBAAsF,CAAtF,uEAAsF,CAItF,kDAAiI,CAAjI,qBAAiI,CAAjI,8LAAiI,CAAjI,kCAAiI,CAAjI,uBAAiI,CAAjI,qBAAiI,CAAjI,iFAAiI,CAAjI,qBAAiI,CAAjI,+EAAiI,CAIjI,kDAAiI,CAAjI,oBAAiI,CAAjI,8LAAiI,CAAjI,gCAAiI,CAAjI,sBAAiI,CAAjI,oBAAiI,CAAjI,gFAAiI,CAAjI,qBAAiI,CAAjI,8EAAiI,CAKjI;2CAAA,UAAmC,CAAnC,qBAAmC,CAAnC,8LAAmC,CAKnC;4CAAA,OAAqC,CAArC,qBAAqC,CAArC,8LAAqC,CAKrC;yCAAA,aAAqC,CAArC,oBAAqC,CAArC,8LAAqC,CAIrC,iDAAgI,CAAhI,qBAAgI,CAAhI,8LAAgI,CAAhI,+BAAgI,CAAhI,qBAAgI,CAAhI,oBAAgI,CAAhI,+EAAgI,CAAhI,qBAAgI,CAAhI,8EAAgI,CAIhI,oDAAkI,CAAlI,oBAAkI,CAAlI,8LAAkI,CAAlI,mCAAkI,CAAlI,uBAAkI,CAAlI,sBAAkI,CAAlI,iFAAkI,CAAlI,qBAAkI,CAAlI,gFAAkI,CAKlI;4CAAA,WAAoC,CAApC,qBAAoC,CAApC,8LAAoC,CAKpC;6CAAA,QAAsC,CAAtC,qBAAsC,CAAtC,8LAAsC,CAKtC;0CAAA,YAAoC,CAApC,oBAAoC,CAApC,8LAAoC,CC/CtC;EACE,wBAAwB;EACxB,2BAA2B;EAC3B,uBAAuB;AACzB,CCJA,kJAAkJ,CAElJ,aAAa,CACb;EACE,QAAQ;EACR,SAAS;EACT,gCAAgC;AAClC,CACA;EACE,MAAM;EACN,QAAQ;EACR,WAAW;AACb,CACA;EACE,SAAS;EACT,QAAQ;EACR,WAAW;AACb,CACA;EACE,MAAM;EACN,QAAQ;EACR,WAAW;AACb,CACA;EACE,SAAS;EACT,QAAQ;EACR,WAAW;AACb,CACA;EACE,SAAS;EACT,UAAU;AACZ,CACA;EACE,SAAS;EACT,WAAW;AACb,CACA;EACE,WAAW;EACX,YAAY;AACd,CACA;EACE,YAAY;EACZ,UAAU;AACZ,CAEA,iBAAiB,CACjB;EACE,iBAAiB;AACnB,CACA;EACE,qBAAqB;AACvB,CACA;;EAEE,cAAc;AAChB,CACA;EACE,cAAc;EACd,qBAAqB;AACvB,CACA;EACE,kBAAkB;EAClB,aAAa;EACb,WAAW;EACX,YAAY;EACZ,eAAe;EACf,iBAAiB;EACjB,cAAc;EACd,4BAA4B;EAC5B,kBAAkB;AACpB,CACA;;EAEE,cAAc;EACd,qBAAqB;EACrB,eAAe;EACf,YAAY;AACd,CACA;;yDAEyD,CACzD;EACE,UAAU;EACV,eAAe;EACf,uBAAuB;EACvB,SAAS;AACX,CACA;EACE,oBAAoB;EACpB,eAAe;EACf,eAAe;AACjB,CACA;EACE,sBAAsB;AACxB,CACA;EACE,kBAAkB;EAClB,gBAAgB;EAChB,eAAe;EACf,4BAA4B;EAC5B,YAAY;EACZ,8BAA8B;EAC9B,gCAAgC;EAChC,4BAA4B;EAC5B,qBAAqB;EACrB,4BAA4B;EAC5B,cAAc;AAChB,CACA;EACE,4BAA4B;EAC5B,UAAU;EACV,eAAe;AACjB,CACA,iHAAiH,CACjH;EACE,yDAAmvB;AACrvB,CACA,kHAAkH,CAClH;EACE,yDAAmtB;AACrtB,CACA,2GAA2G,CAC3G;EACE,yDAA+kB;AACjlB,CACA,0HAA0H,CAC1H;EACE,yDAAuzB;AACzzB,CACA;;EAEE,YAAY;EACZ,iBAAiB;EACjB,kBAAkB;AACpB,CACA;;EAEE,UAAU;EACV,iBAAiB;EACjB,kBAAkB;AACpB,CACA;EACE,yBAAyB;EACzB,oBAAoB;AACtB,CACA;EACE,yBAAyB;AAC3B,CACA;EACE,yBAAyB;AAC3B,CACA;EACE,yBAAyB;AAC3B,CACA;EACE,yBAAyB;AAC3B,CACA;EACE,kBAAkB;EAClB,OAAO;EACP,SAAS;EACT,WAAW;EACX,yBAAyB;EACzB,YAAY;AACd,CACA,sBAAsB,CACtB;EACE;IACE,yBAAyB;IACzB,WAAW;EACb;EACA;IACE,aAAa;IACb,WAAW;EACb;AACF,CACA;EACE;IACE,yBAAyB;IACzB,WAAW;EACb;EACA;IACE,aAAa;IACb,WAAW;EACb;AACF,CACA;EACE;IACE,4BAA4B;IAC5B,WAAW;EACb;AACF,CC5LA,mDAAmD,CACnD;EACE,UAAU;EACV,cAAc;EACd,6BAA6B;;EAE7B,mGAAmG;EACnG,2BAA2B;AAC7B,CAEA,yBAAyB,CACzB;EACE,gBAAgB;AAClB,CAEA;EACE,kCAAkC;EAClC,iBAAiB;EACjB,kBAAkB;AACpB,CAEA;EACE,yBAAyB;AAC3B,CC1BA;;EAEE,CACF;EACE,wBAAwB;EACxB,gBAAgB;EAChB,YAAY;EACZ,WAAW;EACX,SAAS;EACT,eAAe;EACf,4BAA4B;EAC5B,mDAAqC;EACrC,2DAA6C;EAC7C,+CAA+C;AACjD,CAEA;EACE,8CAA8C;AAChD,CCVA;EACE,4CAA4C;EAC5C,2BAA2B;;EAE3B,+BAA+B;EAC/B,mCAAmC;EACnC,kCAAkC;EAClC,kCAAkC;EAClC,iCAAiC;;EAEjC,gCAAgC;EAChC,gCAAgC;EAChC,8BAA8B;EAC9B,8BAA8B;;EAE9B,kCAAkC;EAClC,kCAAkC;EAClC,iCAAiC;EACjC,gCAAgC;EAChC,+BAA+B;;EAE/B,6BAA6B;EAC7B,6BAA6B;EAC7B,2BAA2B;;EAE3B,gCAAgC;EAChC,8BAA8B;EAC9B,6BAA6B;;EAE7B,+BAA+B;EAC/B,6BAA6B;EAC7B,6BAA6B;;EAE7B,gCAAgC;EAChC,8BAA8B;EAC9B,4BAA4B;;EAE5B,qCAAqC;EACrC,oCAAoC;;EAEpC,6BAA6B;EAC7B,8BAA8B;;EAE9B,2BAA2B;EAC3B,8BAA8B;EAC9B,kCAAkC;EAClC,8BAA8B;EAC9B,6BAA6B;;EAE7B,iCAAiC;;EAEjC,+BAA+B;AACjC,CAEA;EACE,iDAAiD;AACnD,CAEA;EACE,kDAAkD;EAClD,qBAAqB;;EAErB,4BAA4B;EAC5B,gCAAgC;EAChC,iCAAiC;EACjC,iCAAiC;EACjC,iCAAiC;;EAEjC,6BAA6B;EAC7B,8BAA8B;EAC9B,gCAAgC;EAChC,gCAAgC;;EAEhC,+BAA+B;EAC/B,gCAAgC;EAChC,kCAAkC;EAClC,kCAAkC;EAClC,kCAAkC;;EAElC,4BAA4B;EAC5B,gCAAgC;EAChC,gCAAgC;;EAEhC,6BAA6B;EAC7B,6BAA6B;EAC7B,+BAA+B;;EAE/B,6BAA6B;EAC7B,8BAA8B;EAC9B,gCAAgC;;EAEhC,2BAA2B;EAC3B,6BAA6B;EAC7B,6BAA6B;;EAE7B,mCAAmC;EACnC,qCAAqC;;EAErC,gCAAgC;EAChC,8BAA8B;;EAE9B,8BAA8B;EAC9B,+BAA+B;EAC/B,+BAA+B;EAC/B,8BAA8B;EAC9B,8BAA8B;;EAE9B,mCAAmC;;EAEnC,+BAA+B;AACjC,CAEA;;;EAGE,CACF;EACE,uBAAuB;AACzB,CAEA;;;EAGE,CACF;;EAEE,aAAa;AACf,CAEA;;;EAGE,CACF;EACE,iBAAiB;AACnB,CAEA;;;;EAIE,CACF;;;;;;EAME,wBAAwB;AAC1B,CAEA;EACE,kBAAkB;EAClB,YAAY;EACZ,gBAAgB;AAClB,CCjKA,4CAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,EAAd,mJAAc,CAAd,oDAAc,CAAd,sDAAc,CAAd,sDAAc,CAAd,sDAAc,CCFd,qBDEA,qCAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,uDAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,EAAd,6FAAc,CAAd,oDAAc,CAAd,sDAAc,CAAd,sDAAc,CAAd,sDAAc,CCFd,WDEA,qCAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,qBACA,wBAAoB,CAApB,uDAAoB,EAApB,uDAAoB,EAApB,yDAAoB,EAApB,yDAAoB,EAApB,yDAAoB,EAIlB,oBAAoB,CAElB,yEAAuB,CAGvB,sFAA6G,CAA7G,gBAA6G,CAA7G,kBAA6G,CAA7G,wBAA6G,CAA7G,iBAA6G,CAA7G,0EAA6G,CAA7G,2BAA6G,CAG7G,mFAA2B,CAA3B,2EAA2B,CAG3B,yFAA0B,CAA1B,0EAA0B,CAG5B,6BAA6B,CAC7B;IACE,iFAAiF;EACnF,CApBF,6BAAmB,CAAnB,SAAmB,CAAnB,UAAmB,CAAnB,SAAmB,CAAnB,WAAmB,CAAnB,eAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CAAnB,cAAmB,CAAnB,2CAAmB,CAAnB,2CAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,8BAAmB,CAAnB,8BAAmB,CAAnB,0BAAmB,CAAnB,qBAAmB,CAAnB,qBAAmB,CAAnB,UAAmB,CAAnB,uBAAmB,CAAnB,+BAAmB,CAAnB,mBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,mDAAmB,CAAnB,2BAAmB,CAAnB,iBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,uBAAmB,CAAnB,mBAAmB,CAAnB,mBAAmB,CAAnB,mBAAmB,CAAnB,mBAAmB,CAAnB,6CAAmB,CAAnB,0CAAmB,CAAnB,+BAAmB,CAAnB,4BAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,sBAAmB,CAAnB,kCAAmB,CAAnB,sBAAmB,CAAnB,8BAAmB,CAAnB,qBAAmB,CAAnB,gCAAmB,CAAnB,qBAAmB,CAAnB,4BAAmB,CAAnB,oBAAmB,CAAnB,gCAAmB,CAAnB,qBAAmB,CAAnB,2BAAmB,CAAnB,mBAAmB,CAAnB,4BAAmB,CAAnB,iBAAmB,CAAnB,0BAAmB,CAAnB,oBAAmB,CAAnB,wBAAmB,CAAnB,kBAAmB,CAAnB,0BAAmB,CAAnB,oBAAmB,CAAnB,uCAAmB,CAAnB,+CAAmB,CAAnB,2CAAmB,CAAnB,2CAAmB,CAAnB,8CAAmB,CAAnB,oCAAmB,CAAnB,8BAAmB,CAAnB,sCAAmB,CAAnB,6BAAmB,CAAnB,0BAAmB,CAAnB,8BAAmB,CAAnB,kCAAmB,CAAnB,4BAAmB,CAAnB,6BAAmB,CAAnB,8BAAmB,CAAnB,2BAAmB,CAAnB,8BAAmB,CAAnB,6BAAmB,CAAnB,kCAAmB,CAAnB,sCAAmB,CAAnB,iCAAmB,CAAnB,sCAAmB,CAAnB,+BAAmB,CAAnB,4BAAmB,CAAnB,gCAAmB,CAAnB,2BAAmB,CAAnB,4BAAmB,CAAnB,yBAAmB,CAAnB,4BAAmB,CAAnB,6BAAmB,CAAnB,4BAAmB,CAAnB,+BAAmB,CAAnB,wCAAmB,CAAnB,kCAAmB,CAAnB,mCAAmB,CAAnB,oCAAmB,CAAnB,oCAAmB,CAAnB,uBAAmB,CAAnB,+BAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,0BAAmB,CAAnB,2BAAmB,CAAnB,wBAAmB,CAAnB,2BAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,2BAAmB,CAAnB,wBAAmB,CAAnB,oCAAmB,CAAnB,sCAAmB,CAAnB,gCAAmB,CAAnB,mBAAmB,CAAnB,2BAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,qCAAmB,CAAnB,yBAAmB,CAAnB,qBAAmB,CAAnB,mCAAmB,CAAnB,qBAAmB,CAAnB,uBAAmB,CAAnB,4BAAmB,CAAnB,eAAmB,CAAnB,uBAAmB,CAAnB,aAAmB,CAAnB,wBAAmB,CAAnB,cAAmB,CAAnB,wBAAmB,CAAnB,cAAmB,CAAnB,4BAAmB,CAAnB,WAAmB,CAAnB,wBAAmB,CAAnB,WAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,oBAAmB,CAAnB,oBAAmB,CAAnB,mBAAmB,CAAnB,qBAAmB,CAAnB,qBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,iCAAmB,CAAnB,4BAAmB,CAAnB,0BAAmB,CAAnB,4BAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,6BAAmB,CAAnB,6BAAmB,CAAnB,kCAAmB,CAAnB,iCAAmB,CAAnB,+BAAmB,CAAnB,6BAAmB,CAAnB,6BAAmB,CAAnB,8BAAmB,CAAnB,yCAAmB,CAAnB,8BAAmB,CAAnB,iCAAmB,CAAnB,iBAAmB,CAAnB,qBAAmB,CAAnB,mBAAmB,CAAnB,mBAAmB,CAAnB,mBAAmB,CAAnB,oBAAmB,CAAnB,oBAAmB,CAAnB,oBAAmB,CAAnB,qBAAmB,CAAnB,oBAAmB,CAAnB,oBAAmB,CAAnB,gCAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,gCAAmB,CAAnB,2BAAmB,CAAnB,wBAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,wBAAmB,CAAnB,yBAAmB,CAAnB,6BAAmB,CAAnB,2BAAmB,CAAnB,6BAAmB,CAAnB,2BAAmB,CAAnB,gEAAmB,CAAnB,wCAAmB,CAAnB,qBAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,6BAAmB,CAAnB,4BAAmB,CAAnB,6BAAmB,CAAnB,4BAAmB,CAAnB,4BAAmB,CAAnB,4BAAmB,CAAnB,6BAAmB,CAAnB,gEAAmB,CAAnB,mCAAmB,CAAnB,wCAAmB,CAAnB,qBAAmB,CAAnB,6BAAmB,CAAnB,4BAAmB,CAAnB,mCAAmB,CAAnB,4BAAmB,CAAnB,4BAAmB,CAAnB,sBAAmB,CAAnB,8BAAmB,CAAnB,uBAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,gCAAmB,CAAnB,kCAAmB,CAAnB,4CAAmB,CAAnB,yDAAmB,CAAnB,8LAAmB,CAAnB,2CAAmB,CAAnB,8LAAmB,CAAnB,2CAAmB,CAAnB,8LAAmB,CAAnB,4BAAmB,CAAnB,cAAmB,CAAnB,8LAAmB,CAAnB,4MAAmB,CAAnB,2BAAmB,CAAnB,iCAAmB,CAAnB,2BAAmB,CAAnB,yCAAmB,CAAnB,iCAAmB,CAAnB,2BAAmB,CAAnB,wCAAmB,CAAnB,qBAAmB,CAAnB,gBAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,eAAmB,CAAnB,2BAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,2CAAmB,CAAnB,oBAAmB,CAAnB,eAAmB,CAAnB,iEAAmB,CAAnB,+DAAmB,CAAnB,+BAAmB,CAAnB,kCAAmB,CAAnB,4BAAmB,CAAnB,sCAAmB,CAAnB,kCAAmB,CAAnB,mCAAmB,CAAnB,uCAAmB,CAAnB,4CAAmB,CAAnB,wCAAmB,CAAnB,yCAAmB,CAAnB,iDAAmB,CAAnB,+CAAmB,CAAnB,qBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,kBAAmB,CAAnB,sEAAmB,CAAnB,qDAAmB,CAAnB,8DAAmB,CAAnB,sEAAmB,CAAnB,6DAAmB,CAAnB,sDAAmB,CAAnB,sEAAmB,CAAnB,8DAAmB,CAAnB,uDAAmB,CAAnB,gCAAmB,CAAnB,iCAAmB,CAAnB,+BAAmB,CAAnB,mCAAmB,CAAnB,qCAAmB,CAAnB,mCAAmB,CAAnB,uCAAmB,CAAnB,4CAAmB,CAAnB,4BAAmB,CAAnB,sBAAmB,CAAnB,kBAAmB,CAAnB,wCAAmB,CAAnB,qCAAmB,CAAnB,YAAmB,CAAnB,wCAAmB,CAAnB,wCAAmB,CAAnB,4CAAmB,CAAnB,4BAAmB,CAAnB,wCAAmB,CAAnB,kCAAmB,CAAnB,iCAAmB,CAAnB,kCAAmB,CAAnB,oCAAmB,CAAnB,qCAAmB,CAAnB,mCAAmB,CAAnB,qCAAmB,CAAnB,oCAAmB,CAAnB,4DAAmB,CAAnB,uCAAmB,CAAnB,8DAAmB,CAAnB,yCAAmB,CAAnB,oDAAmB,CAAnB,kCAAmB,CAAnB,iDAAmB,CAAnB,6BAAmB,CAAnB,+CAAmB,CAAnB,4BAAmB,CAAnB,iDAAmB,CAAnB,8BAAmB,CAAnB,8CAAmB,CAAnB,8BAAmB,CAAnB,6CAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,6BAAmB,CAAnB,6BAAmB,CAAnB,wCAAmB,CAAnB,kCAAmB,CAAnB,sBAAmB,CAAnB,oCAAmB,CAAnB,sBAAmB,CAAnB,iCAAmB,CAAnB,uBAAmB,CAAnB,oCAAmB,CAAnB,sCAAmB,CAAnB,kCAAmB,CAAnB,oCAAmB,CAAnB,mCAAmB,CAAnB,qCAAmB,CAAnB,0CAAmB,CAAnB,iCAAmB,CAAnB,mCAAmB,CAAnB,mCAAmB,CAAnB,gDAAmB,CAAnB,mCAAmB,CAAnB,iCAAmB,CAAnB,2DAAmB,CAAnB,qFAAmB,CAAnB,sCAAmB,CAAnB,qDAAmB,CAAnB,2CAAmB,CAAnB,uEAAmB,CAAnB,2CAAmB,CAAnB,uEAAmB,CAAnB,yCAAmB,CAAnB,qEAAmB,CAAnB,iDAAmB,CAAnB,6EAAmB,CAAnB,4CAAmB,CAAnB,wEAAmB,CAAnB,4CAAmB,CAAnB,wEAAmB,CAAnB,8CAAmB,CAAnB,0EAAmB,CAAnB,8CAAmB,CAAnB,0EAAmB,CAAnB,+EAAmB,CAAnB,8CAAmB,CAAnB,0EAAmB,CAAnB,8CAAmB,CAAnB,0EAAmB,CAAnB,8CAAmB,CAAnB,0EAAmB,CAAnB,4CAAmB,CAAnB,wEAAmB,CAAnB,2CAAmB,CAAnB,uEAAmB,CAAnB,+CAAmB,CAAnB,4CAAmB,CAAnB,wEAAmB,CAAnB,gDAAmB,CAAnB,+EAAmB,CAAnB,gFAAmB,CAAnB,6CAAmB,CAAnB,8EAAmB,CAAnB,6CAAmB,CAAnB,8EAAmB,CAAnB,2CAAmB,CAAnB,4EAAmB,CAAnB,gDAAmB,CAAnB,iFAAmB,CAAnB,yCAAmB,CAAnB,0EAAmB,CAAnB,8CAAmB,CAAnB,+EAAmB,CAAnB,wDAAmB,CAAnB,8CAAmB,CAAnB,+EAAmB,CAAnB,8CAAmB,CAAnB,4EAAmB,CAAnB,gDAAmB,CAAnB,8EAAmB,CAAnB,gDAAmB,CAAnB,8EAAmB,CAAnB,gDAAmB,CAAnB,kFAAmB,CAAnB,mCAAmB,CAAnB,uEAAmB,CAAnB,uCAAmB,CAAnB,2EAAmB,CAAnB,wCAAmB,CAAnB,4EAAmB,CAAnB,wCAAmB,CAAnB,4EAAmB,CAAnB,wCAAmB,CAAnB,4EAAmB,CAAnB,8BAAmB,CAAnB,qDAAmB,CAAnB,mCAAmB,CAAnB,uEAAmB,CAAnB,mCAAmB,CAAnB,uEAAmB,CAAnB,iCAAmB,CAAnB,qEAAmB,CAAnB,yCAAmB,CAAnB,6EAAmB,CAAnB,yCAAmB,CAAnB,6EAAmB,CAAnB,oCAAmB,CAAnB,wEAAmB,CAAnB,oCAAmB,CAAnB,wEAAmB,CAAnB,sCAAmB,CAAnB,0EAAmB,CAAnB,sCAAmB,CAAnB,0EAAmB,CAAnB,oCAAmB,CAAnB,wEAAmB,CAAnB,oCAAmB,CAAnB,wEAAmB,CAAnB,sCAAmB,CAAnB,0EAAmB,CAAnB,uEAAmB,CAAnB,mCAAmB,CAAnB,uEAAmB,CAAnB,+CAAmB,CAAnB,oCAAmB,CAAnB,wEAAmB,CAAnB,oCAAmB,CAAnB,wEAAmB,CAAnB,kCAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,+CAAmB,CAAnB,yDAAmB,CAAnB,2DAAmB,CAAnB,yDAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,uBAAmB,CAAnB,2DAAmB,CAAnB,2DAAmB,CAAnB,+DAAmB,CAAnB,+DAAmB,CAAnB,6DAAmB,CAAnB,+DAAmB,CAAnB,6DAAmB,CAAnB,mEAAmB,CAAnB,2DAAmB,CAAnB,gCAAmB,CAAnB,mBAAmB,CAAnB,2BAAmB,CAAnB,uBAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,oBAAmB,CAAnB,sBAAmB,CAAnB,0CAAmB,CAAnB,gCAAmB,CAAnB,qCAAmB,CAAnB,6BAAmB,CAAnB,yBAAmB,CAAnB,iBAAmB,CAAnB,iCAAmB,CAAnB,sBAAmB,CAAnB,6BAAmB,CAAnB,qBAAmB,CAAnB,4BAAmB,CAAnB,oBAAmB,CAAnB,iCAAmB,CAAnB,sBAAmB,CAAnB,6BAAmB,CAAnB,qBAAmB,CAAnB,0BAAmB,CAAnB,kBAAmB,CAAnB,6BAAmB,CAAnB,qBAAmB,CAAnB,4BAAmB,CAAnB,oBAAmB,CAAnB,wBAAmB,CAAnB,kBAAmB,CAAnB,gCAAmB,CAAnB,uBAAmB,CAAnB,4BAAmB,CAAnB,sBAAmB,CAAnB,gCAAmB,CAAnB,uBAAmB,CAAnB,4BAAmB,CAAnB,qBAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,4BAAmB,CAAnB,sBAAmB,CAAnB,yBAAmB,CAAnB,mBAAmB,CAAnB,4BAAmB,CAAnB,sBAAmB,CAAnB,qCAAmB,CAAnB,2BAAmB,CAAnB,+BAAmB,CAAnB,mCAAmB,CAAnB,4BAAmB,CAAnB,+BAAmB,CAAnB,8BAAmB,CAAnB,iCAAmB,CAAnB,iCAAmB,CAAnB,mCAAmB,CAAnB,kCAAmB,CAAnB,mCAAmB,CAAnB,gCAAmB,CAAnB,yBAAmB,CAAnB,6BAAmB,CAAnB,iCAAmB,CAAnB,6BAAmB,CAAnB,0BAAmB,CAAnB,6BAAmB,CAAnB,6BAAmB,CAAnB,8BAAmB,CAAnB,6BAAmB,CAAnB,iCAAmB,CAAnB,qCAAmB,CAAnB,oCAAmB,CAAnB,qCAAmB,CAAnB,kCAAmB,CAAnB,oCAAmB,CAAnB,kCAAmB,CAAnB,qCAAmB,CAAnB,wBAAmB,CAAnB,4BAAmB,CAAnB,0BAAmB,CAAnB,2BAAmB,CAAnB,4BAAmB,CAAnB,yBAAmB,CAAnB,4BAAmB,CAAnB,2BAAmB,CAAnB,4BAAmB,CAAnB,yBAAmB,CAAnB,6BAAmB,CAAnB,iCAAmB,CAAnB,+BAAmB,CAAnB,0CAAmB,CAAnB,gCAAmB,CAAnB,sCAAmB,CAAnB,sCAAmB,CAAnB,0CAAmB,CAAnB,gCAAmB,CAAnB,4HAAmB,CAAnB,0CAAmB,CAAnB,2BAAmB,CAAnB,2CAAmB,CAAnB,2BAAmB,CAAnB,yCAAmB,CAAnB,6BAAmB,CAAnB,2CAAmB,CAAnB,8BAAmB,CAAnB,2CAAmB,CAAnB,8BAAmB,CAAnB,0CAAmB,CAAnB,8BAAmB,CAAnB,0CAAmB,CAAnB,2BAAmB,CAAnB,6BAAmB,CAAnB,gBAAmB,CAAnB,8BAAmB,CAAnB,gBAAmB,CAAnB,yCAAmB,CAAnB,8BAAmB,CAAnB,8CAAmB,CAAnB,4BAAmB,CAAnB,kBAAmB,CAAnB,8BAAmB,CAAnB,mBAAmB,CAAnB,8BAAmB,CAAnB,mBAAmB,CAAnB,6BAAmB,CAAnB,mBAAmB,CAAnB,6BAAmB,CAAnB,gBAAmB,CAAnB,8CAAmB,CAAnB,6BAAmB,CAAnB,+BAAmB,CAAnB,+BAAmB,CAAnB,iCAAmB,CAAnB,wCAAmB,CAAnB,4BAAmB,CAAnB,gCAAmB,CAAnB,iCAAmB,CAAnB,iCAAmB,CAAnB,iCAAmB,CAAnB,8BAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,+CAAmB,CAAnB,8CAAmB,CAAnB,wEAAmB,CAAnB,kDAAmB,CAAnB,4EAAmB,CAAnB,gDAAmB,CAAnB,yEAAmB,CAAnB,oDAAmB,CAAnB,yEAAmB,CAAnB,oDAAmB,CAAnB,yEAAmB,CAAnB,kDAAmB,CAAnB,uEAAmB,CAAnB,8CAAmB,CAAnB,wEAAmB,CAAnB,+CAAmB,CAAnB,yEAAmB,CAAnB,0DAAmB,CAAnB,+EAAmB,CAAnB,qDAAmB,CAAnB,0EAAmB,CAAnB,qDAAmB,CAAnB,0EAAmB,CAAnB,uDAAmB,CAAnB,4EAAmB,CAAnB,uDAAmB,CAAnB,4EAAmB,CAAnB,qDAAmB,CAAnB,0EAAmB,CAAnB,qDAAmB,CAAnB,0EAAmB,CAAnB,yCAAmB,CAAnB,iCAAmB,CAAnB,6DAAmB,CAAnB,iCAAmB,CAAnB,6DAAmB,CAAnB,qCAAmB,CAAnB,iEAAmB,CAAnB,mCAAmB,CAAnB,8DAAmB,CAAnB,iCAAmB,CAAnB,4DAAmB,CAAnB,iCAAmB,CAAnB,6DAAmB,CAAnB,kCAAmB,CAAnB,8DAAmB,CAAnB,6CAAmB,CAAnB,oEAAmB,CAAnB,wCAAmB,CAAnB,+DAAmB,CAAnB,oCAAmB,CAAnB,+DAAmB,CAAnB,oCAAmB,CAAnB,+DAAmB,CAAnB,uDAAmB,CAAnB,0CAAmB,CAAnB,uEAAmB,CAAnB,qEAAmB,CAAnB,kEAAmB,CAAnB,qEAAmB,CAAnB,uBAAmB,CAAnB,4FAAmB,CAAnB,kGAAmB,CAAnB,sGAAmB,CAAnB,0FAAmB,CAAnB,gGAAmB,CAAnB,sGAAmB,CAAnB,6FAAmB,CAAnB,mGAAmB,CAAnB,sGAAmB,CAAnB,8CAAmB,CAAnB,kBAAmB,CAAnB,mHAAmB,CAAnB,wGAAmB,CAAnB,2FAAmB,CAAnB,qHAAmB,CAAnB,wGAAmB,CAAnB,2FAAmB,CAAnB,oCAAmB,CAAnB,wCAAmB,CAAnB,yEAAmB,CAAnB,sCAAmB,CAAnB,uEAAmB,CAAnB,0CAAmB,CAAnB,2EAAmB,CAAnB,4CAAmB,CAAnB,uEAAmB,CAAnB,8KAAmB,CAAnB,uJAAmB,CAAnB,gLAAmB,CAAnB,uDAAmB,CAAnB,yBAAmB,CAAnB,0CAAmB,CAAnB,uDAAmB,CAAnB,yBAAmB,CAAnB,oHAAmB,CAAnB,uDAAmB,CAAnB,yBAAmB,CAAnB,0CAAmB,CAAnB,uEAAmB,CAJnB,sDA0BA,CA1BA,iBA0BA,CA1BA,4DA0BA,CA1BA,UA0BA,CA1BA,4DA0BA,CA1BA,UA0BA,CA1BA,qDA0BA,CA1BA,SA0BA,CA1BA,2DA0BA,CA1BA,SA0BA,CA1BA,iEA0BA,CA1BA,WA0BA,CA1BA,YA0BA,CA1BA,oEA0BA,CA1BA,aA0BA,CA1BA,gBA0BA,CA1BA,mEA0BA,CA1BA,YA0BA,CA1BA,eA0BA,CA1BA,mDA0BA,CA1BA,aA0BA,CA1BA,0DA0BA,CA1BA,oBA0BA,CA1BA,wDA0BA,CA1BA,oBA0BA,CA1BA,wDA0BA,CA1BA,sBA0BA,CA1BA,kDA0BA,CA1BA,0GA0BA,CA1BA,wGA0BA,CA1BA,2FA0BA,CA1BA,oDA0BA,CA1BA,0GA0BA,CA1BA,wGA0BA,CA1BA,2FA0BA,CA1BA,8DA0BA,CA1BA,2BA0BA,CA1BA,wDA0BA,CA1BA,+JA0BA,CA1BA,uJA0BA,CA1BA,gLA0BA,CA1BA,uDA0BA,CA1BA,yBA0BA,CA1BA,oDA0BA,CA1BA,yBA0BA,CA1BA,oDA0BA,CA1BA,iBA0BA,CA1BA,kDA0BA,CA1BA,QA0BA,CA1BA,wDA0BA,CA1BA,OA0BA,CA1BA,qDA0BA,CA1BA,UA0BA,CA1BA,kDA0BA,CA1BA,UA0BA,CA1BA,iEA0BA,CA1BA,qBA0BA,CA1BA,8LA0BA,CA1BA,4DA0BA,CA1BA,iBA0BA,CA1BA,0EA0BA,CA1BA,kDA0BA,CA1BA,yBA0BA,CA1BA,gDA0BA,CA1BA,2CA0BA,CA1BA,8CA0BA,CA1BA,oDA0BA,CA1BA,0DA0BA,CA1BA,0EA0BA,CA1BA,yDA0BA,CA1BA,uEA0BA,CA1BA,qDA0BA,CA1BA,8CA0BA,CA1BA,6DA0BA,CA1BA,wEA0BA,CA1BA,qDA0BA,CA1BA,wEA0BA,CA1BA,uDA0BA,CA1BA,0EA0BA,CA1BA,6EA0BA,CA1BA,iBA0BA,CA1BA,wEA0BA,CA1BA,+EA0BA,CA1BA,iBA0BA,CA1BA,0EA0BA,CA1BA,uFA0BA,CA1BA,4BA0BA,CA1BA,oBA0BA,CA1BA,wFA0BA,CA1BA,6BA0BA,CA1BA,qBA0BA,CA1BA,yEA0BA,CA1BA,wEA0BA,CA1BA,iEA0BA,CA1BA,wEA0BA,CA1BA,2FA0BA,CA1BA,iBA0BA,CA1BA,0EA0BA,CA1BA,2HA0BA,CA1BA,kDA0BA,CA1BA,0CA0BA,CA1BA,mGA0BA,CA1BA,4BA0BA,CA1BA,oBA0BA,CA1BA,oGA0BA,CA1BA,6BA0BA,CA1BA,qBA0BA,CA1BA,6CA0BA,CA1BA,oCA0BA,CA1BA,8CA0BA,CA1BA,oDA0BA,CA1BA,4DA0BA,CA1BA,8CA0BA,CA1BA,gDA0BA,CA1BA,iEA0BA,CA1BA,0EA0BA,CA1BA,yDA0BA,CA1BA,0EA0BA,CA1BA,yDA0BA,CA1BA,0EA0BA,CA1BA,kEA0BA,CA1BA,yEA0BA,CA1BA,0EA0BA,CA1BA,4EA0BA,CA1BA,6DA0BA,CA1BA,6CA0BA,CA1BA,kFA0BA,CA1BA,0EA0BA,CA1BA,kFA0BA,CA1BA,0EA0BA,CA1BA,uEA0BA,CA1BA,uEA0BA,CA1BA,0EA0BA,CA1BA,0EA0BA,CA1BA,kGA0BA,CA1BA,iBA0BA,CA1BA,0EA0BA,CA1BA,+FA0BA,CA1BA,iBA0BA,CA1BA,uEA0BA,CA1BA,8FA0BA,CA1BA,0EA0BA,CA1BA,sFA0BA,CA1BA,0EA0BA,CA1BA,2GA0BA,CA1BA,iBA0BA,CA1BA,uEA0BA,CA1BA,gHA0BA,CA1BA,8BA0BA,CA1BA,+FA0BA,CA1BA,4GA0BA,CA1BA,4BA0BA,CA1BA,wGA0BA,CA1BA,+GA0BA,CA1BA,sBA0BA,CA1BA,+GA0BA,CA1BA,gHA0BA,CA1BA,8GA0BA,CA1BA,uHA0BA,CA1BA,wEA0BA,CA1BA,yHA0BA,CA1BA,kBA0BA,CA1BA,8GA0BA,CA1BA,gGA0BA,CA1BA,wGA0BA,CA1BA,uEA0BA,CA1BA,yGA0BA,CA1BA,wEA0BA,CA1BA,kHA0BA,CA1BA,iGA0BA,CA1BA,iEA0BA,CA1BA,mFA0BA,CA1BA,0JA0BA,CA1BA,wEA0BA,CA1BA,8HA0BA,CA1BA,gHA0BA,CA1BA,yGA0BA,CA1BA,wEA0BA,CA1BA,iGA0BA,CA1BA,wEA0BA,CA1BA,+GA0BA,CA1BA,4EA0BA,CA1BA,4FA0BA,CA1BA,qHA0BA,CA1BA,wEA0BA,CA1BA,6GA0BA,CA1BA,wEA0BA,CA1BA,2HA0BA,CA1BA,4EA0BA,CA1BA,uHA0BA,CA1BA,kBA0BA,CA1BA,4LA0BA,CA1BA,wGA0BA,CA1BA,2FA0BA,CA1BA,iHA0BA,CA1BA,yEA0BA,CA1BA,qHA0BA,CA1BA,gIA0BA,CA1BA,0EA0BA,CA1BA,sHA0BA,CA1BA,0EA0BA,CA1BA,sIA0BA,CA1BA,0EA0BA,CA1BA,4HA0BA,CA1BA,0EA0BA,CA1BA,mEA0BA,CA1BA,uDA0BA,CA1BA,uEA0BA,CA1BA,qBA0BA,CA1BA,uEA0BA,CA1BA,qBA0BA,CA1BA,kEA0BA,CA1BA,qBA0BA,CA1BA,kEA0BA,CA1BA,qBA0BA,CA1BA,yEA0BA,CA1BA,yEA0BA,CA1BA,oEA0BA,CA1BA,oEA0BA,CA1BA,gKA0BA,CA1BA,wGA0BA,CA1BA,2FA0BA,CA1BA,qFA0BA,CA1BA,yEA0BA,CA1BA,yGA0BA,CA1BA,0LA0BA,CA1BA,0EA0BA,CA1BA,gLA0BA,CA1BA,0EA0BA,CA1BA,gMA0BA,CA1BA,0EA0BA,CA1BA,sLA0BA,CA1BA,0EA0BA,CA1BA,wEA0BA,CA1BA,uEA0BA,CA1BA,uFA0BA,CA1BA,4EA0BA,CA1BA,iJA0BA,CA1BA,mHA0BA,CA1BA,sGA0BA,CA1BA,wEA0BA,CA1BA,yJA0BA,CA1BA,mHA0BA,CA1BA,sGA0BA,CA1BA,gFA0BA,CA1BA,0CA0BA,CA1BA,wEA0BA,CA1BA,qFA0BA,CA1BA,wDA0BA,CA1BA,uEA0BA,CA1BA,wDA0BA,CA1BA,uEA0BA,CA1BA,sDA0BA,CA1BA,qEA0BA,CA1BA,8DA0BA,CA1BA,6EA0BA,CA1BA,yDA0BA,CA1BA,wEA0BA,CA1BA,yDA0BA,CA1BA,wEA0BA,CA1BA,2DA0BA,CA1BA,0EA0BA,CA1BA,2DA0BA,CA1BA,0EA0BA,CA1BA,yDA0BA,CA1BA,wEA0BA,CA1BA,uDA0BA,CA1BA,sEA0BA,CA1BA,2DA0BA,CA1BA,0EA0BA,CA1BA,uDA0BA,CA1BA,sEA0BA,CA1BA,yDA0BA,CA1BA,wEA0BA,CA1BA,2DA0BA,CA1BA,4EA0BA,CA1BA,oDA0BA,CA1BA,2EA0BA,CA1BA,gDA0BA,CA1BA,uEA0BA,CA1BA,8CA0BA,CA1BA,qEA0BA,CA1BA,sDA0BA,CA1BA,6EA0BA,CA1BA,iDA0BA,CA1BA,wEA0BA,CA1BA,wFA0BA,CA1BA,iDA0BA,CA1BA,wEA0BA,CA1BA,iDA0BA,CA1BA,wEA0BA,CA1BA,mDA0BA,CA1BA,0EA0BA,CA1BA,mDA0BA,CA1BA,0EA0BA,CA1BA,mDA0BA,CA1BA,0EA0BA,CA1BA,iDA0BA,CA1BA,wEA0BA,CA1BA,gDA0BA,CA1BA,uEA0BA,CA1BA,4DA0BA,CA1BA,qFA0BA,CA1BA,iDA0BA,CA1BA,wEA0BA,CA1BA,2DA0BA,CA1BA,wEA0BA,CA1BA,4DA0BA,CA1BA,uDA0BA,CA1BA,+DA0BA,CA1BA,4EA0BA,CA1BA,iEA0BA,CA1BA,yEA0BA,CA1BA,2DA0BA,CA1BA,wEA0BA,CA1BA,kEA0BA,CA1BA,0EA0BA,CA1BA,oEA0BA,CA1BA,4EA0BA,CA1BA,8CA0BA,CA1BA,6DA0BA,CA1BA,8CA0BA,CA1BA,6DA0BA,CA1BA,qDA0BA,CA1BA,+DA0BA,CA1BA,yDA0BA,CA1BA,uDA0BA,CA1BA,2DA0BA,CA1BA,4CA0BA,CA1BA,4CA0BA,CA1BA,oDA0BA,CA1BA,2DA0BA,CA1BA,0EA0BA,CA1BA,wEA0BA,CA1BA,kEA0BA,CA1BA,wEA0BA,CA1BA,0FA0BA,CA1BA,iBA0BA,CA1BA,wEA0BA,CA1BA,sFA0BA,CA1BA,wEA0BA,CA1BA,8EA0BA,CA1BA,wEA0BA,CA1BA,2DA0BA,CA1BA,4EA0BA,CA1BA,wEA0BA,CA1BA,8EA0BA,CA1BA,0EA0BA,CA1BA,8EA0BA,CA1BA,0EA0BA,CA1BA,2EA0BA,CA1BA,uEA0BA,CA1BA,+EA0BA,CA1BA,+EA0BA,CA1BA,mEA0BA,CA1BA,uEA0BA,CA1BA,oEA0BA,CA1BA,wEA0BA,CA1BA,sEA0BA,CA1BA,0EA0BA,CA1BA,sEA0BA,CA1BA,0EA0BA,CA1BA,+EA0BA,CA1BA,+EA0BA,CA1BA,yEA0BA,CA1BA,uFA0BA,CA1BA,4EA0BA,CA1BA,0EA0BA,CA1BA,8EA0BA,CA1BA,+FA0BA,CA1BA,0EA0BA,CA1BA,+FA0BA,CA1BA,0EA0BA,CA1BA,+GA0BA,CA1BA,iBA0BA,CA1BA,0EA0BA,CA1BA,gGA0BA,CA1BA,0EA0BA,CA1BA,wFA0BA,CA1BA,0EA0BA,CA1BA,kDA0BA,CA1BA,gFA0BA,CA1BA,qFA0BA,CA1BA,gEA0BA,CA1BA,uEA0BA,CA1BA,gEA0BA,CA1BA,uEA0BA,CA1BA,8DA0BA,CA1BA,qEA0BA,CA1BA,sEA0BA,CA1BA,6EA0BA,CA1BA,iEA0BA,CA1BA,wEA0BA,CA1BA,iEA0BA,CA1BA,wEA0BA,CA1BA,mEA0BA,CA1BA,0EA0BA,CA1BA,mEA0BA,CA1BA,0EA0BA,CA1BA,iEA0BA,CA1BA,wEA0BA,CA1BA,+DA0BA,CA1BA,sEA0BA,CA1BA,mEA0BA,CA1BA,0EA0BA,CA1BA,+DA0BA,CA1BA,sEA0BA,CA1BA,iEA0BA,CA1BA,wEA0BA,CA1BA,mEA0BA,CA1BA,4EA0BA,CA1BA,4DA0BA,CA1BA,2EA0BA,CA1BA,wDA0BA,CA1BA,uEA0BA,CA1BA,sDA0BA,CA1BA,qEA0BA,CA1BA,8DA0BA,CA1BA,6EA0BA,CA1BA,yDA0BA,CA1BA,wEA0BA,CA1BA,gGA0BA,CA1BA,yDA0BA,CA1BA,wEA0BA,CA1BA,yDA0BA,CA1BA,wEA0BA,CA1BA,2DA0BA,CA1BA,0EA0BA,CA1BA,2DA0BA,CA1BA,0EA0BA,CA1BA,2DA0BA,CA1BA,0EA0BA,CA1BA,yDA0BA,CA1BA,wEA0BA,CA1BA,wDA0BA,CA1BA,uEA0BA,CA1BA,oEA0BA,CA1BA,6FA0BA,CA1BA,yDA0BA,CA1BA,wEA0BA,CA1BA,mEA0BA,CA1BA,wEA0BA,CA1BA,oEA0BA,CA1BA,uDA0BA,CA1BA,uEA0BA,CA1BA,4EA0BA,CA1BA,yEA0BA,CA1BA,yEA0BA,CA1BA,mEA0BA,CA1BA,wEA0BA,CA1BA,0EA0BA,CA1BA,0EA0BA,CA1BA,4EA0BA,CA1BA,4EA0BA,CA1BA,sDA0BA,CA1BA,6DA0BA,CA1BA,sDA0BA,CA1BA,6DA0BA,CA1BA,6DA0BA,CA1BA,+DA0BA,CA1BA,iEA0BA,CA1BA,+DA0BA,CA1BA,mEA0BA,CA1BA,oDA0BA,CA1BA,oDA0BA,CA1BA,4DA0BA,CA1BA,mEA0BA,CA1BA,kFA0BA,CA1BA,wEA0BA,CA1BA,0EA0BA,CA1BA,wEA0BA,CA1BA,kGA0BA,CA1BA,iBA0BA,CA1BA,wEA0BA,CA1BA,8FA0BA,CA1BA,wEA0BA,CA1BA,sFA0BA,CA1BA,wEA0BA,CA1BA,mEA0BA,CA1BA,oFA0BA,CA1BA,wEA0BA,CA1BA,sFA0BA,CA1BA,0EA0BA,CA1BA,sFA0BA,CA1BA,0EA0BA,CA1BA,mFA0BA,CA1BA,uEA0BA,CA1BA,uFA0BA,CA1BA,uFA0BA,CA1BA,2EA0BA,CA1BA,uEA0BA,CA1BA,4EA0BA,CA1BA,wEA0BA,CA1BA,8EA0BA,CA1BA,0EA0BA,CA1BA,8EA0BA,CA1BA,0EA0BA,CA1BA,uFA0BA,CA1BA,uFA0BA,CA1BA,yEA0BA,CA1BA,+FA0BA,CA1BA,4EA0BA,CA1BA,kFA0BA,CA1BA,sFA0BA,CA1BA,uGA0BA,CA1BA,0EA0BA,CA1BA,uGA0BA,CA1BA,0EA0BA,CA1BA,uHA0BA,CA1BA,iBA0BA,CA1BA,0EA0BA,CA1BA,wGA0BA,CA1BA,0EA0BA,CA1BA,gGA0BA,CA1BA,0EA0BA,CA1BA,gCA0BA,CA1BA,yDA0BA,CA1BA,wEA0BA,CA1BA,2DA0BA,CA1BA,kBA0BA,CA1BA,kIA0BA,CA1BA,wGA0BA,CA1BA,2FA0BA,CA1BA,iDA0BA,CA1BA,qDA0BA,CA1BA,yEA0BA,CA1BA,sEA0BA,CA1BA,wEA0BA,CA1BA,8EA0BA,CA1BA,wEA0BA,CA1BA,wCA0BA,CA1BA,iEA0BA,CA1BA,wEA0BA,CA1BA,mEA0BA,CA1BA,kBA0BA,CA1BA,0IA0BA,CA1BA,wGA0BA,CA1BA,2FA0BA,CA1BA,yDA0BA,CA1BA,6DA0BA,CA1BA,yEA0BA,CA1BA,8EA0BA,CA1BA,wEA0BA,CA1BA,sFA0BA,CA1BA,wEA0BA,CA1BA,sEA0BA,CA1BA,oDA0BA,CA1BA,8EA0BA,CA1BA,4DA0BA,CA1BA,gDA0BA,CA1BA,gDA0BA,CA1BA,gEA0BA,CA1BA,yEA0BA,CA1BA,wEA0BA,CA1BA,2EA0BA,CA1BA,4EA0BA,CA1BA,kFA0BA,CA1BA,0EA0BA,CA1BA,kEA0BA,CA1BA,4DA0BA,CA1BA,yEA0BA,CA1BA,2EA0BA,CA1BA,2EA0BA,CA1BA,kBA0BA,CA1BA,gJA0BA,CA1BA,wGA0BA,CA1BA,2FA0BA,CA1BA,kJA0BA,CA1BA,wGA0BA,CA1BA,2FA0BA,CA1BA,iEA0BA,CA1BA,qEA0BA,CA1BA,yEA0BA,CA1BA,mEA0BA,CA1BA,uEA0BA,CA1BA,yEA0BA,CA1BA,qHA0BA,CA1BA,mFA0BA,CA1BA,qHA0BA,CA1BA,mHA0BA,CA1BA,sGA0BA,CA1BA,iFA0BA,CA1BA,0GA0BA,CA1BA,wGA0BA,CA1BA,2FA0BA,CA1BA,2FA0BA,CA1BA,mBA0BA,CA1BA,yEA0BA,CA1BA,2FA0BA,CA1BA,mBA0BA,CA1BA,yEA0BA,CA1BA,yFA0BA,CA1BA,mBA0BA,CA1BA,uEA0BA,CA1BA,6FA0BA,CA1BA,mBA0BA,CA1BA,2EA0BA,CA1BA,yFA0BA,CA1BA,mBA0BA,CA1BA,uEA0BA,CA1BA,yFA0BA,CA1BA,iGA0BA,CA1BA,wDA0BA,CA1BA,wDA0BA,CA1BA,wEA0BA,CA1BA,iFA0BA,CA1BA,wEA0BA,CA1BA,mFA0BA,CA1BA,4EA0BA,CA1BA,0FA0BA,CA1BA,0EA0BA,CA1BA,0EA0BA,CA1BA,4DA0BA,CA1BA,iFA0BA,CA1BA,mFA0BA,CA1BA,mFA0BA,CA1BA,kBA0BA,CA1BA,wJA0BA,CA1BA,wGA0BA,CA1BA,2FA0BA,CA1BA,0JA0BA,CA1BA,wGA0BA,CA1BA,2FA0BA,CA1BA,yEA0BA,CA1BA,6EA0BA,CA1BA,yEA0BA,CA1BA,2EA0BA,CA1BA,uEA0BA,CA1BA,iFA0BA,CA1BA,6HA0BA,CA1BA,2FA0BA,CA1BA,qHA0BA,CA1BA,mHA0BA,CA1BA,sGA0BA,CA1BA,yFA0BA,CA1BA,0GA0BA,CA1BA,wGA0BA,CA1BA,2FA0BA,CA1BA,mGA0BA,CA1BA,mBA0BA,CA1BA,yEA0BA,CA1BA,mGA0BA,CA1BA,mBA0BA,CA1BA,yEA0BA,CA1BA,iGA0BA,CA1BA,mBA0BA,CA1BA,uEA0BA,CA1BA,qGA0BA,CA1BA,mBA0BA,CA1BA,2EA0BA,CA1BA,iGA0BA,CA1BA,mBA0BA,CA1BA,uEA0BA,CA1BA,iGA0BA,CA1BA,yGA0BA,CA1BA,+CA0BA,CA1BA,uDA0BA,CA1BA,2DA0BA,CA1BA,wDA0BA,CA1BA,mDA0BA,CA1BA,yDA0BA,CA1BA,oBA0BA,CA1BA,yDA0BA,CA1BA,sBA0BA,CA1BA,6DA0BA,CA1BA,uBA0BA,CA1BA,yDA0BA,CA1BA,sBA0BA,CA1BA,0DA0BA,CA1BA,iEA0BA,CA1BA,gLA0BA,CA1BA,uDA0BA,CA1BA,yBA0BA,EA1BA,4DA0BA,CA1BA,iCA0BA,CA1BA,8BA0BA,CA1BA,8BA0BA,CA1BA,2BA0BA,CA1BA,2BA0BA,CA1BA,uCA0BA,CA1BA,+BA0BA,CA1BA,mEA0BA,CA1BA,+BA0BA,CA1BA,uCA0BA,CA1BA,kDA0BA,CA1BA,0EA0BA,CA1BA,uCA0BA,CA1BA,uEA0BA,CA1BA,wBA0BA,CA1BA,gCA0BA,CA1BA,kBA0BA,EA1BA,wDA0BA,CA1BA,oCA0BA,CA1BA,2BA0BA,CA1BA,wBA0BA,CA1BA,uBA0BA,CA1BA,+BA0BA,CA1BA,sCA0BA,EA1BA,8FA0BA,EA1BA,6MA0BA,CA1BA,gNA0BA,CA1BA,wEA0BA,CA1BA,wHA0BA,CA1BA,kCA0BA,CA1BA,4HA0BA,CA1BA,6GA0BA,CA1BA,0EA0BA,CA1BA,iKA0BA,CA1BA,0EA0BA,CA1BA,uJA0BA,CA1BA,0EA0BA,CA1BA,uKA0BA,CA1BA,0EA0BA,CA1BA,6JA0BA,CA1BA,0EA0BA,CA1BA,qJA0BA,CA1BA,oCA0BA,CA1BA,4BA0BA,CA1BA,6EA0BA,CA1BA,kJA0BA,EA1BA,8MA0BA,CA1BA,wGA0BA,CA1BA,2FA0BA,CA1BA,iIA0BA,CA1BA,yEA0BA,CA1BA,qIA0BA,CA1BA,sNA0BA,CA1BA,wGA0BA,CA1BA,2FA0BA,CA1BA,yIA0BA,CA1BA,yEA0BA,CA1BA,6IA0BA,CA1BA,mFA0BA,CA1BA,yFA0BA,CA1BA,0EA0BA,CA1BA,+GA0BA,CA1BA,wJA0BA,CA1BA,8BA0BA,CA1BA,0JA0BA,CA1BA,gCA0BA,CA1BA,+EA0BA,CA1BA,wEA0BA,CA1BA,0EA0BA,CA1BA,wEA0BA,CA1BA,gFA0BA,CA1BA,iEA0BA,CA1BA,2EA0BA,CA1BA,iEA0BA,CA1BA,gHA0BA,CA1BA,kFA0BA,CA1BA,kFA0BA,CA1BA,iFA0BA,CA1BA,wCA0BA,CA1BA,oCA0BA,CA1BA,oCA0BA,CA1BA,iDA0BA,CA1BA,iCA0BA,CA1BA,8CA0BA,CA1BA,4DA0BA,CA1BA,wEA0BA,CA1BA,+CA0BA,CA1BA,6DA0BA,CA1BA,+CA0BA,CA1BA,6DA0BA,CA1BA,iIA0BA,CA1BA,8HA0BA,CA1BA,yBA0BA,CA1BA,0IA0BA,CA1BA,kFA0BA,CA1BA,qDA0BA,CA1BA,2EA0BA,CA1BA,iBA0BA,CA1BA,uFA0BA,CA1BA,eA0BA,CA1BA,qFA0BA,CA1BA,aA0BA,CA1BA,sFA0BA,CA1BA,cA0BA,CA1BA,mFA0BA,CA1BA,WA0BA,CA1BA,wEA0BA,CA1BA,aA0BA,CA1BA,mHA0BA,CA1BA,cA0BA,CA1BA,gHA0BA,CA1BA,WA0BA,CA1BA,wCA0BA,CA1BA,+DA0BA,CA1BA,+FA0BA,CA1BA,wEA0BA,CA1BA,uFA0BA,CA1BA,wEA0BA,CA1BA,2GA0BA,CA1BA,wEA0BA,CA1BA,mGA0BA,CA1BA,wEA0BA,CA1BA,kDA0BA,CA1BA,2FA0BA,CA1BA,iDA0BA,CA1BA,sCA0BA,CA1BA,6EA0BA,CA1BA,wFA0BA,CA1BA,gGA0BA", "sources": ["webpack:///../../libs/angular/src/scss/webfonts.css", "webpack:///../../node_modules/@angular/cdk/overlay-prebuilt.css", "webpack:///./src/popup/scss/popup.scss", "webpack:///../../libs/angular/src/scss/bwicons/styles/style.scss", "webpack:///../../libs/angular/src/scss/icons.scss", "webpack:///./src/popup/scss/base.scss", "webpack:///./src/popup/scss/variables.scss", "webpack:///./src/popup/scss/grid.scss", "webpack:///./src/popup/scss/box.scss", "webpack:///./src/popup/scss/buttons.scss", "webpack:///./src/popup/scss/misc.scss", "webpack:///./src/popup/scss/environment.scss", "webpack:///./src/popup/scss/pages.scss", "webpack:///./src/popup/scss/plugins.scss", "webpack:///../../libs/components/src/multi-select/scss/bw.theme.scss", "webpack:///../../node_modules/@angular/cdk/a11y-prebuilt.css", "webpack:///../../node_modules/@angular/cdk/text-field-prebuilt.css", "webpack:///../../libs/components/src/reset.css", "webpack:///../../libs/components/src/popover/popover.component.css", "webpack:///../../libs/components/src/toast/toast.tokens.css", "webpack:///../../node_modules/ngx-toastr/toastr.css", "webpack:///../../libs/components/src/toast/toastr.css", "webpack:///../../libs/components/src/search/search.component.css", "webpack:///../../libs/components/src/tw-theme.css", "webpack:///./src/popup/scss/tailwind.css", "webpack:///<no source>"], "sourcesContent": ["@font-face {\n  font-family: Roboto;\n  src:\n    url(\"webfonts/roboto.woff2\") format(\"woff2 supports variations\"),\n    url(\"webfonts/roboto.woff2\") format(\"woff2-variations\");\n  font-display: swap;\n  font-weight: 100 900;\n}\n", ".cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed;z-index:1000}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute;z-index:1000}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;display:flex;max-width:100%;max-height:100%;z-index:1000}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);opacity:0;touch-action:manipulation;z-index:1000;transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}@media(prefers-reduced-motion){.cdk-overlay-backdrop{transition-duration:1ms}}.cdk-overlay-backdrop-showing{opacity:1}@media(forced-colors: active){.cdk-overlay-backdrop-showing{opacity:.6}}.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing,.cdk-high-contrast-active .cdk-overlay-transparent-backdrop{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;display:flex;flex-direction:column;min-width:1px;min-height:1px;z-index:1000}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}\n", "@import \"../../../../../libs/angular/src/scss/webfonts.css\";\n@import \"../../../../../libs/angular/src/scss/bwicons/styles/style.scss\";\n@import \"variables.scss\";\n@import \"../../../../../libs/angular/src/scss/icons.scss\";\n@import \"base.scss\";\n@import \"grid.scss\";\n@import \"box.scss\";\n@import \"buttons.scss\";\n@import \"misc.scss\";\n@import \"environment.scss\";\n@import \"pages.scss\";\n@import \"plugins.scss\";\n@import \"@angular/cdk/overlay-prebuilt.css\";\n@import \"../../../../../libs/components/src/multi-select/scss/bw.theme\";\n", "$icomoon-font-family: \"bwi-font\" !default;\n$icomoon-font-path: \"~@bitwarden/angular/src/scss/bwicons/fonts/\" !default;\n\n// New font sheet? Update the font-face information below\n@font-face {\n  font-family: \"#{$icomoon-font-family}\";\n  src:\n    url($icomoon-font-path + \"bwi-font.svg\") format(\"svg\"),\n    url($icomoon-font-path + \"bwi-font.ttf\") format(\"truetype\"),\n    url($icomoon-font-path + \"bwi-font.woff\") format(\"woff\"),\n    url($icomoon-font-path + \"bwi-font.woff2\") format(\"woff2\");\n  font-weight: normal;\n  font-style: normal;\n  font-display: block;\n}\n\n// Base Class\n.bwi {\n  /* use !important to prevent issues with browser extensions that change fonts */\n  font-family: \"#{$icomoon-font-family}\" !important;\n  speak: never;\n  font-style: normal;\n  font-weight: normal;\n  font-variant: normal;\n  text-transform: none;\n  line-height: 1;\n  display: inline-block;\n  /* Better Font Rendering */\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n// Fixed Width Icons\n.bwi-fw {\n  width: calc(18em / 14);\n  text-align: center;\n}\n\n// Sizing Changes\n.bwi-sm {\n  font-size: 0.875em;\n}\n\n.bwi-lg {\n  font-size: calc(4em / 3);\n  line-height: calc(3em / 4);\n  vertical-align: -15%;\n}\n\n.bwi-2x {\n  font-size: 2em;\n}\n\n.bwi-3x {\n  font-size: 3em;\n}\n\n.bwi-4x {\n  font-size: 4em;\n}\n\n// Spin Animations\n.bwi-spin {\n  animation: bwi-spin 2s infinite linear;\n}\n\n@keyframes bwi-spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(359deg);\n  }\n}\n\n// List Icons\n.bwi-ul {\n  padding-left: 0;\n  margin-left: calc(30em / 14);\n  list-style-type: none;\n  > li {\n    position: relative;\n  }\n}\n\n.bwi-li {\n  position: absolute;\n  left: calc(-30em / 14);\n  width: calc(30em / 14);\n  top: calc(2em / 14);\n  text-align: center;\n  &.bwi-lg {\n    left: calc(-30em / 14) + calc(4em / 14);\n  }\n}\n\n// Rotation\n.bwi-rotate-270 {\n  transform: rotate(270deg);\n}\n\n// For new icons - add their glyph name and value to the map below\n$icons: (\n  \"angle-down\": \"\\e900\",\n  \"angle-left\": \"\\e901\",\n  \"angle-right\": \"\\e902\",\n  \"angle-up\": \"\\e903\",\n  \"bell\": \"\\e904\",\n  \"billing\": \"\\e905\",\n  \"bitcoin\": \"\\e906\",\n  \"browser-alt\": \"\\e907\",\n  \"browser\": \"\\e908\",\n  \"brush\": \"\\e909\",\n  \"bug\": \"\\e90a\",\n  \"business\": \"\\e90b\",\n  \"camera\": \"\\e90c\",\n  \"check-circle\": \"\\e90e\",\n  \"check\": \"\\e90f\",\n  \"cli\": \"\\e910\",\n  \"clock\": \"\\e911\",\n  \"close\": \"\\e912\",\n  \"cog-f\": \"\\e913\",\n  \"cog\": \"\\e914\",\n  \"collection\": \"\\e915\",\n  \"collection-shared\": \"\\e916\",\n  \"clone\": \"\\e917\",\n  \"dollar\": \"\\e919\",\n  \"down-solid\": \"\\e91a\",\n  \"download\": \"\\e91b\",\n  \"drag-and-drop\": \"\\e91c\",\n  \"ellipsis-h\": \"\\e91d\",\n  \"ellipsis-v\": \"\\e91e\",\n  \"envelope\": \"\\e91f\",\n  \"error\": \"\\e920\",\n  \"exclamation-triangle\": \"\\e921\",\n  \"external-link\": \"\\e922\",\n  \"eye-slash\": \"\\e923\",\n  \"eye\": \"\\e924\",\n  \"family\": \"\\e925\",\n  \"file-text\": \"\\e926\",\n  \"file\": \"\\e927\",\n  \"files\": \"\\e928\",\n  \"filter\": \"\\e929\",\n  \"folder\": \"\\e92a\",\n  \"generate\": \"\\e92b\",\n  \"globe\": \"\\e92c\",\n  \"hashtag\": \"\\e92d\",\n  \"id-card\": \"\\e92e\",\n  \"info-circle\": \"\\e92f\",\n  \"import\": \"\\e930\",\n  \"key\": \"\\e931\",\n  \"list-alt\": \"\\e933\",\n  \"list\": \"\\e934\",\n  \"lock-encrypted\": \"\\e935\",\n  \"lock-f\": \"\\e936\",\n  \"lock\": \"\\e937\",\n  \"shield\": \"\\e938\",\n  \"minus-circle\": \"\\e939\",\n  \"mobile\": \"\\e93a\",\n  \"msp\": \"\\e93b\",\n  \"sticky-note\": \"\\e93c\",\n  \"numbered-list\": \"\\e93d\",\n  \"paperclip\": \"\\e93e\",\n  \"passkey\": \"\\e93f\",\n  \"pencil-square\": \"\\e940\",\n  \"pencil\": \"\\e941\",\n  \"plus-circle\": \"\\e942\",\n  \"plus\": \"\\e943\",\n  \"popout\": \"\\e944\",\n  \"provider\": \"\\e945\",\n  \"puzzle\": \"\\e946\",\n  \"question-circle\": \"\\e947\",\n  \"refresh\": \"\\e948\",\n  \"search\": \"\\e949\",\n  \"send\": \"\\e94a\",\n  \"share\": \"\\e94b\",\n  \"sign-in\": \"\\e94c\",\n  \"sign-out\": \"\\e94d\",\n  \"sliders\": \"\\e94e\",\n  \"spinner\": \"\\e94f\",\n  \"star-f\": \"\\e950\",\n  \"star\": \"\\e951\",\n  \"tag\": \"\\e952\",\n  \"trash\": \"\\e953\",\n  \"undo\": \"\\e954\",\n  \"universal-access\": \"\\e955\",\n  \"unlock\": \"\\e956\",\n  \"up-down-btn\": \"\\e957\",\n  \"up-solid\": \"\\e958\",\n  \"user-monitor\": \"\\e959\",\n  \"user\": \"\\e95a\",\n  \"users\": \"\\e95b\",\n  \"vault\": \"\\e95c\",\n  \"wireless\": \"\\e95d\",\n  \"wrench\": \"\\e95e\",\n  \"paypal\": \"\\e95f\",\n  \"credit-card\": \"\\e9a2\",\n  \"desktop\": \"\\e9a3\",\n  \"archive\": \"\\e9c1\",\n);\n\n@each $name, $glyph in $icons {\n  .bwi-#{$name}:before {\n    content: $glyph;\n  }\n}\n", "$card-icons-base: \"~@bitwarden/angular/src/billing/images/cards/\" !default;\n$card-icons: (\n  \"visa\": $card-icons-base + \"visa-light.png\",\n  \"amex\": $card-icons-base + \"amex-light.png\",\n  \"diners-club\": $card-icons-base + \"diners_club-light.png\",\n  \"discover\": $card-icons-base + \"discover-light.png\",\n  \"jcb\": $card-icons-base + \"jcb-light.png\",\n  \"maestro\": $card-icons-base + \"maestro-light.png\",\n  \"mastercard\": $card-icons-base + \"mastercard-light.png\",\n  \"union-pay\": $card-icons-base + \"union_pay-light.png\",\n  \"ru-pay\": $card-icons-base + \"ru_pay-light.png\",\n);\n\n$card-icons-dark: (\n  \"visa\": $card-icons-base + \"visa-dark.png\",\n  \"amex\": $card-icons-base + \"amex-dark.png\",\n  \"diners-club\": $card-icons-base + \"diners_club-dark.png\",\n  \"discover\": $card-icons-base + \"discover-dark.png\",\n  \"jcb\": $card-icons-base + \"jcb-dark.png\",\n  \"maestro\": $card-icons-base + \"maestro-dark.png\",\n  \"mastercard\": $card-icons-base + \"mastercard-dark.png\",\n  \"union-pay\": $card-icons-base + \"union_pay-dark.png\",\n  \"ru-pay\": $card-icons-base + \"ru_pay-dark.png\",\n);\n\n.credit-card-icon {\n  display: block; // Resolves the parent container being slightly to big\n  height: 19px;\n  width: 24px;\n  background-size: contain;\n  background-repeat: no-repeat;\n}\n\n@each $name, $url in $card-icons {\n  .card-#{$name} {\n    background-image: url(\"#{$url}\");\n  }\n}\n\n@each $theme in $dark-icon-themes {\n  @each $name, $url in $card-icons-dark {\n    .#{$theme} .card-#{$name} {\n      background-image: url(\"#{$url}\");\n    }\n  }\n}\n", "@import \"variables.scss\";\n\n* {\n  box-sizing: border-box;\n  padding: 0;\n  margin: 0;\n}\n\nhtml {\n  overflow: hidden;\n  min-height: 600px;\n  height: 100%;\n\n  &.body-sm {\n    min-height: 500px;\n  }\n\n  &.body-xs {\n    min-height: 400px;\n  }\n\n  &.body-xxs {\n    min-height: 300px;\n  }\n\n  &.body-3xs {\n    min-height: 240px;\n  }\n\n  &.body-full {\n    min-height: unset;\n    width: 100%;\n    height: 100%;\n\n    & body {\n      width: 100%;\n    }\n  }\n}\n\nhtml,\nbody {\n  font-family: $font-family-sans-serif;\n  font-size: $font-size-base;\n  line-height: $line-height-base;\n  -webkit-font-smoothing: antialiased;\n}\n\nbody {\n  width: 380px;\n  height: 100%;\n  position: relative;\n  min-height: inherit;\n  overflow: hidden;\n  color: $text-color;\n  background-color: $background-color;\n\n  @include themify($themes) {\n    color: themed(\"textColor\");\n    background-color: themed(\"backgroundColor\");\n  }\n}\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-family: $font-family-sans-serif;\n  font-size: $font-size-base;\n  font-weight: normal;\n}\n\np {\n  margin-bottom: 10px;\n}\n\nul,\nol {\n  margin-bottom: 10px;\n}\n\nimg {\n  border: none;\n}\n\na:not(popup-page a, popup-tab-navigation a) {\n  text-decoration: none;\n\n  @include themify($themes) {\n    color: themed(\"primaryColor\");\n  }\n\n  &:hover,\n  &:focus {\n    @include themify($themes) {\n      color: darken(themed(\"primaryColor\"), 6%);\n    }\n  }\n}\n\ninput:not(bit-form-field input, bit-search input),\nselect:not(bit-form-field select),\ntextarea:not(bit-form-field textarea) {\n  @include themify($themes) {\n    color: themed(\"textColor\");\n    background-color: themed(\"inputBackgroundColor\");\n  }\n}\n\ninput,\nselect,\ntextarea,\nbutton:not(bit-chip-select button) {\n  font-size: $font-size-base;\n  font-family: $font-family-sans-serif;\n}\n\ninput[type*=\"date\"] {\n  @include themify($themes) {\n    color-scheme: themed(\"dateInputColorScheme\");\n  }\n}\n\n::-webkit-calendar-picker-indicator {\n  @include themify($themes) {\n    filter: themed(\"webkitCalendarPickerFilter\");\n  }\n}\n\n::-webkit-calendar-picker-indicator:hover {\n  @include themify($themes) {\n    filter: themed(\"webkitCalendarPickerHoverFilter\");\n  }\n  cursor: pointer;\n}\n\nselect {\n  width: 100%;\n  padding: 0.35rem;\n}\n\nbutton {\n  cursor: pointer;\n}\n\ntextarea {\n  resize: vertical;\n}\n\napp-root > div {\n  height: 100%;\n}\n\nmain::-webkit-scrollbar,\ncdk-virtual-scroll-viewport::-webkit-scrollbar,\n.vault-select::-webkit-scrollbar {\n  width: 10px;\n  height: 10px;\n}\n\nmain::-webkit-scrollbar-track,\n.vault-select::-webkit-scrollbar-track {\n  background-color: transparent;\n}\n\ncdk-virtual-scroll-viewport::-webkit-scrollbar-track {\n  @include themify($themes) {\n    background-color: themed(\"backgroundColor\");\n  }\n}\n\nmain::-webkit-scrollbar-thumb,\ncdk-virtual-scroll-viewport::-webkit-scrollbar-thumb,\n.vault-select::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  margin-right: 1px;\n\n  @include themify($themes) {\n    background-color: themed(\"scrollbarColor\");\n  }\n\n  &:hover {\n    @include themify($themes) {\n      background-color: themed(\"scrollbarHoverColor\");\n    }\n  }\n}\n\nheader:not(bit-callout header, bit-dialog header, popup-page header) {\n  height: 44px;\n  display: flex;\n\n  &:not(.no-theme) {\n    border-bottom: 1px solid #000000;\n\n    @include themify($themes) {\n      color: themed(\"headerColor\");\n      background-color: themed(\"headerBackgroundColor\");\n      border-bottom-color: themed(\"headerBorderColor\");\n    }\n  }\n\n  .header-content {\n    display: flex;\n    flex: 1 1 auto;\n  }\n\n  .header-content > .right,\n  .header-content > .right > .right {\n    height: 100%;\n  }\n\n  .left,\n  .right {\n    flex: 1;\n    display: flex;\n    min-width: -webkit-min-content; /* Workaround to Chrome bug */\n    .header-icon {\n      margin-right: 5px;\n    }\n  }\n\n  .right {\n    justify-content: flex-end;\n    align-items: center;\n    app-avatar {\n      max-height: 30px;\n      margin-right: 5px;\n    }\n  }\n\n  .center {\n    display: flex;\n    align-items: center;\n    text-align: center;\n    min-width: 0;\n  }\n\n  .login-center {\n    margin: auto;\n  }\n\n  app-pop-out > button,\n  div > button:not(app-current-account button):not(.home-acc-switcher-btn),\n  div > a {\n    border: none;\n    padding: 0 10px;\n    text-decoration: none;\n    display: flex;\n    flex-direction: row;\n    justify-content: center;\n    align-items: center;\n    height: 100%;\n    white-space: pre;\n\n    &:not(.home-acc-switcher-btn):hover,\n    &:not(.home-acc-switcher-btn):focus {\n      @include themify($themes) {\n        background-color: themed(\"headerBackgroundHoverColor\");\n        color: themed(\"headerColor\");\n      }\n    }\n\n    &[disabled] {\n      opacity: 0.65;\n      cursor: default !important;\n      background-color: inherit !important;\n    }\n\n    i + span {\n      margin-left: 5px;\n    }\n  }\n\n  app-pop-out {\n    display: flex;\n    padding-right: 0.5em;\n  }\n\n  .title {\n    font-weight: bold;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n  }\n\n  .search {\n    padding: 7px 10px;\n    width: 100%;\n    text-align: left;\n    position: relative;\n    display: flex;\n\n    .bwi {\n      position: absolute;\n      top: 15px;\n      left: 20px;\n\n      @include themify($themes) {\n        color: themed(\"headerInputPlaceholderColor\");\n      }\n    }\n\n    input:not(bit-form-field input) {\n      width: 100%;\n      margin: 0;\n      border: none;\n      padding: 5px 10px 5px 30px;\n      border-radius: $border-radius;\n\n      @include themify($themes) {\n        background-color: themed(\"headerInputBackgroundColor\");\n        color: themed(\"headerInputColor\");\n      }\n\n      &::selection {\n        @include themify($themes) {\n          // explicitly set text selection to invert foreground/background\n          background-color: themed(\"headerInputColor\");\n          color: themed(\"headerInputBackgroundColor\");\n        }\n      }\n\n      &:focus {\n        border-radius: $border-radius;\n        outline: none;\n\n        @include themify($themes) {\n          background-color: themed(\"headerInputBackgroundFocusColor\");\n        }\n      }\n\n      &::-webkit-input-placeholder {\n        @include themify($themes) {\n          color: themed(\"headerInputPlaceholderColor\");\n        }\n      }\n      /** make the cancel button visible in both dark/light themes **/\n      &[type=\"search\"]::-webkit-search-cancel-button {\n        -webkit-appearance: none;\n        appearance: none;\n        height: 15px;\n        width: 15px;\n        background-repeat: no-repeat;\n        mask-image: url(\"../images/close-button-white.svg\");\n        -webkit-mask-image: url(\"../images/close-button-white.svg\");\n        @include themify($themes) {\n          background-color: themed(\"headerInputColor\");\n        }\n      }\n    }\n  }\n\n  .left + .search,\n  .left + .sr-only + .search {\n    padding-left: 0;\n\n    .bwi {\n      left: 10px;\n    }\n  }\n\n  .search + .right {\n    margin-left: -10px;\n  }\n}\n\n.content {\n  padding: 15px 5px;\n}\n\napp-root {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  z-index: 980;\n  @include themify($themes) {\n    background-color: themed(\"backgroundColor\");\n  }\n}\n\n// Adds padding on each side of the content if opened in a tab\n@media only screen and (min-width: 601px) {\n  header,\n  main {\n    padding: 0 calc((100% - 500px) / 2);\n  }\n}\n\nmain:not(popup-page main) {\n  position: absolute;\n  top: 44px;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  overflow-y: auto;\n  overflow-x: hidden;\n\n  @include themify($themes) {\n    background-color: themed(\"backgroundColor\");\n  }\n\n  &.no-header {\n    top: 0;\n  }\n\n  &.flex {\n    display: flex;\n    flex-flow: column;\n    height: calc(100% - 44px);\n  }\n}\n\n.center-content,\n.no-items,\n.full-loading-spinner {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  flex-direction: column;\n  flex-grow: 1;\n}\n\n.no-items,\n.full-loading-spinner {\n  text-align: center;\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n\n  .no-items-image {\n    @include themify($themes) {\n      content: url(\"../images/search-desktop\" + themed(\"svgSuffix\"));\n    }\n  }\n\n  .bwi {\n    margin-bottom: 10px;\n\n    @include themify($themes) {\n      color: themed(\"disabledIconColor\");\n    }\n  }\n}\n\n// cdk-virtual-scroll\n.cdk-virtual-scroll-viewport {\n  width: 100%;\n  height: 100%;\n  overflow-y: auto;\n  overflow-x: hidden;\n}\n\n.cdk-virtual-scroll-content-wrapper {\n  width: 100%;\n}\n", "﻿$dark-icon-themes: \"theme_dark\";\n\n$font-family-sans-serif: <PERSON><PERSON>, \"Helvetica Neue\", Helvetica, Arial, sans-serif;\n$font-family-monospace: Menlo, Monaco, Consolas, \"Courier New\", monospace;\n$font-size-base: 16px;\n$font-size-large: 18px;\n$font-size-xlarge: 22px;\n$font-size-xxlarge: 28px;\n$font-size-small: 12px;\n$text-color: #000000;\n$border-color: #f0f0f0;\n$border-color-dark: #ddd;\n$list-item-hover: #fbfbfb;\n$list-icon-color: #767679;\n$disabled-box-opacity: 1;\n$border-radius: 6px;\n$line-height-base: 1.42857143;\n$icon-hover-color: lighten($text-color, 50%);\n\n$mfaTypes: 0, 2, 3, 4, 6;\n\n$gray: #555;\n$gray-light: #777;\n$text-muted: $gray-light;\n\n$brand-primary: #175ddc;\n$brand-danger: #c83522;\n$brand-success: #017e45;\n$brand-info: #555555;\n$brand-warning: #8b6609;\n$brand-primary-accent: #1252a3;\n\n$background-color: #f0f0f0;\n\n$box-background-color: white;\n$box-background-hover-color: $list-item-hover;\n$box-border-color: $border-color;\n$border-color-alt: #c3c5c7;\n\n$button-border-color: darken($border-color-dark, 12%);\n$button-background-color: white;\n$button-color: lighten($text-color, 40%);\n$button-color-primary: darken($brand-primary, 8%);\n$button-color-danger: darken($brand-danger, 10%);\n\n$code-color: #c01176;\n$code-color-dark: #f08dc7;\n\n$themes: (\n  light: (\n    textColor: $text-color,\n    hoverColorTransparent: rgba($text-color, 0.15),\n    borderColor: $border-color-dark,\n    backgroundColor: $background-color,\n    borderColorAlt: $border-color-alt,\n    backgroundColorAlt: #ffffff,\n    scrollbarColor: rgba(100, 100, 100, 0.2),\n    scrollbarHoverColor: rgba(100, 100, 100, 0.4),\n    boxBackgroundColor: $box-background-color,\n    boxBackgroundHoverColor: $box-background-hover-color,\n    boxBorderColor: $box-border-color,\n    tabBackgroundColor: #ffffff,\n    tabBackgroundHoverColor: $list-item-hover,\n    headerColor: #ffffff,\n    headerBackgroundColor: $brand-primary,\n    headerBackgroundHoverColor: rgba(255, 255, 255, 0.1),\n    headerBorderColor: $brand-primary,\n    headerInputBackgroundColor: darken($brand-primary, 8%),\n    headerInputBackgroundFocusColor: darken($brand-primary, 10%),\n    headerInputColor: #ffffff,\n    headerInputPlaceholderColor: lighten($brand-primary, 35%),\n    listItemBackgroundHoverColor: $list-item-hover,\n    disabledIconColor: $list-icon-color,\n    disabledBoxOpacity: $disabled-box-opacity,\n    headingColor: $gray-light,\n    labelColor: $gray-light,\n    mutedColor: $text-muted,\n    totpStrokeColor: $brand-primary,\n    boxRowButtonColor: $brand-primary,\n    boxRowButtonHoverColor: darken($brand-primary, 10%),\n    inputBorderColor: darken($border-color-dark, 7%),\n    inputBackgroundColor: #ffffff,\n    inputPlaceholderColor: lighten($gray-light, 35%),\n    buttonBackgroundColor: $button-background-color,\n    buttonBorderColor: $button-border-color,\n    buttonColor: $button-color,\n    buttonPrimaryColor: $button-color-primary,\n    buttonDangerColor: $button-color-danger,\n    primaryColor: $brand-primary,\n    primaryAccentColor: $brand-primary-accent,\n    dangerColor: $brand-danger,\n    successColor: $brand-success,\n    infoColor: $brand-info,\n    warningColor: $brand-warning,\n    logoSuffix: \"dark\",\n    mfaLogoSuffix: \".png\",\n    passwordNumberColor: #007fde,\n    passwordSpecialColor: #c40800,\n    passwordCountText: #212529,\n    calloutBorderColor: $border-color-dark,\n    calloutBackgroundColor: $box-background-color,\n    toastTextColor: #ffffff,\n    svgSuffix: \"-light.svg\",\n    transparentColor: rgba(0, 0, 0, 0),\n    dateInputColorScheme: light,\n    // https://stackoverflow.com/a/53336754\n    webkitCalendarPickerFilter: invert(46%) sepia(69%) saturate(6397%) hue-rotate(211deg)\n      brightness(85%) contrast(103%),\n    // light has no hover so use same color\n    webkitCalendarPickerHoverFilter: invert(46%) sepia(69%) saturate(6397%) hue-rotate(211deg)\n      brightness(85%) contrast(103%),\n    codeColor: $code-color,\n  ),\n  dark: (\n    textColor: #ffffff,\n    hoverColorTransparent: rgba($text-color, 0.15),\n    borderColor: #161c26,\n    backgroundColor: #161c26,\n    borderColorAlt: #6e788a,\n    backgroundColorAlt: #2f343d,\n    scrollbarColor: #6e788a,\n    scrollbarHoverColor: #8d94a5,\n    boxBackgroundColor: #2f343d,\n    boxBackgroundHoverColor: #3c424e,\n    boxBorderColor: #4c525f,\n    tabBackgroundColor: #2f343d,\n    tabBackgroundHoverColor: #3c424e,\n    headerColor: #ffffff,\n    headerBackgroundColor: #2f343d,\n    headerBackgroundHoverColor: #3c424e,\n    headerBorderColor: #161c26,\n    headerInputBackgroundColor: #3c424e,\n    headerInputBackgroundFocusColor: #4c525f,\n    headerInputColor: #ffffff,\n    headerInputPlaceholderColor: #bac0ce,\n    listItemBackgroundHoverColor: #3c424e,\n    disabledIconColor: #bac0ce,\n    disabledBoxOpacity: 0.5,\n    headingColor: #bac0ce,\n    labelColor: #bac0ce,\n    mutedColor: #bac0ce,\n    totpStrokeColor: #4c525f,\n    boxRowButtonColor: #bac0ce,\n    boxRowButtonHoverColor: #ffffff,\n    inputBorderColor: #4c525f,\n    inputBackgroundColor: #2f343d,\n    inputPlaceholderColor: #bac0ce,\n    buttonBackgroundColor: #3c424e,\n    buttonBorderColor: #4c525f,\n    buttonColor: #bac0ce,\n    buttonPrimaryColor: #6f9df1,\n    buttonDangerColor: #ff8d85,\n    primaryColor: #6f9df1,\n    primaryAccentColor: #6f9df1,\n    dangerColor: #ff8d85,\n    successColor: #52e07c,\n    infoColor: #a4b0c6,\n    warningColor: #ffeb66,\n    logoSuffix: \"white\",\n    mfaLogoSuffix: \"-w.png\",\n    passwordNumberColor: #6f9df1,\n    passwordSpecialColor: #ff8d85,\n    passwordCountText: #ffffff,\n    calloutBorderColor: #4c525f,\n    calloutBackgroundColor: #3c424e,\n    toastTextColor: #1f242e,\n    svgSuffix: \"-dark.svg\",\n    transparentColor: rgba(0, 0, 0, 0),\n    dateInputColorScheme: dark,\n    // https://stackoverflow.com/a/53336754 - must prepend brightness(0) saturate(100%) to dark themed date inputs\n    webkitCalendarPickerFilter: brightness(0) saturate(100%) invert(86%) sepia(19%) saturate(152%)\n      hue-rotate(184deg) brightness(87%) contrast(93%),\n    webkitCalendarPickerHoverFilter: brightness(0) saturate(100%) invert(100%) sepia(0%)\n      saturate(0%) hue-rotate(93deg) brightness(103%) contrast(103%),\n    codeColor: $code-color-dark,\n  ),\n);\n\n@mixin themify($themes: $themes) {\n  @each $theme, $map in $themes {\n    html.theme_#{$theme} & {\n      $theme-map: () !global;\n      @each $key, $submap in $map {\n        $value: map-get(map-get($themes, $theme), \"#{$key}\");\n        $theme-map: map-merge(\n          $theme-map,\n          (\n            $key: $value,\n          )\n        ) !global;\n      }\n      @content;\n      $theme-map: null !global;\n    }\n  }\n}\n\n@function themed($key) {\n  @return map-get($theme-map, $key);\n}\n", ".row {\n  display: flex;\n  margin: 0 -15px;\n  width: 100%;\n}\n\n.col {\n  flex-basis: 0;\n  flex-grow: 1;\n  padding: 0 15px;\n}\n", "@import \"variables.scss\";\n\n.box {\n  position: relative;\n  width: 100%;\n\n  &.first {\n    margin-top: 0;\n  }\n\n  .box-header {\n    margin: 0 10px 5px 10px;\n    text-transform: uppercase;\n    display: flex;\n\n    @include themify($themes) {\n      color: themed(\"headingColor\");\n    }\n  }\n\n  .box-content {\n    @include themify($themes) {\n      background-color: themed(\"backgroundColor\");\n      border-color: themed(\"borderColor\");\n    }\n\n    &.box-content-padded {\n      padding: 10px 15px;\n    }\n\n    &.condensed .box-content-row,\n    .box-content-row.condensed {\n      padding-top: 5px;\n      padding-bottom: 5px;\n    }\n\n    &.no-hover .box-content-row,\n    .box-content-row.no-hover {\n      &:hover,\n      &:focus {\n        @include themify($themes) {\n          background-color: themed(\"boxBackgroundColor\") !important;\n        }\n      }\n    }\n\n    &.single-line .box-content-row,\n    .box-content-row.single-line {\n      padding-top: 10px;\n      padding-bottom: 10px;\n      margin: 5px;\n    }\n\n    &.row-top-padding {\n      padding-top: 10px;\n    }\n  }\n\n  .box-footer {\n    margin: 0 5px 5px 5px;\n    padding: 0 10px 5px 10px;\n    font-size: $font-size-small;\n\n    button.btn {\n      font-size: $font-size-small;\n      padding: 0;\n    }\n\n    button.btn.primary {\n      font-size: $font-size-base;\n      padding: 7px 15px;\n      width: 100%;\n\n      &:hover {\n        @include themify($themes) {\n          border-color: themed(\"borderHoverColor\") !important;\n        }\n      }\n    }\n\n    @include themify($themes) {\n      color: themed(\"mutedColor\");\n    }\n  }\n\n  &.list {\n    margin: 10px 0 20px 0;\n    .box-content {\n      .virtual-scroll-item {\n        display: inline-block;\n        width: 100%;\n      }\n\n      .box-content-row {\n        text-decoration: none;\n        border-radius: $border-radius;\n        // background-color: $background-color;\n\n        @include themify($themes) {\n          color: themed(\"textColor\");\n          background-color: themed(\"boxBackgroundColor\");\n        }\n\n        &.padded {\n          padding-top: 10px;\n          padding-bottom: 10px;\n        }\n\n        &.no-hover {\n          &:hover {\n            @include themify($themes) {\n              background-color: themed(\"boxBackgroundColor\") !important;\n            }\n          }\n        }\n\n        &:hover,\n        &:focus,\n        &.active {\n          @include themify($themes) {\n            background-color: themed(\"listItemBackgroundHoverColor\");\n          }\n        }\n\n        &:focus {\n          border-left: 5px solid #000000;\n          padding-left: 5px;\n\n          @include themify($themes) {\n            border-left-color: themed(\"mutedColor\");\n          }\n        }\n\n        .action-buttons {\n          .row-btn {\n            padding-left: 5px;\n            padding-right: 5px;\n          }\n        }\n\n        .text:not(.no-ellipsis),\n        .detail {\n          white-space: nowrap;\n          overflow: hidden;\n          text-overflow: ellipsis;\n        }\n\n        .row-main {\n          display: flex;\n          min-width: 0;\n          align-items: normal;\n\n          .row-main-content {\n            min-width: 0;\n          }\n        }\n      }\n\n      &.single-line {\n        .box-content-row {\n          display: flex;\n          padding-top: 10px;\n          padding-bottom: 10px;\n          margin: 5px;\n          border-radius: $border-radius;\n        }\n      }\n    }\n  }\n}\n\n.box-content-row {\n  display: block;\n  padding: 5px 10px;\n  position: relative;\n  z-index: 1;\n  border-radius: $border-radius;\n  margin: 3px 5px;\n\n  @include themify($themes) {\n    background-color: themed(\"boxBackgroundColor\");\n  }\n\n  &:last-child {\n    &:before {\n      border: none;\n      height: 0;\n    }\n  }\n\n  &.override-last:last-child:before {\n    border-bottom: 1px solid #000000;\n    @include themify($themes) {\n      border-bottom-color: themed(\"boxBorderColor\");\n    }\n  }\n\n  &.last:last-child:before {\n    border-bottom: 1px solid #000000;\n    @include themify($themes) {\n      border-bottom-color: themed(\"boxBorderColor\");\n    }\n  }\n\n  &:after {\n    content: \"\";\n    display: table;\n    clear: both;\n  }\n\n  &:hover,\n  &:focus,\n  &.active {\n    @include themify($themes) {\n      background-color: themed(\"boxBackgroundHoverColor\");\n    }\n  }\n\n  &.pre {\n    white-space: pre;\n    overflow-x: auto;\n  }\n\n  &.pre-wrap {\n    white-space: pre-wrap;\n    overflow-x: auto;\n  }\n\n  .row-label,\n  label {\n    font-size: $font-size-small;\n    display: block;\n    width: 100%;\n    margin-bottom: 5px;\n\n    @include themify($themes) {\n      color: themed(\"mutedColor\");\n    }\n\n    .sub-label {\n      margin-left: 10px;\n    }\n  }\n\n  .flex-label {\n    font-size: $font-size-small;\n    display: flex;\n    flex-grow: 1;\n    margin-bottom: 5px;\n\n    @include themify($themes) {\n      color: themed(\"mutedColor\");\n    }\n\n    > a {\n      flex-grow: 0;\n    }\n  }\n\n  .text,\n  .detail {\n    display: block;\n    text-align: left;\n\n    @include themify($themes) {\n      color: themed(\"textColor\");\n    }\n  }\n\n  .detail {\n    font-size: $font-size-small;\n\n    @include themify($themes) {\n      color: themed(\"mutedColor\");\n    }\n  }\n\n  .img-right,\n  .txt-right {\n    float: right;\n    margin-left: 10px;\n  }\n\n  .row-main {\n    flex-grow: 1;\n    min-width: 0;\n  }\n\n  &.box-content-row-flex,\n  .box-content-row-flex,\n  &.box-content-row-checkbox,\n  &.box-content-row-link,\n  &.box-content-row-input,\n  &.box-content-row-slider,\n  &.box-content-row-multi {\n    display: flex;\n    align-items: center;\n    word-break: break-all;\n\n    &.box-content-row-word-break {\n      word-break: normal;\n    }\n  }\n\n  &.box-content-row-multi {\n    input:not([type=\"checkbox\"]) {\n      width: 100%;\n    }\n\n    input + label.sr-only + select {\n      margin-top: 5px;\n    }\n\n    > a,\n    > button {\n      padding: 8px 8px 8px 4px;\n      margin: 0;\n\n      @include themify($themes) {\n        color: themed(\"dangerColor\");\n      }\n    }\n  }\n\n  &.box-content-row-multi,\n  &.box-content-row-newmulti {\n    padding-left: 10px;\n  }\n\n  &.box-content-row-newmulti {\n    @include themify($themes) {\n      color: themed(\"primaryColor\");\n    }\n  }\n\n  &.box-content-row-checkbox,\n  &.box-content-row-link,\n  &.box-content-row-input,\n  &.box-content-row-slider {\n    padding-top: 10px;\n    padding-bottom: 10px;\n    margin: 5px;\n\n    label,\n    .row-label {\n      font-size: $font-size-base;\n      display: block;\n      width: initial;\n      margin-bottom: 0;\n\n      @include themify($themes) {\n        color: themed(\"textColor\");\n      }\n    }\n\n    > span {\n      @include themify($themes) {\n        color: themed(\"mutedColor\");\n      }\n    }\n\n    > input {\n      margin: 0 0 0 auto;\n      padding: 0;\n    }\n\n    > * {\n      margin-right: 15px;\n\n      &:last-child {\n        margin-right: 0;\n      }\n    }\n  }\n\n  &.box-content-row-checkbox-left {\n    justify-content: flex-start;\n\n    > input {\n      margin: 0 15px 0 0;\n    }\n  }\n\n  &.box-content-row-input {\n    label {\n      white-space: nowrap;\n    }\n\n    input {\n      text-align: right;\n\n      &[type=\"number\"] {\n        max-width: 50px;\n      }\n    }\n  }\n\n  &.box-content-row-slider {\n    input[type=\"range\"] {\n      height: 10px;\n    }\n\n    input[type=\"number\"] {\n      width: 45px;\n    }\n\n    label {\n      white-space: nowrap;\n    }\n  }\n\n  input:not([type=\"checkbox\"]):not([type=\"radio\"]),\n  textarea {\n    border: none;\n    width: 100%;\n    background-color: transparent !important;\n\n    &::-webkit-input-placeholder {\n      @include themify($themes) {\n        color: themed(\"inputPlaceholderColor\");\n      }\n    }\n\n    &:not([type=\"file\"]):focus {\n      outline: none;\n    }\n  }\n\n  select {\n    width: 100%;\n    border: 1px solid #000000;\n    border-radius: $border-radius;\n    padding: 7px 4px;\n\n    @include themify($themes) {\n      border-color: themed(\"inputBorderColor\");\n    }\n  }\n\n  .action-buttons {\n    display: flex;\n    margin-left: 5px;\n\n    &.action-buttons-fixed {\n      align-self: start;\n      margin-top: 2px;\n    }\n\n    .row-btn {\n      cursor: pointer;\n      padding: 10px 8px;\n      background: none;\n      border: none;\n\n      @include themify($themes) {\n        color: themed(\"boxRowButtonColor\");\n      }\n\n      &:hover,\n      &:focus {\n        @include themify($themes) {\n          color: themed(\"boxRowButtonHoverColor\");\n        }\n      }\n\n      &.disabled,\n      &[disabled] {\n        @include themify($themes) {\n          color: themed(\"disabledIconColor\");\n          opacity: themed(\"disabledBoxOpacity\");\n        }\n\n        &:hover {\n          @include themify($themes) {\n            color: themed(\"disabledIconColor\");\n            opacity: themed(\"disabledBoxOpacity\");\n          }\n        }\n        cursor: default !important;\n      }\n    }\n\n    &.no-pad .row-btn {\n      padding-top: 0;\n      padding-bottom: 0;\n    }\n  }\n\n  &:not(.box-draggable-row) {\n    .action-buttons .row-btn:last-child {\n      margin-right: -3px;\n    }\n  }\n\n  &.box-draggable-row {\n    &.box-content-row-checkbox {\n      input[type=\"checkbox\"] + .drag-handle {\n        margin-left: 10px;\n      }\n    }\n  }\n\n  .drag-handle {\n    cursor: move;\n    padding: 10px 2px 10px 8px;\n    user-select: none;\n\n    @include themify($themes) {\n      color: themed(\"mutedColor\");\n    }\n  }\n\n  &.cdk-drag-preview {\n    position: relative;\n    display: flex;\n    align-items: center;\n    opacity: 0.8;\n\n    @include themify($themes) {\n      background-color: themed(\"boxBackgroundColor\");\n    }\n  }\n\n  select.field-type {\n    margin: 5px 0 0 25px;\n    width: calc(100% - 25px);\n  }\n\n  .row-sub-icon,\n  .row-sub-label + i.bwi {\n    @include themify($themes) {\n      color: themed(\"disabledIconColor\");\n    }\n  }\n\n  .icon {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    min-width: 34px;\n    margin-left: -5px;\n\n    @include themify($themes) {\n      color: themed(\"mutedColor\");\n    }\n\n    &.icon-small {\n      min-width: 25px;\n    }\n\n    img {\n      border-radius: $border-radius;\n      max-height: 20px;\n      max-width: 20px;\n    }\n  }\n\n  .progress {\n    display: flex;\n    height: 5px;\n    overflow: hidden;\n    margin: 5px -15px -10px;\n\n    .progress-bar {\n      display: flex;\n      flex-direction: column;\n      justify-content: center;\n      white-space: nowrap;\n      background-color: $brand-primary;\n    }\n  }\n\n  .radio-group {\n    display: flex;\n    justify-content: flex-start;\n    align-items: center;\n    margin-bottom: 5px;\n\n    input {\n      flex-grow: 0;\n    }\n\n    label {\n      margin: 0 0 0 5px;\n      flex-grow: 1;\n      font-size: $font-size-base;\n      display: block;\n      width: 100%;\n\n      @include themify($themes) {\n        color: themed(\"textColor\");\n      }\n    }\n\n    &.align-start {\n      align-items: start;\n      margin-top: 10px;\n\n      label {\n        margin-top: -4px;\n      }\n    }\n  }\n}\n\n.truncate {\n  display: inline-block;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\nform {\n  .box {\n    .box-content {\n      .box-content-row {\n        &.no-hover {\n          &:hover {\n            @include themify($themes) {\n              background-color: themed(\"transparentColor\") !important;\n            }\n          }\n        }\n      }\n    }\n  }\n}\n", "@import \"variables.scss\";\n\n.btn {\n  border-radius: $border-radius;\n  padding: 7px 15px;\n  border: 1px solid #000000;\n  font-size: $font-size-base;\n  text-align: center;\n  cursor: pointer;\n\n  @include themify($themes) {\n    background-color: themed(\"buttonBackgroundColor\");\n    border-color: themed(\"buttonBorderColor\");\n    color: themed(\"buttonColor\");\n  }\n\n  &.primary {\n    @include themify($themes) {\n      color: themed(\"buttonPrimaryColor\");\n    }\n  }\n\n  &.danger {\n    @include themify($themes) {\n      color: themed(\"buttonDangerColor\");\n    }\n  }\n\n  &.callout-half {\n    font-weight: bold;\n    max-width: 50%;\n  }\n\n  &:hover:not([disabled]) {\n    cursor: pointer;\n\n    @include themify($themes) {\n      background-color: darken(themed(\"buttonBackgroundColor\"), 1.5%);\n      border-color: darken(themed(\"buttonBorderColor\"), 17%);\n      color: darken(themed(\"buttonColor\"), 10%);\n    }\n\n    &.primary {\n      @include themify($themes) {\n        color: darken(themed(\"buttonPrimaryColor\"), 6%);\n      }\n    }\n\n    &.danger {\n      @include themify($themes) {\n        color: darken(themed(\"buttonDangerColor\"), 6%);\n      }\n    }\n  }\n\n  &:focus:not([disabled]) {\n    cursor: pointer;\n    outline: 0;\n\n    @include themify($themes) {\n      background-color: darken(themed(\"buttonBackgroundColor\"), 6%);\n      border-color: darken(themed(\"buttonBorderColor\"), 25%);\n    }\n  }\n\n  &[disabled] {\n    opacity: 0.65;\n    cursor: default !important;\n  }\n\n  &.block {\n    display: block;\n    width: calc(100% - 10px);\n    margin: 0 auto;\n  }\n\n  &.link,\n  &.neutral {\n    border: none !important;\n    background: none !important;\n\n    &:focus {\n      text-decoration: underline;\n    }\n  }\n}\n\n.action-buttons {\n  .btn {\n    &:focus {\n      outline: auto;\n    }\n  }\n}\n\nbutton.box-content-row {\n  display: block;\n  width: calc(100% - 10px);\n  text-align: left;\n  border-color: none;\n\n  @include themify($themes) {\n    background-color: themed(\"boxBackgroundColor\");\n  }\n}\n\nbutton {\n  border: none;\n  background: transparent;\n  color: inherit;\n}\n\n.login-buttons {\n  .btn.block {\n    width: 100%;\n    margin-bottom: 10px;\n  }\n}\n", "@import \"variables.scss\";\n\nsmall,\n.small {\n  font-size: $font-size-small;\n}\n\n.bg-primary {\n  @include themify($themes) {\n    background-color: themed(\"primaryColor\") !important;\n  }\n}\n\n.bg-success {\n  @include themify($themes) {\n    background-color: themed(\"successColor\") !important;\n  }\n}\n\n.bg-danger {\n  @include themify($themes) {\n    background-color: themed(\"dangerColor\") !important;\n  }\n}\n\n.bg-info {\n  @include themify($themes) {\n    background-color: themed(\"infoColor\") !important;\n  }\n}\n\n.bg-warning {\n  @include themify($themes) {\n    background-color: themed(\"warningColor\") !important;\n  }\n}\n\n.text-primary {\n  @include themify($themes) {\n    color: themed(\"primaryColor\") !important;\n  }\n}\n\n.text-success {\n  @include themify($themes) {\n    color: themed(\"successColor\") !important;\n  }\n}\n\n.text-muted {\n  @include themify($themes) {\n    color: themed(\"mutedColor\") !important;\n  }\n}\n\n.text-default {\n  @include themify($themes) {\n    color: themed(\"textColor\") !important;\n  }\n}\n\n.text-danger {\n  @include themify($themes) {\n    color: themed(\"dangerColor\") !important;\n  }\n}\n\n.text-info {\n  @include themify($themes) {\n    color: themed(\"infoColor\") !important;\n  }\n}\n\n.text-warning {\n  @include themify($themes) {\n    color: themed(\"warningColor\") !important;\n  }\n}\n\n.text-center {\n  text-align: center;\n}\n\n.font-weight-semibold {\n  font-weight: 600;\n}\n\np.lead {\n  font-size: $font-size-large;\n  margin-bottom: 20px;\n  font-weight: normal;\n}\n\n.flex-right {\n  margin-left: auto;\n}\n\n.flex-bottom {\n  margin-top: auto;\n}\n\n.no-margin {\n  margin: 0 !important;\n}\n\n.display-block {\n  display: block !important;\n}\n\n.monospaced {\n  font-family: $font-family-monospace;\n}\n\n.show-whitespace {\n  white-space: pre-wrap;\n}\n\n.img-responsive {\n  display: block;\n  max-width: 100%;\n  height: auto;\n}\n\n.img-rounded {\n  border-radius: $border-radius;\n}\n\n.select-index-top {\n  position: relative;\n  z-index: 100;\n}\n\n.sr-only {\n  position: absolute !important;\n  width: 1px !important;\n  height: 1px !important;\n  padding: 0 !important;\n  margin: -1px !important;\n  overflow: hidden !important;\n  clip: rect(0, 0, 0, 0) !important;\n  border: 0 !important;\n}\n\n:not(:focus) > .exists-only-on-parent-focus {\n  display: none;\n}\n\n.password-wrapper {\n  overflow-wrap: break-word;\n  white-space: pre-wrap;\n  min-width: 0;\n}\n\n.password-number {\n  @include themify($themes) {\n    color: themed(\"passwordNumberColor\");\n  }\n}\n\n.password-special {\n  @include themify($themes) {\n    color: themed(\"passwordSpecialColor\");\n  }\n}\n\n.password-character {\n  display: inline-flex;\n  flex-direction: column;\n  align-items: center;\n  width: 30px;\n  height: 36px;\n  font-weight: 600;\n\n  &:nth-child(odd) {\n    @include themify($themes) {\n      background-color: themed(\"backgroundColor\");\n    }\n  }\n}\n\n.password-count {\n  white-space: nowrap;\n  font-size: 8px;\n\n  @include themify($themes) {\n    color: themed(\"passwordCountText\") !important;\n  }\n}\n\n#duo-frame {\n  background: url(\"../images/loading.svg\") 0 0 no-repeat;\n  width: 100%;\n  height: 470px;\n  margin-bottom: -10px;\n\n  iframe {\n    width: 100%;\n    height: 100%;\n    border: none;\n  }\n}\n\n#web-authn-frame {\n  width: 100%;\n  height: 40px;\n\n  iframe {\n    border: none;\n    height: 100%;\n    width: 100%;\n  }\n}\n\nbody.linux-webauthn {\n  width: 485px !important;\n  #web-authn-frame {\n    iframe {\n      width: 375px;\n      margin: 0 55px;\n    }\n  }\n}\n\napp-root > #loading {\n  display: flex;\n  text-align: center;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  width: 100%;\n  color: $text-muted;\n\n  @include themify($themes) {\n    color: themed(\"mutedColor\");\n  }\n}\n\napp-vault-icon,\n.app-vault-icon {\n  display: flex;\n}\n\n.logo-image {\n  margin: 0 auto;\n  width: 142px;\n  height: 21px;\n  background-size: 142px 21px;\n  background-repeat: no-repeat;\n  @include themify($themes) {\n    background-image: url(\"../images/logo-\" + themed(\"logoSuffix\") + \"@2x.png\");\n  }\n  @media (min-width: 219px) {\n    width: 189px;\n    height: 28px;\n    background-size: 189px 28px;\n  }\n  @media (min-width: 314px) {\n    width: 284px;\n    height: 43px;\n    background-size: 284px 43px;\n  }\n}\n\n[hidden] {\n  display: none !important;\n}\n\n.draggable {\n  cursor: move;\n}\n\ninput[type=\"password\"]::-ms-reveal {\n  display: none;\n}\n\n.flex {\n  display: flex;\n\n  &.flex-grow {\n    > * {\n      flex: 1;\n    }\n  }\n}\n\n// Text selection styles\n// Set explicit selection styles (assumes primary accent color has sufficient\n// contrast against the background, so its inversion is also still readable)\n// and suppress user selection for most elements (to make it more app-like)\n\n:not(bit-form-field input)::selection {\n  @include themify($themes) {\n    color: themed(\"backgroundColor\");\n    background-color: themed(\"primaryAccentColor\");\n  }\n}\n\nh1,\nh2,\nh3,\nlabel,\na,\nbutton,\np,\nimg,\n.box-header,\n.box-footer,\n.callout,\n.row-label,\n.modal-title,\n.overlay-container {\n  user-select: none;\n\n  &.user-select {\n    user-select: auto;\n  }\n}\n\n/* tweak for inconsistent line heights in cipher view */\n.box-footer button,\n.box-footer a {\n  line-height: 1;\n}\n\n// Workaround for slow performance on external monitors on Chrome + MacOS\n// See: https://bugs.chromium.org/p/chromium/issues/detail?id=971701#c64\n@keyframes redraw {\n  0% {\n    opacity: 0.99;\n  }\n  100% {\n    opacity: 1;\n  }\n}\nhtml.force_redraw {\n  animation: redraw 1s linear infinite;\n}\n\n/* override for vault icon in browser (pre extension refresh) */\napp-vault-icon:not(app-vault-list-items-container app-vault-icon) > div {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  float: left;\n  height: 36px;\n  width: 34px;\n  margin-left: -5px;\n}\n", "@import \"variables.scss\";\n\nhtml.browser_safari {\n  &.safari_height_fix {\n    body {\n      height: 360px !important;\n\n      &.body-xs {\n        height: 300px !important;\n      }\n\n      &.body-full {\n        height: 100% !important;\n      }\n    }\n  }\n\n  header {\n    .search .bwi {\n      left: 20px;\n    }\n\n    .left + .search .bwi {\n      left: 10px;\n    }\n  }\n\n  .content {\n    &.login-page {\n      padding-top: 100px;\n    }\n  }\n\n  app-root {\n    border-width: 1px;\n    border-style: solid;\n    border-color: #000000;\n  }\n\n  &.theme_light app-root {\n    border-color: #777777;\n  }\n}\n", "@import \"variables.scss\";\n\napp-home {\n  position: fixed;\n  height: 100%;\n  width: 100%;\n\n  .center-content {\n    margin-top: -50px;\n    height: calc(100% + 50px);\n  }\n\n  img {\n    width: 284px;\n    margin: 0 auto;\n  }\n\n  p.lead {\n    margin: 30px 0;\n  }\n\n  .btn + .btn {\n    margin-top: 10px;\n  }\n\n  button.settings-icon {\n    position: absolute;\n    top: 10px;\n    left: 10px;\n\n    @include themify($themes) {\n      color: themed(\"mutedColor\");\n    }\n\n    &:not(:hover):not(:focus) {\n      span {\n        clip: rect(0 0 0 0);\n        clip-path: inset(50%);\n        height: 1px;\n        overflow: hidden;\n        position: absolute;\n        white-space: nowrap;\n        width: 1px;\n      }\n    }\n\n    &:hover,\n    &:focus {\n      text-decoration: none;\n\n      @include themify($themes) {\n        color: themed(\"primaryColor\");\n      }\n    }\n  }\n}\n\nbody.body-sm,\nbody.body-xs {\n  app-home {\n    .center-content {\n      margin-top: 0;\n      height: 100%;\n    }\n\n    p.lead {\n      margin: 15px 0;\n    }\n  }\n}\n\nbody.body-full {\n  app-home {\n    .center-content {\n      margin-top: -80px;\n      height: calc(100% + 80px);\n    }\n  }\n}\n\n.createAccountLink {\n  padding: 30px 10px 0 10px;\n}\n\n.remember-email-check {\n  padding-top: 18px;\n  padding-left: 10px;\n  padding-bottom: 18px;\n}\n\n.login-buttons > button {\n  margin: 15px 0 15px 0;\n}\n\n.useBrowserlink {\n  margin-left: 5px;\n  margin-top: 20px;\n\n  span {\n    font-weight: 700;\n    font-size: $font-size-small;\n  }\n}\n\n.fido2-browser-selector-dropdown {\n  @include themify($themes) {\n    background-color: themed(\"boxBackgroundColor\");\n  }\n  padding: 8px;\n  width: 100%;\n  box-shadow:\n    0 2px 2px 0 rgba(0, 0, 0, 0.14),\n    0 3px 1px -2px rgba(0, 0, 0, 0.12),\n    0 1px 5px 0 rgba(0, 0, 0, 0.2);\n  border-radius: $border-radius;\n}\n\n.fido2-browser-selector-dropdown-item {\n  @include themify($themes) {\n    color: themed(\"textColor\") !important;\n  }\n  width: 100%;\n  text-align: left;\n  padding: 0px 15px 0px 5px;\n  margin-bottom: 5px;\n  border-radius: 3px;\n  border: 1px solid transparent;\n  transition: all 0.2s ease-in-out;\n\n  &:hover {\n    @include themify($themes) {\n      background-color: themed(\"listItemBackgroundHoverColor\") !important;\n    }\n  }\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n", "@import \"variables.scss\";\n\n@each $mfaType in $mfaTypes {\n  .mfaType#{$mfaType} {\n    content: url(\"../images/two-factor/\" + $mfaType + \".png\");\n    max-width: 100px;\n  }\n}\n\n.mfaType1 {\n  @include themify($themes) {\n    content: url(\"../images/two-factor/1\" + themed(\"mfaLogoSuffix\"));\n    max-width: 100px;\n    max-height: 45px;\n  }\n}\n\n.mfaType7 {\n  @include themify($themes) {\n    content: url(\"../images/two-factor/7\" + themed(\"mfaLogoSuffix\"));\n    max-width: 100px;\n  }\n}\n", "// Default theme copied from https://github.com/ng-select/ng-select/blob/master/src/ng-select/themes/default.theme.scss\n@mixin rtl {\n  @at-root [dir=\"rtl\"] #{&} {\n    @content;\n  }\n}\n\n$ng-select-highlight: rgb(var(--color-primary-700)) !default;\n$ng-select-primary-text: rgb(var(--color-text-main)) !default;\n$ng-select-disabled-text: rgb(var(--color-secondary-100)) !default;\n$ng-select-border: rgb(var(--color-secondary-600)) !default;\n$ng-select-border-radius: 0.5rem !default;\n$ng-select-bg: rgb(var(--color-background)) !default;\n$ng-select-selected: transparent !default;\n$ng-select-selected-text: $ng-select-primary-text !default;\n\n$ng-select-marked: rgb(var(--color-primary-100)) !default;\n$ng-select-marked-text: $ng-select-primary-text !default;\n\n$ng-select-box-shadow: none !default;\n$ng-select-placeholder: rgb(var(--color-text-muted)) !default;\n$ng-select-height: 100%;\n$ng-select-value-padding-left: 1rem !default;\n$ng-select-value-font-size: 0.9em !default;\n$ng-select-value-text: $ng-select-primary-text !default;\n\n$ng-select-dropdown-bg: $ng-select-bg !default;\n$ng-select-dropdown-border: $ng-select-border !default;\n$ng-select-dropdown-optgroup-text: rgb(var(--color-text-muted)) !default;\n$ng-select-dropdown-optgroup-marked: $ng-select-dropdown-optgroup-text !default;\n$ng-select-dropdown-option-bg: $ng-select-dropdown-bg !default;\n$ng-select-dropdown-option-text: $ng-select-primary-text !default;\n$ng-select-dropdown-option-disabled: rgb(var(--color-secondary-300)) !default;\n\n$ng-select-input-text: $ng-select-primary-text !default;\n\n// Custom color variables\n$ng-select-arrow-hover: rgb(var(--color-secondary-700)) !default;\n$ng-clear-icon-hover: rgb(var(--color-text-main)) !default;\n$ng-dropdown-shadow: rgb(var(--color-secondary-100)) !default;\n\n.ng-select {\n  height: $ng-select-height;\n  &.ng-select-opened {\n    > .ng-select-container {\n      background: transparent;\n      &:hover {\n        box-shadow: $ng-select-box-shadow;\n      }\n      .ng-arrow {\n        top: -2px;\n        border-color: transparent transparent $ng-select-arrow-hover;\n        border-width: 0 5px 5px;\n        &:hover {\n          border-color: transparent transparent $ng-select-arrow-hover;\n        }\n      }\n    }\n    &.ng-select-top {\n      > .ng-select-container {\n        border-top-right-radius: 0;\n        border-top-left-radius: 0;\n      }\n    }\n    &.ng-select-right {\n      > .ng-select-container {\n        border-top-right-radius: 0;\n        border-bottom-right-radius: 0;\n      }\n    }\n    &.ng-select-bottom {\n      > .ng-select-container {\n        border-bottom-right-radius: 0;\n        border-bottom-left-radius: 0;\n      }\n    }\n    &.ng-select-left {\n      > .ng-select-container {\n        border-top-left-radius: 0;\n        border-bottom-left-radius: 0;\n      }\n    }\n  }\n  &.ng-select-focused {\n    &:not(.ng-select-opened) > .ng-select-container {\n      border-color: $ng-select-highlight;\n      box-shadow: $ng-select-box-shadow;\n    }\n  }\n  &.ng-select-disabled {\n    > .ng-select-container {\n      background-color: $ng-select-disabled-text;\n    }\n  }\n  .ng-has-value .ng-placeholder {\n    display: none;\n  }\n  .ng-select-container {\n    color: $ng-select-primary-text;\n    background-color: $ng-select-bg;\n    border-radius: $ng-select-border-radius;\n    border: none;\n    height: $ng-select-height;\n    align-items: center;\n    &:hover {\n      box-shadow: $ng-select-box-shadow;\n    }\n    .ng-value-container {\n      align-items: center;\n      padding: 6px 0px 5px $ng-select-value-padding-left;\n      @include rtl {\n        padding-right: $ng-select-value-padding-left;\n        padding-left: 0;\n      }\n      .ng-placeholder {\n        color: $ng-select-placeholder;\n      }\n\n      .ng-input {\n        padding-top: 2px;\n        > input {\n          color: $ng-select-input-text;\n        }\n      }\n    }\n  }\n  &.ng-select-single {\n    .ng-select-container {\n      height: $ng-select-height;\n      .ng-value-container {\n        display: flex;\n        height: 100%;\n        .ng-input {\n          left: 0;\n          padding-left: $ng-select-value-padding-left;\n          padding-right: 50px;\n          @include rtl {\n            padding-right: $ng-select-value-padding-left;\n            padding-left: 50px;\n          }\n        }\n      }\n    }\n  }\n  &.ng-select-multiple {\n    &.ng-select-disabled {\n      > .ng-select-container .ng-value-container .ng-value {\n        background-color: $ng-select-disabled-text;\n        border: 0px solid $ng-select-border; // Removing border on selected value when disabled\n        .ng-value-label {\n          padding: 0 5px;\n        }\n      }\n    }\n    .ng-select-container {\n      .ng-value-container {\n        height: 100%;\n        @include rtl {\n          padding-left: 0;\n        }\n        .ng-value {\n          font-size: $ng-select-value-font-size;\n          color: $ng-select-value-text;\n          background-color: $ng-select-selected;\n          border-radius: 2px;\n          margin: 4px 5px 4px 0px;\n          @include rtl {\n            margin-right: 0;\n            margin-left: 5px;\n          }\n          &.ng-value-disabled {\n            background-color: $ng-select-disabled-text;\n            .ng-value-label {\n              padding-left: 5px;\n              @include rtl {\n                padding-left: 0;\n                padding-right: 5px;\n              }\n            }\n          }\n          .ng-value-label {\n            display: inline-block;\n            padding: 1px 5px;\n          }\n          .ng-value-icon {\n            display: inline-block;\n            padding: 1px 5px;\n            &:hover {\n              background-color: $ng-select-arrow-hover;\n            }\n            &.left {\n              border-right: 1px solid $ng-select-selected;\n              @include rtl {\n                border-left: 1px solid $ng-select-selected;\n                border-right: none;\n              }\n            }\n            &.right {\n              border-left: 1px solid $ng-select-selected;\n              @include rtl {\n                border-left: 0;\n                border-right: 1px solid $ng-select-selected;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n  .ng-clear-wrapper {\n    color: $ng-select-placeholder;\n    padding-top: 2.5px;\n    &:hover .ng-clear {\n      color: $ng-clear-icon-hover;\n    }\n    border-radius: $ng-select-border-radius;\n    text-align: center;\n  }\n  .ng-spinner-zone {\n    padding: 5px 5px 0 0;\n\n    @include rtl {\n      padding: 5px 0 0 5px;\n    }\n  }\n  .ng-arrow-wrapper {\n    width: 25px;\n    padding-right: 5px;\n    @include rtl {\n      padding-left: 5px;\n      padding-right: 0;\n    }\n    &:hover {\n      .ng-arrow {\n        border-top-color: $ng-select-arrow-hover;\n      }\n    }\n    .ng-arrow {\n      border-color: $ng-select-placeholder transparent transparent;\n      border-style: solid;\n      border-width: 5px 5px 2.5px;\n    }\n  }\n}\n\n.ng-dropdown-panel {\n  z-index: 2050 !important;\n  background-color: $ng-select-dropdown-bg;\n  border: 1px solid $ng-select-dropdown-border;\n  border-radius: $ng-select-border-radius;\n  box-shadow: $ng-select-box-shadow;\n  left: 0;\n  &.ng-select-top {\n    bottom: 100%;\n    border-top-right-radius: $ng-select-border-radius;\n    border-top-left-radius: $ng-select-border-radius;\n    border-bottom-color: $ng-select-border;\n    margin-bottom: -1px;\n    .ng-dropdown-panel-items {\n      .ng-option {\n        &:first-child {\n          border-top-right-radius: $ng-select-border-radius;\n          border-top-left-radius: $ng-select-border-radius;\n        }\n      }\n    }\n  }\n  &.ng-select-right {\n    left: 100%;\n    top: 0;\n    border-top-right-radius: $ng-select-border-radius;\n    border-bottom-right-radius: $ng-select-border-radius;\n    border-bottom-left-radius: $ng-select-border-radius;\n    border-bottom-color: $ng-select-border;\n    margin-bottom: -1px;\n    .ng-dropdown-panel-items {\n      .ng-option {\n        &:first-child {\n          border-top-right-radius: $ng-select-border-radius;\n        }\n      }\n    }\n  }\n  &.ng-select-bottom {\n    top: 100%;\n    border-bottom-right-radius: $ng-select-border-radius;\n    border-bottom-left-radius: $ng-select-border-radius;\n    border-top-color: $ng-select-border;\n    margin-top: -1px;\n    .ng-dropdown-panel-items {\n      .ng-option {\n        &:last-child {\n          border-bottom-right-radius: $ng-select-border-radius;\n          border-bottom-left-radius: $ng-select-border-radius;\n        }\n      }\n    }\n  }\n  &.ng-select-left {\n    left: -100%;\n    top: 0;\n    border-top-left-radius: $ng-select-border-radius;\n    border-bottom-right-radius: $ng-select-border-radius;\n    border-bottom-left-radius: $ng-select-border-radius;\n    border-bottom-color: $ng-select-border;\n    margin-bottom: -1px;\n    .ng-dropdown-panel-items {\n      .ng-option {\n        &:first-child {\n          border-top-left-radius: $ng-select-border-radius;\n        }\n      }\n    }\n  }\n  .ng-dropdown-header {\n    border-bottom: 1px solid $ng-select-border;\n    padding: 5px 7px;\n  }\n  .ng-dropdown-footer {\n    border-top: 1px solid $ng-select-border;\n    padding: 5px 7px;\n  }\n  .ng-dropdown-panel-items {\n    border-radius: $ng-select-border-radius;\n    background: $ng-select-bg;\n    .ng-optgroup {\n      user-select: none;\n      padding: 8px 10px;\n      font-weight: 500;\n      color: $ng-select-dropdown-optgroup-text;\n      cursor: pointer;\n      &.ng-option-disabled {\n        cursor: default;\n      }\n      &.ng-option-marked {\n        background-color: $ng-select-marked;\n      }\n      &.ng-option-selected,\n      &.ng-option-selected.ng-option-marked {\n        color: $ng-select-dropdown-optgroup-marked;\n        background-color: $ng-select-selected;\n        font-weight: 600;\n      }\n    }\n    .ng-option {\n      background-color: $ng-select-dropdown-option-bg;\n      color: $ng-select-dropdown-option-text;\n      padding: 0.375rem 0.75rem;\n      &.ng-option-selected.ng-option-marked {\n        background-color: $ng-select-marked;\n      }\n      &.ng-option-selected,\n      &.ng-option-selected.ng-option-marked {\n        color: $ng-select-selected-text;\n        .ng-option-label {\n          font-weight: 600;\n        }\n      }\n      &.ng-option-marked {\n        background-color: $ng-select-marked;\n        color: $ng-select-marked-text;\n      }\n      &.ng-option-disabled {\n        color: $ng-select-dropdown-option-disabled;\n      }\n      &.ng-option-child {\n        padding-left: 22px;\n        @include rtl {\n          padding-right: 22px;\n          padding-left: 0;\n        }\n      }\n      .ng-tag-label {\n        font-size: 80%;\n        font-weight: 400;\n        padding-right: 5px;\n        @include rtl {\n          padding-left: 5px;\n          padding-right: 0;\n        }\n      }\n    }\n  }\n\n  @include rtl {\n    direction: rtl;\n    text-align: right;\n  }\n}\n", ".cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}\n", "textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}\n", "/**\n * Reset styles to be consistent with Bootstrap reset\n * Reassess when <PERSON><PERSON><PERSON> is removed and Tailwind preflight is added\n*/\n\nfieldset {\n  min-width: 0;\n  padding: 0;\n  margin: 0;\n  border: 0;\n}\n\nth {\n  text-align: inherit;\n  text-align: -webkit-match-parent;\n}\n", ".bit-popover-arrow {\n  @apply tw-absolute tw-z-10 tw-h-4 tw-w-4 tw-rotate-45 tw-border-solid tw-bg-background;\n}\n\n.bit-popover-right .bit-popover-arrow {\n  @apply tw-left-1 -tw-translate-x-1/2 tw-rounded-bl-sm tw-border-b tw-border-l tw-border-b-secondary-300 tw-border-l-secondary-300;\n}\n\n.bit-popover-left .bit-popover-arrow {\n  @apply tw-right-1 tw-translate-x-1/2 tw-rounded-tr-sm tw-border-r tw-border-t tw-border-r-secondary-300 tw-border-t-secondary-300;\n}\n\n.bit-popover-right-start .bit-popover-arrow,\n.bit-popover-left-start .bit-popover-arrow {\n  @apply tw-top-6 -tw-translate-y-1/2;\n}\n\n.bit-popover-right-center .bit-popover-arrow,\n.bit-popover-left-center .bit-popover-arrow {\n  @apply tw-top-1/2 -tw-translate-y-1/2;\n}\n\n.bit-popover-right-end .bit-popover-arrow,\n.bit-popover-left-end .bit-popover-arrow {\n  @apply tw-bottom-6 tw-translate-y-1/2;\n}\n\n.bit-popover-below .bit-popover-arrow {\n  @apply tw-top-1 -tw-translate-y-1/2 tw-rounded-tl-sm tw-border-l tw-border-t tw-border-l-secondary-300 tw-border-t-secondary-300;\n}\n\n.bit-popover-above .bit-popover-arrow {\n  @apply tw-bottom-1 tw-translate-y-1/2 tw-rounded-br-sm tw-border-b tw-border-r tw-border-b-secondary-300 tw-border-r-secondary-300;\n}\n\n.bit-popover-below-start .bit-popover-arrow,\n.bit-popover-above-start .bit-popover-arrow {\n  @apply tw-left-6 -tw-translate-x-1/2;\n}\n\n.bit-popover-below-center .bit-popover-arrow,\n.bit-popover-above-center .bit-popover-arrow {\n  @apply tw-left-1/2 -tw-translate-x-1/2;\n}\n\n.bit-popover-below-end .bit-popover-arrow,\n.bit-popover-above-end .bit-popover-arrow {\n  @apply tw-right-6 tw-translate-x-1/2;\n}\n", ":root {\n  --bit-toast-width: 19rem;\n  --bit-toast-width-full: 96%;\n  --bit-toast-top: 4.3rem;\n}\n", "/* based on angular-toastr css https://github.com/Foxandxss/angular-toastr/blob/cb508fe6801d6b288d3afc525bb40fee1b101650/dist/angular-toastr.css */\n\n/* position */\n.toast-center-center {\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n.toast-top-center {\n  top: 0;\n  right: 0;\n  width: 100%;\n}\n.toast-bottom-center {\n  bottom: 0;\n  right: 0;\n  width: 100%;\n}\n.toast-top-full-width {\n  top: 0;\n  right: 0;\n  width: 100%;\n}\n.toast-bottom-full-width {\n  bottom: 0;\n  right: 0;\n  width: 100%;\n}\n.toast-top-left {\n  top: 12px;\n  left: 12px;\n}\n.toast-top-right {\n  top: 12px;\n  right: 12px;\n}\n.toast-bottom-right {\n  right: 12px;\n  bottom: 12px;\n}\n.toast-bottom-left {\n  bottom: 12px;\n  left: 12px;\n}\n\n/* toast styles */\n.toast-title {\n  font-weight: bold;\n}\n.toast-message {\n  word-wrap: break-word;\n}\n.toast-message a,\n.toast-message label {\n  color: #FFFFFF;\n}\n.toast-message a:hover {\n  color: #CCCCCC;\n  text-decoration: none;\n}\n.toast-close-button {\n  position: relative;\n  right: -0.3em;\n  top: -0.3em;\n  float: right;\n  font-size: 20px;\n  font-weight: bold;\n  color: #FFFFFF;\n  text-shadow: 0 1px 0 #ffffff;\n  /* opacity: 0.8; */\n}\n.toast-close-button:hover,\n.toast-close-button:focus {\n  color: #000000;\n  text-decoration: none;\n  cursor: pointer;\n  opacity: 0.4;\n}\n/*Additional properties for button version\n iOS requires the button element instead of an anchor tag.\n If you want the anchor version, it requires `href=\"#\"`.*/\nbutton.toast-close-button {\n  padding: 0;\n  cursor: pointer;\n  background: transparent;\n  border: 0;\n}\n.toast-container {\n  pointer-events: none;\n  position: fixed;\n  z-index: 999999;\n}\n.toast-container * {\n  box-sizing: border-box;\n}\n.toast-container .ngx-toastr {\n  position: relative;\n  overflow: hidden;\n  margin: 0 0 6px;\n  padding: 15px 15px 15px 50px;\n  width: 300px;\n  border-radius: 3px 3px 3px 3px;\n  background-position: 15px center;\n  background-repeat: no-repeat;\n  background-size: 24px;\n  box-shadow: 0 0 12px #999999;\n  color: #FFFFFF;\n}\n.toast-container .ngx-toastr:hover {\n  box-shadow: 0 0 12px #000000;\n  opacity: 1;\n  cursor: pointer;\n}\n/* https://github.com/FortAwesome/Font-Awesome-Pro/blob/master/advanced-options/raw-svg/regular/info-circle.svg */\n.toast-info {\n  background-image: url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA1MTIgNTEyJyB3aWR0aD0nNTEyJyBoZWlnaHQ9JzUxMic+PHBhdGggZmlsbD0ncmdiKDI1NSwyNTUsMjU1KScgZD0nTTI1NiA4QzExOS4wNDMgOCA4IDExOS4wODMgOCAyNTZjMCAxMzYuOTk3IDExMS4wNDMgMjQ4IDI0OCAyNDhzMjQ4LTExMS4wMDMgMjQ4LTI0OEM1MDQgMTE5LjA4MyAzOTIuOTU3IDggMjU2IDh6bTAgMTEwYzIzLjE5NiAwIDQyIDE4LjgwNCA0MiA0MnMtMTguODA0IDQyLTQyIDQyLTQyLTE4LjgwNC00Mi00MiAxOC44MDQtNDIgNDItNDJ6bTU2IDI1NGMwIDYuNjI3LTUuMzczIDEyLTEyIDEyaC04OGMtNi42MjcgMC0xMi01LjM3My0xMi0xMnYtMjRjMC02LjYyNyA1LjM3My0xMiAxMi0xMmgxMnYtNjRoLTEyYy02LjYyNyAwLTEyLTUuMzczLTEyLTEydi0yNGMwLTYuNjI3IDUuMzczLTEyIDEyLTEyaDY0YzYuNjI3IDAgMTIgNS4zNzMgMTIgMTJ2MTAwaDEyYzYuNjI3IDAgMTIgNS4zNzMgMTIgMTJ2MjR6Jy8+PC9zdmc+\");\n}\n/* https://github.com/FortAwesome/Font-Awesome-Pro/blob/master/advanced-options/raw-svg/regular/times-circle.svg */\n.toast-error {\n  background-image: url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA1MTIgNTEyJyB3aWR0aD0nNTEyJyBoZWlnaHQ9JzUxMic+PHBhdGggZmlsbD0ncmdiKDI1NSwyNTUsMjU1KScgZD0nTTI1NiA4QzExOSA4IDggMTE5IDggMjU2czExMSAyNDggMjQ4IDI0OCAyNDgtMTExIDI0OC0yNDhTMzkzIDggMjU2IDh6bTEyMS42IDMxMy4xYzQuNyA0LjcgNC43IDEyLjMgMCAxN0wzMzggMzc3LjZjLTQuNyA0LjctMTIuMyA0LjctMTcgMEwyNTYgMzEybC02NS4xIDY1LjZjLTQuNyA0LjctMTIuMyA0LjctMTcgMEwxMzQuNCAzMzhjLTQuNy00LjctNC43LTEyLjMgMC0xN2w2NS42LTY1LTY1LjYtNjUuMWMtNC43LTQuNy00LjctMTIuMyAwLTE3bDM5LjYtMzkuNmM0LjctNC43IDEyLjMtNC43IDE3IDBsNjUgNjUuNyA2NS4xLTY1LjZjNC43LTQuNyAxMi4zLTQuNyAxNyAwbDM5LjYgMzkuNmM0LjcgNC43IDQuNyAxMi4zIDAgMTdMMzEyIDI1Nmw2NS42IDY1LjF6Jy8+PC9zdmc+\");\n}\n/* https://github.com/FortAwesome/Font-Awesome-Pro/blob/master/advanced-options/raw-svg/regular/check.svg */\n.toast-success {\n  background-image: url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA1MTIgNTEyJyB3aWR0aD0nNTEyJyBoZWlnaHQ9JzUxMic+PHBhdGggZmlsbD0ncmdiKDI1NSwyNTUsMjU1KScgZD0nTTE3My44OTggNDM5LjQwNGwtMTY2LjQtMTY2LjRjLTkuOTk3LTkuOTk3LTkuOTk3LTI2LjIwNiAwLTM2LjIwNGwzNi4yMDMtMzYuMjA0YzkuOTk3LTkuOTk4IDI2LjIwNy05Ljk5OCAzNi4yMDQgMEwxOTIgMzEyLjY5IDQzMi4wOTUgNzIuNTk2YzkuOTk3LTkuOTk3IDI2LjIwNy05Ljk5NyAzNi4yMDQgMGwzNi4yMDMgMzYuMjA0YzkuOTk3IDkuOTk3IDkuOTk3IDI2LjIwNiAwIDM2LjIwNGwtMjk0LjQgMjk0LjQwMWMtOS45OTggOS45OTctMjYuMjA3IDkuOTk3LTM2LjIwNC0uMDAxeicvPjwvc3ZnPg==\");\n}\n/* https://github.com/FortAwesome/Font-Awesome-Pro/blob/master/advanced-options/raw-svg/regular/exclamation-triangle.svg */\n.toast-warning {\n  background-image: url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCA1NzYgNTEyJyB3aWR0aD0nNTc2JyBoZWlnaHQ9JzUxMic+PHBhdGggZmlsbD0ncmdiKDI1NSwyNTUsMjU1KScgZD0nTTU2OS41MTcgNDQwLjAxM0M1ODcuOTc1IDQ3Mi4wMDcgNTY0LjgwNiA1MTIgNTI3Ljk0IDUxMkg0OC4wNTRjLTM2LjkzNyAwLTU5Ljk5OS00MC4wNTUtNDEuNTc3LTcxLjk4N0wyNDYuNDIzIDIzLjk4NWMxOC40NjctMzIuMDA5IDY0LjcyLTMxLjk1MSA4My4xNTQgMGwyMzkuOTQgNDE2LjAyOHpNMjg4IDM1NGMtMjUuNDA1IDAtNDYgMjAuNTk1LTQ2IDQ2czIwLjU5NSA0NiA0NiA0NiA0Ni0yMC41OTUgNDYtNDYtMjAuNTk1LTQ2LTQ2LTQ2em0tNDMuNjczLTE2NS4zNDZsNy40MTggMTM2Yy4zNDcgNi4zNjQgNS42MDkgMTEuMzQ2IDExLjk4MiAxMS4zNDZoNDguNTQ2YzYuMzczIDAgMTEuNjM1LTQuOTgyIDExLjk4Mi0xMS4zNDZsNy40MTgtMTM2Yy4zNzUtNi44NzQtNS4wOTgtMTIuNjU0LTExLjk4Mi0xMi42NTRoLTYzLjM4M2MtNi44ODQgMC0xMi4zNTYgNS43OC0xMS45ODEgMTIuNjU0eicvPjwvc3ZnPg==\");\n}\n.toast-container.toast-top-center .ngx-toastr,\n.toast-container.toast-bottom-center .ngx-toastr {\n  width: 300px;\n  margin-left: auto;\n  margin-right: auto;\n}\n.toast-container.toast-top-full-width .ngx-toastr,\n.toast-container.toast-bottom-full-width .ngx-toastr {\n  width: 96%;\n  margin-left: auto;\n  margin-right: auto;\n}\n.ngx-toastr {\n  background-color: #030303;\n  pointer-events: auto;\n}\n.toast-success {\n  background-color: #51A351;\n}\n.toast-error {\n  background-color: #BD362F;\n}\n.toast-info {\n  background-color: #2F96B4;\n}\n.toast-warning {\n  background-color: #F89406;\n}\n.toast-progress {\n  position: absolute;\n  left: 0;\n  bottom: 0;\n  height: 4px;\n  background-color: #000000;\n  opacity: 0.4;\n}\n/* Responsive Design */\n@media all and (max-width: 240px) {\n  .toast-container .ngx-toastr.div {\n    padding: 8px 8px 8px 50px;\n    width: 11em;\n  }\n  .toast-container .toast-close-button {\n    right: -0.2em;\n    top: -0.2em;\n  }\n}\n@media all and (min-width: 241px) and (max-width: 480px) {\n  .toast-container .ngx-toastr.div {\n    padding: 8px 8px 8px 50px;\n    width: 18em;\n  }\n  .toast-container .toast-close-button {\n    right: -0.2em;\n    top: -0.2em;\n  }\n}\n@media all and (min-width: 481px) and (max-width: 768px) {\n  .toast-container .ngx-toastr.div {\n    padding: 15px 15px 15px 50px;\n    width: 25em;\n  }\n}\n", "@import \"./toast.tokens.css\";\n@import \"ngx-toastr/toastr\";\n\n/* Override all default styles from `ngx-toaster` */\n.toast-container .ngx-toastr {\n  all: unset;\n  display: block;\n  width: var(--bit-toast-width);\n\n  /* Needed to make hover states work in Electron, since the toast appears in the draggable region. */\n  -webkit-app-region: no-drag;\n}\n\n/* Disable hover styles */\n.toast-container .ngx-toastr:hover {\n  box-shadow: none;\n}\n\n.toast-container.toast-bottom-full-width .ngx-toastr {\n  width: var(--bit-toast-width-full);\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.toast-container.toast-top-full-width {\n  top: var(--bit-toast-top);\n}\n", "/**\n * Tailwind doesn't have a good way to style search-cancel-button.\n */\nbit-search input[type=\"search\"]::-webkit-search-cancel-button {\n  -webkit-appearance: none;\n  appearance: none;\n  height: 21px;\n  width: 21px;\n  margin: 0;\n  cursor: pointer;\n  background-repeat: no-repeat;\n  mask-image: url(\"./close-button.svg\");\n  -webkit-mask-image: url(\"./close-button.svg\");\n  background-color: rgba(var(--color-text-muted));\n}\n\nbit-search input[type=\"search\"]::-webkit-search-cancel-button:hover {\n  background-color: rgba(var(--color-text-main));\n}\n", "@import \"@angular/cdk/a11y-prebuilt.css\";\n@import \"@angular/cdk/text-field-prebuilt.css\";\n@import \"./reset.css\";\n@import \"./popover/popover.component.css\";\n@import \"./toast/toast.tokens.css\";\n@import \"./toast/toastr.css\";\n@import \"./search/search.component.css\";\n\n:root {\n  --color-transparent-hover: rgb(0 0 0 / 0.02);\n  --color-shadow: 168 179 200;\n\n  --color-background: 255 255 255;\n  --color-background-alt: 243 246 249;\n  --color-background-alt2: 23 92 219;\n  --color-background-alt3: 26 65 172;\n  --color-background-alt4: 2 15 102;\n\n  --color-primary-100: 219 229 246;\n  --color-primary-300: 121 161 233;\n  --color-primary-600: 23 93 220;\n  --color-primary-700: 26 65 172;\n\n  --color-secondary-100: 230 233 239;\n  --color-secondary-300: 168 179 200;\n  --color-secondary-500: 90 109 145;\n  --color-secondary-600: 83 99 131;\n  --color-secondary-700: 63 75 99;\n\n  --color-info-100: 219 229 246;\n  --color-info-600: 121 161 233;\n  --color-info-700: 13 36 123;\n\n  --color-warning-100: 255 244 212;\n  --color-warning-600: 255 191 0;\n  --color-warning-700: 142 64 0;\n\n  --color-danger-100: 255 236 239;\n  --color-danger-600: 203 38 58;\n  --color-danger-700: 149 27 42;\n\n  --color-success-100: 213 243 216;\n  --color-success-600: 12 128 24;\n  --color-success-700: 8 81 15;\n\n  --color-notification-100: 255 225 247;\n  --color-notification-600: 192 17 118;\n\n  --color-art-primary: 2 15 102;\n  --color-art-accent: 44 221 223;\n\n  --color-text-main: 27 32 41;\n  --color-text-muted: 96 109 145;\n  --color-text-contrast: 255 255 255;\n  --color-text-alt2: 255 255 255;\n  --color-text-code: 192 17 118;\n\n  --color-marketing-logo: 23 93 220;\n\n  --tw-ring-offset-color: #ffffff;\n}\n\n.theme_light {\n  /* should be left empty as white is the default */\n}\n\n.theme_dark {\n  --color-transparent-hover: rgb(255 255 255 / 0.02);\n  --color-shadow: 0 0 0;\n\n  --color-background: 32 39 51;\n  --color-background-alt: 18 26 39;\n  --color-background-alt2: 47 52 61;\n  --color-background-alt3: 48 57 70;\n  --color-background-alt4: 18 26 39;\n\n  --color-primary-100: 26 39 78;\n  --color-primary-300: 26 65 172;\n  --color-primary-600: 101 171 255;\n  --color-primary-700: 170 195 239;\n\n  --color-secondary-100: 48 57 70;\n  --color-secondary-300: 82 91 106;\n  --color-secondary-500: 121 128 142;\n  --color-secondary-600: 143 152 166;\n  --color-secondary-700: 158 167 181;\n\n  --color-success-100: 8 81 15;\n  --color-success-600: 107 241 120;\n  --color-success-700: 213 243 216;\n\n  --color-danger-100: 149 27 42;\n  --color-danger-600: 255 78 99;\n  --color-danger-700: 255 236 239;\n\n  --color-warning-100: 142 64 0;\n  --color-warning-600: 255 191 0;\n  --color-warning-700: 255 244 212;\n\n  --color-info-100: 13 36 123;\n  --color-info-600: 121 161 233;\n  --color-info-700: 219 229 246;\n\n  --color-notification-100: 117 37 83;\n  --color-notification-600: 255 143 208;\n\n  --color-art-primary: 243 246 249;\n  --color-art-accent: 44 221 233;\n\n  --color-text-main: 243 246 249;\n  --color-text-muted: 136 152 181;\n  --color-text-contrast: 18 26 39;\n  --color-text-alt2: 255 255 255;\n  --color-text-code: 255 143 208;\n\n  --color-marketing-logo: 255 255 255;\n\n  --tw-ring-offset-color: #1f242e;\n}\n\n/**\n * tw-break-words does not work with table cells:\n * https://github.com/tailwindlabs/tailwindcss/issues/835\n */\ntd.tw-break-words {\n  overflow-wrap: anywhere;\n}\n\n/**\n * tw-list-none hides summary arrow in Firefox & Chrome but not Safari:\n * https://github.com/tailwindlabs/tailwindcss/issues/924#issuecomment-915509785\n */\nsummary.tw-list-none::marker,\nsummary.tw-list-none::-webkit-details-marker {\n  display: none;\n}\n\n/**\n * Arbitrary values can't be used with `text-align`:\n * https://github.com/tailwindlabs/tailwindcss/issues/802#issuecomment-849013311\n */\n.tw-text-unset {\n  text-align: unset;\n}\n\n/**\n * Bootstrap uses z-index: 1050 for modals, dialogs and drag-and-drop previews should appear above them.\n * When bootstrap is removed, test if these styles are still needed and that overlays display properly over other content.\n * CL-483\n */\n.cdk-drag-preview,\n.cdk-overlay-container,\n.cdk-global-overlay-wrapper,\n.cdk-overlay-connected-position-bounding-box,\n.cdk-overlay-backdrop,\n.cdk-overlay-pane {\n  z-index: 2000 !important;\n}\n\n.cdk-global-scrollblock {\n  position: relative;\n  height: 100%;\n  overflow: hidden;\n}\n", "@import \"../../../../../libs/components/src/tw-theme.css\";\n\n@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n@layer components {\n  /** Safari Support */\n  html.browser_safari .tw-styled-scrollbar::-webkit-scrollbar {\n    @apply tw-overflow-auto;\n  }\n  html.browser_safari .tw-styled-scrollbar::-webkit-scrollbar-thumb {\n    @apply tw-bg-secondary-500 tw-rounded-lg tw-border-4 tw-border-solid tw-border-transparent tw-bg-clip-content;\n  }\n  html.browser_safari .tw-styled-scrollbar::-webkit-scrollbar-track {\n    @apply tw-bg-background-alt;\n  }\n  html.browser_safari .tw-styled-scrollbar::-webkit-scrollbar-thumb:hover {\n    @apply tw-bg-secondary-600;\n  }\n\n  /* FireFox & Chrome support */\n  html:not(.browser_safari) .tw-styled-scrollbar {\n    scrollbar-color: rgb(var(--color-secondary-500)) rgb(var(--color-background-alt));\n  }\n}\n"], "names": [], "sourceRoot": ""}