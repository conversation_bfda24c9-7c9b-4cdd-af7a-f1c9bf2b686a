{"appName": {"message": "bitwarden"}, "appLogoLabel": {"message": "โลโก้ Bitwarden"}, "extName": {"message": "Bitwarden Password Manager", "description": "Extension name, MUST be less than 40 characters (Safari restriction)"}, "extDesc": {"message": "At home, at work, or on the go, <PERSON><PERSON><PERSON> easily secures all your passwords, passkeys, and sensitive information", "description": "Extension description, MUST be less than 112 characters (Safari restriction)"}, "loginOrCreateNewAccount": {"message": "ล็อกอิน หรือ สร้างบัญชีใหม่ เพื่อใช้งานตู้นิรภัยของคุณ"}, "inviteAccepted": {"message": "Invitation accepted"}, "createAccount": {"message": "สร้างบัญชี"}, "newToBitwarden": {"message": "New to Bitwarden?"}, "logInWithPasskey": {"message": "Log in with passkey"}, "useSingleSignOn": {"message": "Use single sign-on"}, "welcomeBack": {"message": "Welcome back"}, "setAStrongPassword": {"message": "Set a strong password"}, "finishCreatingYourAccountBySettingAPassword": {"message": "Finish creating your account by setting a password"}, "enterpriseSingleSignOn": {"message": "Enterprise Single Sign-On"}, "cancel": {"message": "ยกเลิก"}, "close": {"message": "ปิด"}, "submit": {"message": "ส่งข้อมูล"}, "emailAddress": {"message": "ที่อยู่อีเมล"}, "masterPass": {"message": "Master Password"}, "masterPassDesc": {"message": "รหัสผ่านหลัก คือ รหัสผ่านที่ใช้เข้าถึงตู้นิรภัยของคุณ สิ่งสำคัญมาก คือ คุณจะต้องไม่ลืมรหัสผ่านหลักโดยเด็ดขาด เพราะหากคุณลืมแล้วล่ะก็ จะไม่มีวิธีที่สามารถกู้รหัสผ่านของคุณได้เลย"}, "masterPassHintDesc": {"message": "คำใบ้เกี่ยวกับรหัสผ่านหลักสามารถช่วยให้คุณนึกรหัสผ่านหลักออกได้หากลืม"}, "masterPassHintText": {"message": "If you forget your password, the password hint can be sent to your email. $CURRENT$/$MAXIMUM$ character maximum.", "placeholders": {"current": {"content": "$1", "example": "0"}, "maximum": {"content": "$2", "example": "50"}}}, "reTypeMasterPass": {"message": "Re-type Master Password"}, "masterPassHint": {"message": "Master Password Hint (optional)"}, "passwordStrengthScore": {"message": "Password strength score $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "joinOrganization": {"message": "Join organization"}, "joinOrganizationName": {"message": "Join $ORGANIZATIONNAME$", "placeholders": {"organizationName": {"content": "$1", "example": "My Org Name"}}}, "finishJoiningThisOrganizationBySettingAMasterPassword": {"message": "Finish joining this organization by setting a master password."}, "tab": {"message": "แท็บ"}, "vault": {"message": "ตู้นิรภัย"}, "myVault": {"message": "My Vault"}, "allVaults": {"message": "ตู้นิรภัยทั้งหมด"}, "tools": {"message": "เครื่องมือ"}, "settings": {"message": "การตั้งค่า"}, "currentTab": {"message": "แท็บปัจจุบัน"}, "copyPassword": {"message": "คัดลอกรหัสผ่าน"}, "copyPassphrase": {"message": "Copy passphrase"}, "copyNote": {"message": "Copy Note"}, "copyUri": {"message": "คัดลอก URI"}, "copyUsername": {"message": "คัดลอกชื่อผู้ใช้"}, "copyNumber": {"message": "คัดลอกหมายเลข"}, "copySecurityCode": {"message": "คัดลอกรหัสรักษาความปลอดภัย"}, "copyName": {"message": "Copy name"}, "copyCompany": {"message": "Copy company"}, "copySSN": {"message": "Copy Social Security number"}, "copyPassportNumber": {"message": "Copy passport number"}, "copyLicenseNumber": {"message": "Copy license number"}, "copyPrivateKey": {"message": "Copy private key"}, "copyPublicKey": {"message": "Copy public key"}, "copyFingerprint": {"message": "Copy fingerprint"}, "copyCustomField": {"message": "Copy $FIELD$", "placeholders": {"field": {"content": "$1", "example": "Custom field label"}}}, "copyWebsite": {"message": "Copy website"}, "copyNotes": {"message": "Copy notes"}, "copy": {"message": "Copy", "description": "Copy to clipboard"}, "fill": {"message": "Fill", "description": "This string is used on the vault page to indicate autofilling. Horizontal space is limited in the interface here so try and keep translations as concise as possible."}, "autoFill": {"message": "กรอกข้อมูลอัตโนมัติ"}, "autoFillLogin": {"message": "Autofill login"}, "autoFillCard": {"message": "Autofill card"}, "autoFillIdentity": {"message": "Autofill identity"}, "fillVerificationCode": {"message": "Fill verification code"}, "fillVerificationCodeAria": {"message": "Fill Verification Code", "description": "Aria label for the heading displayed the inline menu for totp code autofill"}, "generatePasswordCopied": {"message": "Generate Password (copied)"}, "copyElementIdentifier": {"message": "คัดลอกชื่อของช่องที่กำหนดเอง"}, "noMatchingLogins": {"message": "ไม่พบข้อมูลล็อกอินที่ตรงกัน"}, "noCards": {"message": "No cards"}, "noIdentities": {"message": "No identities"}, "addLoginMenu": {"message": "Add login"}, "addCardMenu": {"message": "Add card"}, "addIdentityMenu": {"message": "Add identity"}, "unlockVaultMenu": {"message": "ปลดล็อกกตู้นิรภัยของคุณ"}, "loginToVaultMenu": {"message": "ลงชื่อเข้าใช้ตู้นิรภัยของคุณ"}, "autoFillInfo": {"message": "ไม่พบข้อมูลล็อกอินเพื่อใช้กรอกข้อมูลอัตโนมัติ สำหรับแท็บปัจจุบันของเบราว์เซอร์"}, "addLogin": {"message": "Add a Login"}, "addItem": {"message": "เพิ่มรายการ"}, "accountEmail": {"message": "Account email"}, "requestHint": {"message": "Request hint"}, "requestPasswordHint": {"message": "Request password hint"}, "enterYourAccountEmailAddressAndYourPasswordHintWillBeSentToYou": {"message": "Enter your account email address and your password hint will be sent to you"}, "getMasterPasswordHint": {"message": "รับคำใบ้เกี่ยวกับรหัสผ่านหลักของคุณ"}, "continue": {"message": "ดำเนินการต่อไป"}, "sendVerificationCode": {"message": "ส่งโค้ดยืนยันไปยังอีเมลของคุณ"}, "sendCode": {"message": "ส่งโค้ด"}, "codeSent": {"message": "ส่งโค้ดแล้ว"}, "verificationCode": {"message": "Verification Code"}, "confirmIdentity": {"message": "ยืนยันตัวตนของคุณเพื่อดำเนินการต่อ"}, "changeMasterPassword": {"message": "Change Master Password"}, "continueToWebApp": {"message": "Continue to web app?"}, "continueToWebAppDesc": {"message": "Explore more features of your Bitwarden account on the web app."}, "continueToHelpCenter": {"message": "Continue to Help Center?"}, "continueToHelpCenterDesc": {"message": "Learn more about how to use Bitwarden on the Help Center."}, "continueToBrowserExtensionStore": {"message": "Continue to browser extension store?"}, "continueToBrowserExtensionStoreDesc": {"message": "Help others find out if <PERSON><PERSON><PERSON> is right for them. Visit your browser's extension store and leave a rating now."}, "changeMasterPasswordOnWebConfirmation": {"message": "You can change your master password on the Bitwarden web app."}, "fingerprintPhrase": {"message": "Fingerprint Phrase", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "yourAccountsFingerprint": {"message": "ข้อความลายนิ้วมือของบัญชีของคุณ", "description": "A 'fingerprint phrase' is a unique word phrase (similar to a passphrase) that a user can use to authenticate their public key with another user, for the purposes of sharing."}, "twoStepLogin": {"message": "Two-step <PERSON><PERSON>"}, "logOut": {"message": "ออกจากระบบ"}, "aboutBitwarden": {"message": "About Bitwarden"}, "about": {"message": "เกี่ยวกับ"}, "moreFromBitwarden": {"message": "More from Bitwarden"}, "continueToBitwardenDotCom": {"message": "Continue to bitwarden.com?"}, "bitwardenForBusiness": {"message": "Bitwarden for Business"}, "bitwardenAuthenticator": {"message": "Bitwarden Authenticator"}, "continueToAuthenticatorPageDesc": {"message": "Bitwarden Authenticator allows you to store authenticator keys and generate TOTP codes for 2-step verification flows. Learn more on the bitwarden.com website"}, "bitwardenSecretsManager": {"message": "Bitwarden Secrets Manager"}, "continueToSecretsManagerPageDesc": {"message": "Securely store, manage, and share developer secrets with Bitwarden Secrets Manager. Learn more on the bitwarden.com website."}, "passwordlessDotDev": {"message": "Passwordless.dev"}, "continueToPasswordlessDotDevPageDesc": {"message": "Create smooth and secure login experiences free from traditional passwords with Passwordless.dev. Learn more on the bitwarden.com website."}, "freeBitwardenFamilies": {"message": "Free Bitwarden Families"}, "freeBitwardenFamiliesPageDesc": {"message": "You are eligible for Free Bitwarden Families. Redeem this offer today in the web app."}, "version": {"message": "เวอร์ชัน"}, "save": {"message": "บันทึก"}, "move": {"message": "ย้าย"}, "addFolder": {"message": "เพิ่มโฟลเดอร์"}, "name": {"message": "ชื่อ"}, "editFolder": {"message": "แก้ไขโฟลเดอร์"}, "editFolderWithName": {"message": "Edit folder: $FOLDERNAME$", "placeholders": {"foldername": {"content": "$1", "example": "Social"}}}, "newFolder": {"message": "New folder"}, "folderName": {"message": "Folder name"}, "folderHintText": {"message": "Nest a folder by adding the parent folder's name followed by a “/”. Example: Social/Forums"}, "noFoldersAdded": {"message": "No folders added"}, "createFoldersToOrganize": {"message": "Create folders to organize your vault items"}, "deleteFolderPermanently": {"message": "Are you sure you want to permanently delete this folder?"}, "deleteFolder": {"message": "ลบโฟลเดอร์"}, "folders": {"message": "โฟลเดอร์"}, "noFolders": {"message": "ไม่มีโฟลเดอร์"}, "helpFeedback": {"message": "Help & Feedback"}, "helpCenter": {"message": "Bitwarden Help center"}, "communityForums": {"message": "Explore Bitwarden community forums"}, "contactSupport": {"message": "Contact Bitwarden support"}, "sync": {"message": "ซิงค์"}, "syncVaultNow": {"message": "Sync Vault Now"}, "lastSync": {"message": "Last Sync:"}, "passGen": {"message": "Password Generator"}, "generator": {"message": "สุ่มรหัส", "description": "Short for 'credential generator'."}, "passGenInfo": {"message": "สร้างรหัสผ่านที่รัดกุมและไม่ซ้ำใครโดยอัตโนมัติสำหรับการเข้าสู่ระบบของคุณ"}, "bitWebVaultApp": {"message": "Bitwarden web app"}, "importItems": {"message": "Import Items"}, "select": {"message": "เลือก"}, "generatePassword": {"message": "Generate Password"}, "generatePassphrase": {"message": "Generate passphrase"}, "passwordGenerated": {"message": "Password generated"}, "passphraseGenerated": {"message": "Passphrase generated"}, "usernameGenerated": {"message": "Username generated"}, "emailGenerated": {"message": "Email generated"}, "regeneratePassword": {"message": "Regenerate Password"}, "options": {"message": "ตัวเลือก"}, "length": {"message": "ความยาว"}, "include": {"message": "Include", "description": "Card header for password generator include block"}, "uppercaseDescription": {"message": "Include uppercase characters", "description": "Tooltip for the password generator uppercase character checkbox"}, "uppercaseLabel": {"message": "A-Z", "description": "Label for the password generator uppercase character checkbox"}, "lowercaseDescription": {"message": "Include lowercase characters", "description": "Full description for the password generator lowercase character checkbox"}, "lowercaseLabel": {"message": "a-z", "description": "Label for the password generator lowercase character checkbox"}, "numbersDescription": {"message": "Include numbers", "description": "Full description for the password generator numbers checkbox"}, "numbersLabel": {"message": "0-9", "description": "Label for the password generator numbers checkbox"}, "specialCharactersDescription": {"message": "Include special characters", "description": "Full description for the password generator special characters checkbox"}, "numWords": {"message": "Number of Words"}, "wordSeparator": {"message": "Word Separator"}, "capitalize": {"message": "ขึ้นต้นด้วยตัวพิมพ์ใหญ่", "description": "Make the first letter of a work uppercase."}, "includeNumber": {"message": "ต่อท้ายด้วยตัวเลข"}, "minNumbers": {"message": "Minimum Numbers"}, "minSpecial": {"message": "Minimum Special"}, "avoidAmbiguous": {"message": "Avoid ambiguous characters", "description": "Label for the avoid ambiguous characters checkbox."}, "generatorPolicyInEffect": {"message": "Enterprise policy requirements have been applied to your generator options.", "description": "Indicates that a policy limits the credential generator screen."}, "searchVault": {"message": "ค้นหาในตู้นิรภัย"}, "edit": {"message": "แก้ไข"}, "view": {"message": "แสดง"}, "noItemsInList": {"message": "ไม่มีรายการ"}, "itemInformation": {"message": "Item Information"}, "username": {"message": "ชื่อผู้ใช้"}, "password": {"message": "รหัสผ่าน"}, "totp": {"message": "Authenticator secret"}, "passphrase": {"message": "ข้อความรหัสผ่าน"}, "favorite": {"message": "รายการโปรด"}, "unfavorite": {"message": "Unfavorite"}, "itemAddedToFavorites": {"message": "Item added to favorites"}, "itemRemovedFromFavorites": {"message": "<PERSON><PERSON> removed from favorites"}, "notes": {"message": "โน้ต"}, "privateNote": {"message": "Private note"}, "note": {"message": "โน้ต"}, "editItem": {"message": "แก้ไขรายการ"}, "folder": {"message": "โฟลเดอร์"}, "deleteItem": {"message": "ลบรายการ"}, "viewItem": {"message": "ดูรายการ"}, "launch": {"message": "เริ่ม"}, "launchWebsite": {"message": "Launch website"}, "launchWebsiteName": {"message": "Launch website $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret item"}}}, "website": {"message": "เว็บไซต์"}, "toggleVisibility": {"message": "Toggle Visibility"}, "manage": {"message": "จัดการ"}, "other": {"message": "อื่น ๆ"}, "unlockMethods": {"message": "Unlock options"}, "unlockMethodNeededToChangeTimeoutActionDesc": {"message": "Set up an unlock method to change your vault timeout action."}, "unlockMethodNeeded": {"message": "Set up an unlock method in Settings"}, "sessionTimeoutHeader": {"message": "Session timeout"}, "vaultTimeoutHeader": {"message": "Vault timeout"}, "otherOptions": {"message": "Other options"}, "rateExtension": {"message": "Rate the Extension"}, "browserNotSupportClipboard": {"message": "เว็บเบราว์เซอร์ของคุณไม่รองรับการคัดลอกคลิปบอร์ดอย่างง่าย คัดลอกด้วยตนเองแทน"}, "verifyYourIdentity": {"message": "Verify your identity"}, "weDontRecognizeThisDevice": {"message": "We don't recognize this device. Enter the code sent to your email to verify your identity."}, "continueLoggingIn": {"message": "Continue logging in"}, "yourVaultIsLocked": {"message": "ตู้เซฟของคุณถูกล็อก ยืนยันตัวตนของคุณเพื่อดำเนินการต่อ"}, "yourVaultIsLockedV2": {"message": "Your vault is locked"}, "yourAccountIsLocked": {"message": "Your account is locked"}, "or": {"message": "or"}, "unlock": {"message": "ปลดล็อค"}, "loggedInAsOn": {"message": "ล็อกอินด้วย $EMAIL$ บน $HOSTNAME$", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}, "hostname": {"content": "$2", "example": "bitwarden.com"}}}, "invalidMasterPassword": {"message": "รหัสผ่านหลักไม่ถูกต้อง"}, "vaultTimeout": {"message": "ระยะเวลาล็อกตู้เซฟ"}, "vaultTimeout1": {"message": "Timeout"}, "lockNow": {"message": "Lock Now"}, "lockAll": {"message": "Lock all"}, "immediately": {"message": "ทันที"}, "tenSeconds": {"message": "10 วินาที"}, "twentySeconds": {"message": "20 วินาที"}, "thirtySeconds": {"message": "30 วินาที"}, "oneMinute": {"message": "1 นาที"}, "twoMinutes": {"message": "2 นาที"}, "fiveMinutes": {"message": "5 นาที"}, "fifteenMinutes": {"message": "15 นาที"}, "thirtyMinutes": {"message": "30 นาที"}, "oneHour": {"message": "1 ชั่วโมง"}, "fourHours": {"message": "4 ชั่วโมง"}, "onLocked": {"message": "On Locked"}, "onRestart": {"message": "On Restart"}, "never": {"message": "ไม่อีกเลย"}, "security": {"message": "ความปลอดภัย"}, "confirmMasterPassword": {"message": "Confirm master password"}, "masterPassword": {"message": "Master password"}, "masterPassImportant": {"message": "Your master password cannot be recovered if you forget it!"}, "masterPassHintLabel": {"message": "Master password hint"}, "errorOccurred": {"message": "พบข้อผิดพลาด"}, "emailRequired": {"message": "ต้องระบุอีเมล"}, "invalidEmail": {"message": "ที่อยู่อีเมลไม่ถูกต้อง"}, "masterPasswordRequired": {"message": "ต้องใช้รหัสผ่านหลัก"}, "confirmMasterPasswordRequired": {"message": "ต้องพิมพ์รหัสผ่านหลักอีกครั้ง"}, "masterPasswordMinlength": {"message": "Master password must be at least $VALUE$ characters long.", "description": "The Master Password must be at least a specific number of characters long.", "placeholders": {"value": {"content": "$1", "example": "8"}}}, "masterPassDoesntMatch": {"message": "การยืนยันรหัสผ่านหลักไม่ตรงกัน"}, "newAccountCreated": {"message": "บัญชีใหม่ของคุณถูกสร้างขึ้นแล้ว! ตอนนี้คุณสามารถเข้าสู่ระบบ"}, "newAccountCreated2": {"message": "Your new account has been created!"}, "youHaveBeenLoggedIn": {"message": "You have been logged in!"}, "youSuccessfullyLoggedIn": {"message": "You successfully logged in"}, "youMayCloseThisWindow": {"message": "You may close this window"}, "masterPassSent": {"message": "เราได้ส่งอีเมลพร้อมคำใบ้รหัสผ่านหลักของคุณออกไปแล้ว"}, "verificationCodeRequired": {"message": "ต้องระบุโค้ดยืนยัน"}, "webauthnCancelOrTimeout": {"message": "The authentication was cancelled or took too long. Please try again."}, "invalidVerificationCode": {"message": "โค้ดยืนยันไม่ถูกต้อง"}, "valueCopied": {"message": "$VALUE$ copied", "description": "Value has been copied to the clipboard.", "placeholders": {"value": {"content": "$1", "example": "Password"}}}, "autofillError": {"message": "Unable to auto-fill the selected login on this page. Copy/paste your username and/or password instead."}, "totpCaptureError": {"message": "Unable to scan QR code from the current webpage"}, "totpCaptureSuccess": {"message": "Authenticator key added"}, "totpCapture": {"message": "Scan authenticator QR code from current webpage"}, "totpHelperTitle": {"message": "Make 2-step verification seamless"}, "totpHelper": {"message": "Bitwarden can store and fill 2-step verification codes. Copy and paste the key into this field."}, "totpHelperWithCapture": {"message": "Bitwarden can store and fill 2-step verification codes. Select the camera icon to take a screenshot of this website's authenticator QR code, or copy and paste the key into this field."}, "learnMoreAboutAuthenticators": {"message": "Learn more about authenticators"}, "copyTOTP": {"message": "Copy Authenticator key (TOTP)"}, "loggedOut": {"message": "ออกจากระบบ"}, "loggedOutDesc": {"message": "You have been logged out of your account."}, "loginExpired": {"message": "เซสชันของคุณหมดอายุแล้ว"}, "logIn": {"message": "Log in"}, "logInToBitwarden": {"message": "Log in to Bitwarden"}, "enterTheCodeSentToYourEmail": {"message": "Enter the code sent to your email"}, "enterTheCodeFromYourAuthenticatorApp": {"message": "Enter the code from your authenticator app"}, "pressYourYubiKeyToAuthenticate": {"message": "Press your YubiKey to authenticate"}, "duoTwoFactorRequiredPageSubtitle": {"message": "Duo two-step login is required for your account. Follow the steps below to finish logging in."}, "followTheStepsBelowToFinishLoggingIn": {"message": "Follow the steps below to finish logging in."}, "followTheStepsBelowToFinishLoggingInWithSecurityKey": {"message": "Follow the steps below to finish logging in with your security key."}, "restartRegistration": {"message": "Restart registration"}, "expiredLink": {"message": "Expired link"}, "pleaseRestartRegistrationOrTryLoggingIn": {"message": "Please restart registration or try logging in."}, "youMayAlreadyHaveAnAccount": {"message": "You may already have an account"}, "logOutConfirmation": {"message": "คุณต้องการล็อกเอาต์ใช่หรือไม่?"}, "yes": {"message": "ใช่"}, "no": {"message": "ไม่ใช่"}, "location": {"message": "Location"}, "unexpectedError": {"message": "An unexpected error has occured."}, "nameRequired": {"message": "ต้องระบุชื่อ"}, "addedFolder": {"message": "เพิ่มโฟลเดอร์แล้ว"}, "twoStepLoginConfirmation": {"message": "Two-step login makes your account more secure by requiring you to enter a security code from an authenticator app whenever you log in. Two-step login can be enabled on the bitwarden.com web vault. Do you want to visit the website now?"}, "twoStepLoginConfirmationContent": {"message": "Make your account more secure by setting up two-step login in the Bitwarden web app."}, "twoStepLoginConfirmationTitle": {"message": "Continue to web app?"}, "editedFolder": {"message": "Edited Folder"}, "deleteFolderConfirmation": {"message": "คุณแน่ใจหรือไม่ว่าต้องการลบโฟลเดอร์นี้"}, "deletedFolder": {"message": "ลบโฟลเดอร์แล้ว"}, "gettingStartedTutorial": {"message": "Getting Started Tutorial"}, "gettingStartedTutorialVideo": {"message": "ดูบทช่วยสอนการเริ่มต้นของเราเพื่อเรียนรู้วิธีใช้ประโยชน์สูงสุดจากส่วนขยายเบราว์เซอร์"}, "syncingComplete": {"message": "การซิงก์เสร็จสมบูรณ์"}, "syncingFailed": {"message": "การซิงก์ล้มเหลว"}, "passwordCopied": {"message": "คัดลอกรหัสผ่านแล้ว"}, "uri": {"message": "URI"}, "uriPosition": {"message": "URI $POSITION$", "description": "A listing of URIs. Ex: URI 1, URI 2, URI 3, etc.", "placeholders": {"position": {"content": "$1", "example": "2"}}}, "newUri": {"message": "เพิ่ม URI ใหม่"}, "addDomain": {"message": "Add domain", "description": "'Domain' here refers to an internet domain name (e.g. 'bitwarden.com') and the message in whole described the act of putting a domain value into the context."}, "addedItem": {"message": "เพิ่มรายการแล้ว"}, "editedItem": {"message": "แก้ไขรายการแล้ว"}, "deleteItemConfirmation": {"message": "คุณต้องการส่งไปยังถังขยะใช่หรือไม่?"}, "deletedItem": {"message": "ส่งรายการไปยังถังขยะแล้ว"}, "overwritePassword": {"message": "Overwrite Password"}, "overwritePasswordConfirmation": {"message": "คุณต้องการเขียนทับรหัสผ่านปัจจุบันใช่หรือไม่?"}, "overwriteUsername": {"message": "เขียนทับชื่อผู้ใช้"}, "overwriteUsernameConfirmation": {"message": "คุณแน่ใจหรือไม่ว่าต้องการเขียนทับชื่อผู้ใช้ปัจจุบัน"}, "searchFolder": {"message": "ค้นหาในโพลเดอร์"}, "searchCollection": {"message": "คอลเลกชันการค้นหา"}, "searchType": {"message": "ประเภทการค้นหา"}, "noneFolder": {"message": "No Folder", "description": "This is the folder for uncategorized items"}, "enableAddLoginNotification": {"message": "ถามเพื่อให้เพิ่มการเข้าสู่ระบบ"}, "vaultSaveOptionsTitle": {"message": "Save to vault options"}, "addLoginNotificationDesc": {"message": "The \"Add Login Notification\" automatically prompts you to save new logins to your vault whenever you log into them for the first time."}, "addLoginNotificationDescAlt": {"message": "Ask to add an item if one isn't found in your vault. Applies to all logged in accounts."}, "showCardsInVaultViewV2": {"message": "Always show cards as Autofill suggestions on Vault view"}, "showCardsCurrentTab": {"message": "แสดงการ์ดบนหน้าแท็บ"}, "showCardsCurrentTabDesc": {"message": "บัตรรายการในหน้าแท็บเพื่อให้ป้อนอัตโนมัติได้ง่าย"}, "showIdentitiesInVaultViewV2": {"message": "Always show identities as Autofill suggestions on Vault view"}, "showIdentitiesCurrentTab": {"message": "แสดงตัวตนบนหน้าแท็บ"}, "showIdentitiesCurrentTabDesc": {"message": "แสดงรายการข้อมูลประจำตัวในหน้าแท็บเพื่อให้ป้อนอัตโนมัติได้ง่าย"}, "clickToAutofillOnVault": {"message": "Click items to autofill on Vault view"}, "clickToAutofill": {"message": "Click items in autofill suggestion to fill"}, "clearClipboard": {"message": "ล้างคลิปบอร์ด", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "clearClipboardDesc": {"message": "ล้างค่าที่คัดลอกโดยอัตโนมัติจากคลิปบอร์ดของคุณ", "description": "Clipboard is the operating system thing where you copy/paste data to on your device."}, "notificationAddDesc": {"message": "Should bitwarden remember this password for you?"}, "notificationAddSave": {"message": "Yes, Save Now"}, "notificationViewAria": {"message": "View $ITEMNAME$, opens in new window", "placeholders": {"itemName": {"content": "$1"}}, "description": "Aria label for the view button in notification bar confirmation message"}, "notificationNewItemAria": {"message": "New Item, opens in new window", "description": "Aria label for the new item button in notification bar confirmation message when error is prompted"}, "notificationEditTooltip": {"message": "Edit before saving", "description": "Tooltip and Aria label for edit button on cipher item"}, "newNotification": {"message": "New notification"}, "labelWithNotification": {"message": "$LABEL$: New notification", "description": "Label for the notification with a new login suggestion.", "placeholders": {"label": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "notificationLoginSaveConfirmation": {"message": "saved to Bitwarden.", "description": "Shown to user after item is saved."}, "notificationLoginUpdatedConfirmation": {"message": "updated in Bitwarden.", "description": "Shown to user after item is updated."}, "selectItemAriaLabel": {"message": "Select $ITEMTYPE$, $ITEMNAME$", "description": "Used by screen readers. $1 is the item type (like vault or folder), $2 is the selected item name.", "placeholders": {"itemType": {"content": "$1"}, "itemName": {"content": "$2"}}}, "saveAsNewLoginAction": {"message": "Save as new login", "description": "Button text for saving login details as a new entry."}, "updateLoginAction": {"message": "Update login", "description": "Button text for updating an existing login entry."}, "unlockToSave": {"message": "Unlock to save this login", "description": "User prompt to take action in order to save the login they just entered."}, "saveLogin": {"message": "Save login", "description": "Prompt asking the user if they want to save their login details."}, "updateLogin": {"message": "Update existing login", "description": "Prompt asking the user if they want to update an existing login entry."}, "loginSaveSuccess": {"message": "<PERSON><PERSON> saved", "description": "Message displayed when login details are successfully saved."}, "loginUpdateSuccess": {"message": "Login updated", "description": "Message displayed when login details are successfully updated."}, "loginUpdateTaskSuccess": {"message": "Great job! You took the steps to make you and $ORGANIZATION$ more secure.", "placeholders": {"organization": {"content": "$1"}}, "description": "Shown to user after login is updated."}, "loginUpdateTaskSuccessAdditional": {"message": "Thank you for making $ORGANIZATION$ more secure. You have $TASK_COUNT$ more passwords to update.", "placeholders": {"organization": {"content": "$1"}, "task_count": {"content": "$2"}}, "description": "Shown to user after login is updated."}, "nextSecurityTaskAction": {"message": "Change next password", "description": "Message prompting user to undertake completion of another security task."}, "saveFailure": {"message": "Error saving", "description": "Error message shown when the system fails to save login details."}, "saveFailureDetails": {"message": "Oh no! We couldn't save this. Try entering the details manually.", "description": "Detailed error message shown when saving login details fails."}, "enableChangedPasswordNotification": {"message": "ขอให้ปรับปรุงการเข้าสู่ระบบที่มีอยู่"}, "changedPasswordNotificationDesc": {"message": "Ask to update a login's password when a change is detected on a website."}, "changedPasswordNotificationDescAlt": {"message": "Ask to update a login's password when a change is detected on a website. Applies to all logged in accounts."}, "enableUsePasskeys": {"message": "Ask to save and use passkeys"}, "usePasskeysDesc": {"message": "Ask to save new passkeys or log in with passkeys stored in your vault. Applies to all logged in accounts."}, "notificationChangeDesc": {"message": "คุณต้องการอัปเดตรหัสผ่านนี้ใน Bitwarden หรือไม่?"}, "notificationChangeSave": {"message": "Yes, Update Now"}, "notificationUnlockDesc": {"message": "Unlock your Bitwarden vault to complete the autofill request."}, "notificationUnlock": {"message": "Unlock"}, "additionalOptions": {"message": "Additional options"}, "enableContextMenuItem": {"message": "แสดงตัวเลือกเมนูบริบท"}, "contextMenuItemDesc": {"message": "ใช้การคลิกสำรองเพื่อเข้าถึงการสร้างรหัสผ่านและการเข้าสู่ระบบที่ตรงกันสำหรับเว็บไซต์ "}, "contextMenuItemDescAlt": {"message": "Use a secondary click to access password generation and matching logins for the website. Applies to all logged in accounts."}, "defaultUriMatchDetection": {"message": "การตรวจจับการจับคู่ URI เริ่มต้น", "description": "Default URI match detection for autofill."}, "defaultUriMatchDetectionDesc": {"message": "เลือกวิธีเริ่มต้นในการจัดการการตรวจหาการจับคู่ URI สำหรับการเข้าสู่ระบบเมื่อดำเนินการต่างๆ เช่น การป้อนอัตโนมัติ"}, "theme": {"message": "ธีม"}, "themeDesc": {"message": "Change the application's color theme."}, "themeDescAlt": {"message": "Change the application's color theme. Applies to all logged in accounts."}, "dark": {"message": "มืด", "description": "Dark color"}, "light": {"message": "สว่าง", "description": "Light color"}, "exportFrom": {"message": "Export from"}, "exportVault": {"message": "Export <PERSON>"}, "fileFormat": {"message": "File Format"}, "fileEncryptedExportWarningDesc": {"message": "This file export will be password protected and require the file password to decrypt."}, "filePassword": {"message": "File password"}, "exportPasswordDescription": {"message": "This password will be used to export and import this file"}, "accountRestrictedOptionDescription": {"message": "Use your account encryption key, derived from your account's username and Master Password, to encrypt the export and restrict import to only the current Bitwarden account."}, "passwordProtectedOptionDescription": {"message": "Set a file password to encrypt the export and import it to any Bitwarden account using the password for decryption."}, "exportTypeHeading": {"message": "Export type"}, "accountRestricted": {"message": "Account restricted"}, "filePasswordAndConfirmFilePasswordDoNotMatch": {"message": "“File password”  and “Confirm file password“ do not match."}, "warning": {"message": "คำเตือน", "description": "WARNING (should stay in capitalized letters if the language permits)"}, "warningCapitalized": {"message": "Warning", "description": "Warning (should maintain locale-relevant capitalization)"}, "confirmVaultExport": {"message": "ยืนยันการส่งออกตู้นิรภัย"}, "exportWarningDesc": {"message": "This export contains your vault data in an unencrypted format. You should not store or send the exported file over unsecure channels (such as email). Delete it immediately after you are done using it."}, "encExportKeyWarningDesc": {"message": "การส่งออกนี้เข้ารหัสข้อมูลของคุณโดยใช้คีย์เข้ารหัสของบัญชีของคุณ หากคุณเคยหมุนเวียนคีย์เข้ารหัสของบัญชี คุณควรส่งออกอีกครั้ง เนื่องจากคุณจะไม่สามารถถอดรหัสไฟล์ส่งออกนี้ได้"}, "encExportAccountWarningDesc": {"message": "คีย์การเข้ารหัสบัญชีจะไม่ซ้ำกันสำหรับบัญชีผู้ใช้ Bitwarden แต่ละบัญชี ดังนั้นคุณจึงไม่สามารถนำเข้าการส่งออกที่เข้ารหัสไปยังบัญชีอื่นได้"}, "exportMasterPassword": {"message": "ป้อนรหัสผ่านหลักของคุณเพื่อส่งออกข้อมูลตู้นิรภัยของคุณ"}, "shared": {"message": "แชร์แล้ว"}, "bitwardenForBusinessPageDesc": {"message": "Bitwarden for Business allows you to share your vault items with others by using an organization. Learn more on the bitwarden.com website."}, "moveToOrganization": {"message": "ย้ายไปยังแบบองค์กร"}, "movedItemToOrg": {"message": "ย้าย $ITEMNAME$ ไปยัง $ORGNAME$ แล้ว", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "orgname": {"content": "$2", "example": "Company Name"}}}, "moveToOrgDesc": {"message": "เลือกองค์กรที่คุณต้องการย้ายรายการนี้ไป การย้ายไปยังองค์กรจะโอนความเป็นเจ้าของรายการไปยังองค์กรนั้น คุณจะไม่ได้เป็นเจ้าของโดยตรงของรายการนี้อีกต่อไปเมื่อมีการย้ายแล้ว"}, "learnMore": {"message": "เรียนรู้เพิ่มเติม"}, "authenticatorKeyTotp": {"message": "Authenticator Key (TOTP)"}, "verificationCodeTotp": {"message": "Verification Code (TOTP)"}, "copyVerificationCode": {"message": "Copy Verification Code"}, "attachments": {"message": "ไฟล์แนบ"}, "deleteAttachment": {"message": "ลบไฟล์แนบ"}, "deleteAttachmentConfirmation": {"message": "คุณต้องการลบไฟล์แนบนี้ใช่หรือไม่?"}, "deletedAttachment": {"message": "ลบไฟล์แนบแล้ว"}, "newAttachment": {"message": "Add New Attachment"}, "noAttachments": {"message": "ไม่มีไฟล์แนบ"}, "attachmentSaved": {"message": "บันทึกไฟล์แนบแล้ว"}, "file": {"message": "ไฟล์"}, "fileToShare": {"message": "File to share"}, "selectFile": {"message": "เลือกไฟล์"}, "maxFileSize": {"message": "ขนาดไฟล์สูงสุด คือ 500 MB"}, "featureUnavailable": {"message": "Feature Unavailable"}, "legacyEncryptionUnsupported": {"message": "Legacy encryption is no longer supported. Please contact support to recover your account."}, "premiumMembership": {"message": "Premium Membership"}, "premiumManage": {"message": "Manage Membership"}, "premiumManageAlert": {"message": "คุณสามารถจัดการการเป็นสมาชิกของคุณได้ที่ bitwarden.com web vault คุณต้องการเข้าชมเว็บไซต์ตอนนี้หรือไม่?"}, "premiumRefresh": {"message": "Refresh Membership"}, "premiumNotCurrentMember": {"message": "คุณยังไม่ได้เป็นสมาชิกพรีเมียม"}, "premiumSignUpAndGet": {"message": "สมัครสมาชิกพรีเมี่ยมและรับ:"}, "ppremiumSignUpStorage": {"message": "1 GB of encrypted file storage."}, "premiumSignUpEmergency": {"message": "Emergency access."}, "premiumSignUpTwoStepOptions": {"message": "Proprietary two-step login options such as YubiKey and Duo."}, "ppremiumSignUpReports": {"message": "สุขอนามัยของรหัสผ่าน ความสมบูรณ์ของบัญชี และรายงานการละเมิดข้อมูลเพื่อให้ตู้นิรภัยของคุณปลอดภัย"}, "ppremiumSignUpTotp": {"message": "ตัวสร้างรหัสยืนยัน TOTP (2FA) สำหรับการเข้าสู่ระบบในตู้นิรภัยของคุณ"}, "ppremiumSignUpSupport": {"message": "Priority customer support."}, "ppremiumSignUpFuture": {"message": "All future Premium features. More coming soon!"}, "premiumPurchase": {"message": "Purchase Premium"}, "premiumPurchaseAlertV2": {"message": "You can purchase Premium from your account settings on the Bitwarden web app."}, "premiumCurrentMember": {"message": "You are a Premium member!"}, "premiumCurrentMemberThanks": {"message": "Thank you for supporting bitwarden."}, "premiumFeatures": {"message": "Upgrade to Premium and receive:"}, "premiumPrice": {"message": "All for just $PRICE$ /year!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "premiumPriceV2": {"message": "All for just $PRICE$ per year!", "placeholders": {"price": {"content": "$1", "example": "$10"}}}, "refreshComplete": {"message": "Refresh complete"}, "enableAutoTotpCopy": {"message": "Copy TOTP automatically"}, "disableAutoTotpCopyDesc": {"message": "If a login has an authenticator key, copy the TOTP verification code to your clip-board when you autofill the login."}, "enableAutoBiometricsPrompt": {"message": "Ask for biometrics on launch"}, "premiumRequired": {"message": "Premium Required"}, "premiumRequiredDesc": {"message": "A Premium membership is required to use this feature."}, "authenticationTimeout": {"message": "Authentication timeout"}, "authenticationSessionTimedOut": {"message": "The authentication session timed out. Please restart the login process."}, "verificationCodeEmailSent": {"message": "ส่งโค้ดยืนยันไปยังอีเมล $EMAIL$ แล้ว", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "dontAskAgainOnThisDeviceFor30Days": {"message": "Don't ask again on this device for 30 days"}, "selectAnotherMethod": {"message": "Select another method", "description": "Select another two-step login method"}, "useYourRecoveryCode": {"message": "Use your recovery code"}, "insertU2f": {"message": "Insert your security key into your computer's USB port. If it has a button, touch it."}, "openInNewTab": {"message": "Open in new tab"}, "webAuthnAuthenticate": {"message": "Authenticate WebAuthn"}, "readSecurityKey": {"message": "Read security key"}, "awaitingSecurityKeyInteraction": {"message": "Awaiting security key interaction..."}, "loginUnavailable": {"message": "Login Unavailable"}, "noTwoStepProviders": {"message": "This account has two-step login enabled, however, none of the configured two-step providers are supported by this web browser."}, "noTwoStepProviders2": {"message": "Please use a supported web browser (such as Chrome) and/or add additional providers that are better supported across web browsers (such as an authenticator app)."}, "twoStepOptions": {"message": "Two-step Login Options"}, "selectTwoStepLoginMethod": {"message": "Select two-step login method"}, "recoveryCodeDesc": {"message": "Lost access to all of your two-factor providers? Use your recovery code to turn off all two-factor providers from your account."}, "recoveryCodeTitle": {"message": "Recovery Code"}, "authenticatorAppTitle": {"message": "Authenticator App"}, "authenticatorAppDescV2": {"message": "Enter a code generated by an authenticator app like Bitwarden Authenticator.", "description": "'Bitwarden Authenticator' is a product name and should not be translated."}, "yubiKeyTitleV2": {"message": "Yubico OTP Security Key"}, "yubiKeyDesc": {"message": "Use a YubiKey to access your account. Works with YubiKey 4, 4 Nano, 4C, and NEO devices."}, "duoDescV2": {"message": "Enter a code generated by Duo Security.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "duoOrganizationDesc": {"message": "Verify with Duo Security for your organization using the Duo Mobile app, SMS, phone call, or U2F security key.", "description": "'Duo Security' and 'Duo Mobile' are product names and should not be translated."}, "webAuthnTitle": {"message": "FIDO2 WebAuthn"}, "webAuthnDesc": {"message": "Use any WebAuthn compatible security key to access your account."}, "emailTitle": {"message": "อีเมล"}, "emailDescV2": {"message": "Enter a code sent to your email."}, "selfHostedEnvironment": {"message": "Self-hosted Environment"}, "selfHostedBaseUrlHint": {"message": "Specify the base URL of your on-premises hosted Bitwarden installation. Example: https://bitwarden.company.com"}, "selfHostedCustomEnvHeader": {"message": "For advanced configuration, you can specify the base URL of each service independently."}, "selfHostedEnvFormInvalid": {"message": "You must add either the base Server URL or at least one custom environment."}, "customEnvironment": {"message": "Custom Environment"}, "baseUrl": {"message": "URL ของเซิร์ฟเวอร์"}, "selfHostBaseUrl": {"message": "Self-host server URL", "description": "Label for field requesting a self-hosted integration service URL"}, "apiUrl": {"message": "API Server URL"}, "webVaultUrl": {"message": "Web Vault Server URL"}, "identityUrl": {"message": "Identity Server URL"}, "notificationsUrl": {"message": "Notifications Server URL"}, "iconsUrl": {"message": "Icons Server URL"}, "environmentSaved": {"message": "Environment URLs saved"}, "showAutoFillMenuOnFormFields": {"message": "Show autofill menu on form fields", "description": "Represents the message for allowing the user to enable the autofill overlay"}, "autofillSuggestionsSectionTitle": {"message": "Autofill suggestions"}, "autofillSpotlightTitle": {"message": "Easily find autofill suggestions"}, "autofillSpotlightDesc": {"message": "Turn off your browser's autofill settings, so they don't conflict with Bitwarden."}, "turnOffBrowserAutofill": {"message": "Turn off $BROWSER$ autofill", "placeholders": {"browser": {"content": "$1", "example": "Chrome"}}}, "turnOffAutofill": {"message": "Turn off autofill"}, "showInlineMenuLabel": {"message": "Show autofill suggestions on form fields"}, "showInlineMenuIdentitiesLabel": {"message": "Display identities as suggestions"}, "showInlineMenuCardsLabel": {"message": "Display cards as suggestions"}, "showInlineMenuOnIconSelectionLabel": {"message": "Display suggestions when icon is selected"}, "showInlineMenuOnFormFieldsDescAlt": {"message": "Applies to all logged in accounts."}, "turnOffBrowserBuiltInPasswordManagerSettings": {"message": "Turn off your browser's built in password manager settings to avoid conflicts."}, "turnOffBrowserBuiltInPasswordManagerSettingsLink": {"message": "Edit browser settings."}, "autofillOverlayVisibilityOff": {"message": "Off", "description": "Overlay setting select option for disabling autofill overlay"}, "autofillOverlayVisibilityOnFieldFocus": {"message": "When field is selected (on focus)", "description": "Overlay appearance select option for showing the field on focus of the input element"}, "autofillOverlayVisibilityOnButtonClick": {"message": "When autofill icon is selected", "description": "Overlay appearance select option for showing the field on click of the overlay icon"}, "enableAutoFillOnPageLoadSectionTitle": {"message": "Autofill on page load"}, "enableAutoFillOnPageLoad": {"message": "Enable Auto-fill On Page Load."}, "enableAutoFillOnPageLoadDesc": {"message": "If a login form is detected, autofill when the web page loads."}, "experimentalFeature": {"message": "Compromised or untrusted websites can exploit autofill on page load."}, "learnMoreAboutAutofillOnPageLoadLinkText": {"message": "Learn more about risks"}, "learnMoreAboutAutofill": {"message": "Learn more about autofill"}, "defaultAutoFillOnPageLoad": {"message": "Default autofill setting for login items"}, "defaultAutoFillOnPageLoadDesc": {"message": "You can turn off autofill on page load for individual login items from the item's Edit view."}, "itemAutoFillOnPageLoad": {"message": "Autofill on page load (if set up in Options)"}, "autoFillOnPageLoadUseDefault": {"message": "Use default setting"}, "autoFillOnPageLoadYes": {"message": "Autofill on page load"}, "autoFillOnPageLoadNo": {"message": "Do not autofill on page load"}, "commandOpenPopup": {"message": "Open vault popup"}, "commandOpenSidebar": {"message": "Open vault in sidebar"}, "commandAutofillLoginDesc": {"message": "Autofill the last used login for the current website"}, "commandAutofillCardDesc": {"message": "Autofill the last used card for the current website"}, "commandAutofillIdentityDesc": {"message": "Autofill the last used identity for the current website"}, "commandGeneratePasswordDesc": {"message": "Generate and copy a new random password to the clipboard."}, "commandLockVaultDesc": {"message": "ล็อกตู้เซฟ"}, "customFields": {"message": "Custom Fields"}, "copyValue": {"message": "Copy Value"}, "value": {"message": "ค่า"}, "newCustomField": {"message": "New Custom Field"}, "dragToSort": {"message": "Drag to sort"}, "dragToReorder": {"message": "Drag to reorder"}, "cfTypeText": {"message": "ข้อความ"}, "cfTypeHidden": {"message": "Hidden"}, "cfTypeBoolean": {"message": "Boolean"}, "cfTypeCheckbox": {"message": "Checkbox"}, "cfTypeLinked": {"message": "Linked", "description": "This describes a field that is 'linked' (tied) to another field."}, "linkedValue": {"message": "Linked value", "description": "This describes a value that is 'linked' (tied) to another value."}, "popup2faCloseMessage": {"message": "Clicking outside the popup window to check your email for your verification code will cause this popup to close. Do you want to open this popup in a new window so that it does not close?"}, "popupU2fCloseMessage": {"message": "This browser cannot process U2F requests in this popup window. Do you want to open this popup in a new window so that you can log in using U2F?"}, "enableFavicon": {"message": "Show website icons"}, "faviconDesc": {"message": "Show a recognizable image next to each login."}, "faviconDescAlt": {"message": "Show a recognizable image next to each login. Applies to all logged in accounts."}, "enableBadgeCounter": {"message": "Show badge counter"}, "badgeCounterDesc": {"message": "Indicate how many logins you have for the current web page."}, "cardholderName": {"message": "Cardholder Name"}, "number": {"message": "หมายเลข"}, "brand": {"message": "แบรนด์"}, "expirationMonth": {"message": "Expiration Month"}, "expirationYear": {"message": "Expiration Year"}, "expiration": {"message": "วันหมดอายุ"}, "january": {"message": "มกราคม"}, "february": {"message": "กุมภาพันธ์"}, "march": {"message": "มีนาคม"}, "april": {"message": "เมษายน"}, "may": {"message": "พฤษภาคม"}, "june": {"message": "มิถุนายน"}, "july": {"message": "กรกฎาคม"}, "august": {"message": "สิงหาคม"}, "september": {"message": "กันยายน"}, "october": {"message": "ตุลาคม"}, "november": {"message": "พฤศจิกายน"}, "december": {"message": "ธันวาคม"}, "securityCode": {"message": "Security Code"}, "ex": {"message": "ex."}, "title": {"message": "คำนำหน้า"}, "mr": {"message": "นาย"}, "mrs": {"message": "นาง"}, "ms": {"message": "นางสาว"}, "dr": {"message": "ดร."}, "mx": {"message": "Mx"}, "firstName": {"message": "First Name"}, "middleName": {"message": "Middle Name"}, "lastName": {"message": "Last Name"}, "fullName": {"message": "ชื่อเต็ม"}, "identityName": {"message": "Identity Name"}, "company": {"message": "บริษัท"}, "ssn": {"message": "หมายเลขประกันสังคม"}, "passportNumber": {"message": "หมายเลขหนังสือเดินทาง"}, "licenseNumber": {"message": "หมายเลขใบอนุญาต"}, "email": {"message": "อีเมล"}, "phone": {"message": "โทรศัพท์"}, "address": {"message": "ที่อยู่"}, "address1": {"message": "ที่อยู่ 1"}, "address2": {"message": "ที่อยู่ 2"}, "address3": {"message": "ที่อยู่ 3"}, "cityTown": {"message": "เมือง"}, "stateProvince": {"message": "รัฐ / จังหวัด"}, "zipPostalCode": {"message": "รหัสไปรษณีย์"}, "country": {"message": "ประเทศ"}, "type": {"message": "ชนิด"}, "typeLogin": {"message": "ล็อกอิน"}, "typeLogins": {"message": "ล็อกอิน"}, "typeSecureNote": {"message": "Secure Note"}, "typeCard": {"message": "บัตรเครดิต"}, "typeIdentity": {"message": "ข้อมูลระบุตัวตน"}, "typeSshKey": {"message": "SSH key"}, "newItemHeader": {"message": "New $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "editItemHeader": {"message": "Edit $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "viewItemHeader": {"message": "View $TYPE$", "placeholders": {"type": {"content": "$1", "example": "<PERSON><PERSON>"}}}, "passwordHistory": {"message": "ประวัติของรหัสผ่าน"}, "generatorHistory": {"message": "Generator history"}, "clearGeneratorHistoryTitle": {"message": "Clear generator history"}, "cleargGeneratorHistoryDescription": {"message": "If you continue, all entries will be permanently deleted from generator's history. Are you sure you want to continue?"}, "back": {"message": "ย้อนกลับ"}, "collections": {"message": "คอลเลกชัน"}, "nCollections": {"message": "$COUNT$ collections", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "favorites": {"message": "รายการโปรด"}, "popOutNewWindow": {"message": "เปิดหน้าต่างใหม่"}, "refresh": {"message": "รีเฟรช"}, "cards": {"message": "บัตร"}, "identities": {"message": "ข้อมูลระบุตัวตน"}, "logins": {"message": "เข้าสู่ระบบ"}, "secureNotes": {"message": "Secure Notes"}, "sshKeys": {"message": "SSH Keys"}, "clear": {"message": "ลบทิ้ง", "description": "To clear something out. example: To clear browser history."}, "checkPassword": {"message": "ตรวจสอบว่ารหัสผ่านถูกเปิดเผยหรือไม่"}, "passwordExposed": {"message": "This password has been exposed $VALUE$ time(s) in data breaches. You should change it.", "placeholders": {"value": {"content": "$1", "example": "2"}}}, "passwordSafe": {"message": "ไม่พบรหัสผ่านนี้ในการละเมิดข้อมูลที่มี ควรใช้อย่างปลอดภัย"}, "baseDomain": {"message": "โดเมนพื้นฐาน", "description": "Domain name. Ex. website.com"}, "baseDomainOptionRecommended": {"message": "Base domain (recommended)", "description": "Domain name. Ex. website.com"}, "domainName": {"message": "ชื่อโดเมน", "description": "Domain name. Ex. website.com"}, "host": {"message": "โฮสต์", "description": "A URL's host value. For example, the host of https://sub.domain.com:443 is 'sub.domain.com:443'."}, "exact": {"message": "ถูกต้อง"}, "startsWith": {"message": "เริ่มต้นด้วย"}, "regEx": {"message": "นิพจน์ทั่วไป", "description": "A programming term, also known as 'RegEx'."}, "matchDetection": {"message": "Match Detection", "description": "URI match detection for autofill."}, "defaultMatchDetection": {"message": "การตรวจจับการจับคู่เริ่มต้น", "description": "Default URI match detection for autofill."}, "toggleOptions": {"message": "Toggle Options"}, "toggleCurrentUris": {"message": "สลับ URI ปัจจุบัน", "description": "Toggle the display of the URIs of the currently open tabs in the browser."}, "currentUri": {"message": "URI ปัจจุบัน", "description": "The URI of one of the current open tabs in the browser."}, "organization": {"message": "องค์กร", "description": "An entity of multiple related people (ex. a team or business organization)."}, "types": {"message": "ชนิด"}, "allItems": {"message": "รายการทั้งหมด"}, "noPasswordsInList": {"message": "ไม่มีรหัสผ่านที่จะแสดง"}, "clearHistory": {"message": "Clear history"}, "nothingToShow": {"message": "Nothing to show"}, "nothingGeneratedRecently": {"message": "You haven't generated anything recently"}, "remove": {"message": "ลบ"}, "default": {"message": "ค่าเริ่มต้น"}, "dateUpdated": {"message": "อัปเดตแล้ว", "description": "ex. Date this item was updated"}, "dateCreated": {"message": "สร้างเมื่อ", "description": "ex. Date this item was created"}, "datePasswordUpdated": {"message": "อัปเดต Password แล้ว", "description": "ex. Date this password was updated"}, "neverLockWarning": {"message": "คุณแน่ใจหรือไม่ว่าต้องการใช้ตัวเลือก \"ไม่เคย\" การตั้งค่าตัวเลือกการล็อกเป็น \"ไม่\" จะเก็บคีย์เข้ารหัสของห้องนิรภัยไว้ในอุปกรณ์ของคุณ หากคุณใช้ตัวเลือกนี้ คุณควรตรวจสอบให้แน่ใจว่าคุณปกป้องอุปกรณ์ของคุณอย่างเหมาะสม"}, "noOrganizationsList": {"message": "You do not belong to any organizations. Organizations allow you to securely share items with other users."}, "noCollectionsInList": {"message": "ไม่มีคอลเลกชันที่จะแสดง"}, "ownership": {"message": "เจ้าของ"}, "whoOwnsThisItem": {"message": "ใครเป็นเจ้าของรายการนี้?"}, "strong": {"message": "แข็งแรง", "description": "ex. A strong password. Scale: Weak -> Good -> Strong"}, "good": {"message": "ไม่เลว", "description": "ex. A good password. Scale: Weak -> Good -> Strong"}, "weak": {"message": "ง่ายเกินไป", "description": "ex. A weak password. Scale: Weak -> Good -> Strong"}, "weakMasterPassword": {"message": "Weak Master Password"}, "weakMasterPasswordDesc": {"message": "รหัสผ่านหลักที่คุณเลือกนั้นไม่รัดกุม คุณควรใช้รหัสผ่านหลักที่รัดกุม (หรือวลีรหัสผ่าน) เพื่อปกป้องบัญชี Bitwarden ของคุณอย่างเหมาะสม คุณแน่ใจหรือไม่ว่าต้องการใช้รหัสผ่านหลักนี้"}, "pin": {"message": "PIN", "description": "PIN code. Ex. The short code (often numeric) that you use to unlock a device."}, "unlockWithPin": {"message": "ปลดล็อกด้วย PIN"}, "setYourPinTitle": {"message": "Set PIN"}, "setYourPinButton": {"message": "Set PIN"}, "setYourPinCode": {"message": "ตั้ง PIN เพื่อใช้ปลดล็อก Bitwarden ทั้งนี้ หากคุณล็อกเอาต์ออกจากแอปโดยสมบูรณ์จะเป็นการลบการตั้งค่า PIN ของคุณด้วย"}, "setPinCode": {"message": "You can use this PIN to unlock Bitwarden. Your PIN will be reset if you ever fully log out of the application."}, "pinRequired": {"message": "ต้องระบุ PIN"}, "invalidPin": {"message": "PIN ไม่ถูกต้อง"}, "tooManyInvalidPinEntryAttemptsLoggingOut": {"message": "Too many invalid PIN entry attempts. Logging out."}, "unlockWithBiometrics": {"message": "ปลดล็อกด้วยไบโอเมตริก"}, "unlockWithMasterPassword": {"message": "Unlock with master password"}, "awaitDesktop": {"message": "Awaiting confirmation from desktop"}, "awaitDesktopDesc": {"message": "โปรดยืนยันการใช้ไบโอเมตริกในแอปพลิเคชันเดสก์ท็อป Bitwarden เพื่อตั้งค่าไบโอเมตริกสำหรับเบราว์เซอร์"}, "lockWithMasterPassOnRestart": {"message": "ล็อคด้วยรหัสผ่านหลักเมื่อรีสตาร์ทเบราว์เซอร์"}, "lockWithMasterPassOnRestart1": {"message": "Require master password on browser restart"}, "selectOneCollection": {"message": "คุณต้องเลือกอย่างน้อยหนึ่งคอลเลกชัน"}, "cloneItem": {"message": "โคลนรายการ"}, "clone": {"message": "โคลน"}, "passwordGenerator": {"message": "Password generator"}, "usernameGenerator": {"message": "Username generator"}, "useThisEmail": {"message": "Use this email"}, "useThisPassword": {"message": "Use this password"}, "useThisPassphrase": {"message": "Use this passphrase"}, "useThisUsername": {"message": "Use this username"}, "securePasswordGenerated": {"message": "Secure password generated! Don't forget to also update your password on the website."}, "useGeneratorHelpTextPartOne": {"message": "Use the generator", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "useGeneratorHelpTextPartTwo": {"message": "to create a strong unique password", "description": "This will be used as part of a larger sentence, broken up to include the generator icon. The full sentence will read 'Use the generator [GENERATOR_ICON] to create a strong unique password'"}, "vaultCustomization": {"message": "Vault customization"}, "vaultTimeoutAction": {"message": "การดำเนินการหลังหมดเวลาล็อคตู้เซฟ"}, "vaultTimeoutAction1": {"message": "Timeout action"}, "lock": {"message": "ล็อก", "description": "Verb form: to make secure or inaccessible by"}, "trash": {"message": "ถังขยะ", "description": "Noun: a special folder to hold deleted items"}, "searchTrash": {"message": "ค้นหาในถังขยะ"}, "permanentlyDeleteItem": {"message": "ลบรายการอย่างถาวร"}, "permanentlyDeleteItemConfirmation": {"message": "คุณแน่ใจหรือไม่ว่าต้องการลบรายการนี้อย่างถาวร?"}, "permanentlyDeletedItem": {"message": "ลบรายการอย่างถาวรแล้ว"}, "restoreItem": {"message": "กู้คืนรายการ"}, "restoredItem": {"message": "คืนค่ารายการแล้ว"}, "alreadyHaveAccount": {"message": "Already have an account?"}, "vaultTimeoutLogOutConfirmation": {"message": "การออกจากระบบจะลบการเข้าถึงตู้นิรภัยของคุณทั้งหมด และต้องมีการตรวจสอบสิทธิ์ออนไลน์หลังจากหมดเวลา คุณแน่ใจหรือไม่ว่าต้องการใช้การตั้งค่านี้"}, "vaultTimeoutLogOutConfirmationTitle": {"message": "การยืนยันการดำเนินการหมดเวลา"}, "autoFillAndSave": {"message": "กรอกอัตโนมัติและบันทึก"}, "fillAndSave": {"message": "Fill and save"}, "autoFillSuccessAndSavedUri": {"message": "เติมรายการอัตโนมัติและบันทึก URI แล้ว"}, "autoFillSuccess": {"message": "รายการเติมอัตโนมัติ "}, "insecurePageWarning": {"message": "Warning: This is an unsecured HTTP page, and any information you submit can potentially be seen and changed by others. This Login was originally saved on a secure (HTTPS) page."}, "insecurePageWarningFillPrompt": {"message": "Do you still wish to fill this login?"}, "autofillIframeWarning": {"message": "The form is hosted by a different domain than the URI of your saved login. <PERSON>ose OK to autofill anyway, or Cancel to stop."}, "autofillIframeWarningTip": {"message": "To prevent this warning in the future, save this URI, $HOSTNAME$, to your Bitwarden login item for this site.", "placeholders": {"hostname": {"content": "$1", "example": "www.example.com"}}}, "setMasterPassword": {"message": "ตั้งรหัสผ่านหลัก"}, "currentMasterPass": {"message": "Current master password"}, "newMasterPass": {"message": "New master password"}, "confirmNewMasterPass": {"message": "Confirm new master password"}, "masterPasswordPolicyInEffect": {"message": "นโยบายองค์กรอย่างน้อยหนึ่งนโยบายกำหนดให้รหัสผ่านหลักของคุณเป็นไปตามข้อกำหนดต่อไปนี้:"}, "policyInEffectMinComplexity": {"message": "คะแนนความซับซ้อนขั้นต่ำ $SCORE$", "placeholders": {"score": {"content": "$1", "example": "4"}}}, "policyInEffectMinLength": {"message": "ความยาวอย่างน้อย $LENGTH$ อักขระ", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "policyInEffectUppercase": {"message": "มีตัวพิมพ์ใหญ่อย่างน้อย 1 ตัว"}, "policyInEffectLowercase": {"message": "มีตัวพิมพ์เล็กอย่างน้อย 1 ตัว"}, "policyInEffectNumbers": {"message": "มีตัวเลขอย่างน้อย 1 ตัว"}, "policyInEffectSpecial": {"message": "มีอักขระพิเศษต่อไปนี้อย่างน้อย 1 อักขระ: $CHARS$ ", "placeholders": {"chars": {"content": "$1", "example": "!@#$%^&*"}}}, "masterPasswordPolicyRequirementsNotMet": {"message": "รหัสผ่านหลักใหม่ของคุณไม่เป็นไปตามข้อกำหนดของนโยบาย"}, "receiveMarketingEmailsV2": {"message": "Get advice, announcements, and research opportunities from Bitwarden in your inbox."}, "unsubscribe": {"message": "Unsubscribe"}, "atAnyTime": {"message": "at any time."}, "byContinuingYouAgreeToThe": {"message": "By continuing, you agree to the"}, "and": {"message": "and"}, "acceptPolicies": {"message": "By checking this box you agree to the following:"}, "acceptPoliciesRequired": {"message": "Terms of Service and Privacy Policy have not been acknowledged."}, "termsOfService": {"message": "Terms of Service"}, "privacyPolicy": {"message": "Privacy Policy"}, "yourNewPasswordCannotBeTheSameAsYourCurrentPassword": {"message": "Your new password cannot be the same as your current password."}, "hintEqualsPassword": {"message": "Your password hint cannot be the same as your password."}, "ok": {"message": "ตกลง"}, "errorRefreshingAccessToken": {"message": "Access Token Refresh Error"}, "errorRefreshingAccessTokenDesc": {"message": "No refresh token or API keys found. Please try logging out and logging back in."}, "desktopSyncVerificationTitle": {"message": "Desktop sync verification"}, "desktopIntegrationVerificationText": {"message": "Please verify that the desktop application shows this fingerprint: "}, "desktopIntegrationDisabledTitle": {"message": "Browser integration is not set up"}, "desktopIntegrationDisabledDesc": {"message": "Browser integration is not set up in the Bitwarden desktop application. Please set it up in the settings within the desktop application."}, "startDesktopTitle": {"message": "Start the Bitwarden desktop application"}, "startDesktopDesc": {"message": "The Bitwarden desktop application needs to be started before unlock with biometrics can be used."}, "errorEnableBiometricTitle": {"message": "Unable to set up biometrics"}, "errorEnableBiometricDesc": {"message": "Action was canceled by the desktop application"}, "nativeMessagingInvalidEncryptionDesc": {"message": "Desktop application invalidated the secure communication channel. Please retry this operation"}, "nativeMessagingInvalidEncryptionTitle": {"message": "Desktop communication interrupted"}, "nativeMessagingWrongUserDesc": {"message": "The desktop application is logged into a different account. Please ensure both applications are logged into the same account."}, "nativeMessagingWrongUserTitle": {"message": "Account missmatch"}, "nativeMessagingWrongUserKeyTitle": {"message": "Biometric key missmatch"}, "nativeMessagingWrongUserKeyDesc": {"message": "Biometric unlock failed. The biometric secret key failed to unlock the vault. Please try to set up biometrics again."}, "biometricsNotEnabledTitle": {"message": "Biometrics not set up"}, "biometricsNotEnabledDesc": {"message": "Browser biometrics requires desktop biometric to be set up in the settings first."}, "biometricsNotSupportedTitle": {"message": "Biometrics not supported"}, "biometricsNotSupportedDesc": {"message": "Browser biometrics is not supported on this device."}, "biometricsNotUnlockedTitle": {"message": "User locked or logged out"}, "biometricsNotUnlockedDesc": {"message": "Please unlock this user in the desktop application and try again."}, "biometricsNotAvailableTitle": {"message": "Biometric unlock unavailable"}, "biometricsNotAvailableDesc": {"message": "Biometric unlock is currently unavailable. Please try again later."}, "biometricsFailedTitle": {"message": "Biometrics failed"}, "biometricsFailedDesc": {"message": "Biometrics cannot be completed, consider using a master password or logging out. If this persists, please contact Bitwarden support."}, "nativeMessaginPermissionErrorTitle": {"message": "Permission not provided"}, "nativeMessaginPermissionErrorDesc": {"message": "Without permission to communicate with the Bitwarden Desktop Application we cannot provide biometrics in the browser extension. Please try again."}, "nativeMessaginPermissionSidebarTitle": {"message": "Permission request error"}, "nativeMessaginPermissionSidebarDesc": {"message": "This action cannot be done in the sidebar, please retry the action in the popup or popout."}, "personalOwnershipSubmitError": {"message": "Due to an Enterprise Policy, you are restricted from saving items to your personal vault. Change the Ownership option to an organization and choose from available collections."}, "personalOwnershipPolicyInEffect": {"message": "An organization policy is affecting your ownership options."}, "personalOwnershipPolicyInEffectImports": {"message": "An organization policy has blocked importing items into your individual vault."}, "domainsTitle": {"message": "Domains", "description": "A category title describing the concept of web domains"}, "blockedDomains": {"message": "Blocked domains"}, "learnMoreAboutBlockedDomains": {"message": "Learn more about blocked domains"}, "excludedDomains": {"message": "Excluded domains"}, "excludedDomainsDesc": {"message": "Bitwarden will not ask to save login details for these domains. You must refresh the page for changes to take effect."}, "excludedDomainsDescAlt": {"message": "Bitwarden will not ask to save login details for these domains for all logged in accounts. You must refresh the page for changes to take effect."}, "blockedDomainsDesc": {"message": "Autofill and other related features will not be offered for these websites. You must refresh the page for changes to take effect."}, "autofillBlockedNoticeV2": {"message": "Autofill is blocked for this website."}, "autofillBlockedNoticeGuidance": {"message": "Change this in settings"}, "change": {"message": "Change"}, "changePassword": {"message": "Change password", "description": "Change password button for browser at risk notification on login."}, "changeButtonTitle": {"message": "Change password - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "atRiskPassword": {"message": "At-risk password"}, "atRiskPasswords": {"message": "At-risk passwords"}, "atRiskPasswordDescSingleOrg": {"message": "$ORGANIZATION$ is requesting you change one password because it is at-risk.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}}, "atRiskPasswordsDescSingleOrgPlural": {"message": "$ORGANIZATION$ is requesting you change the $COUNT$ passwords because they are at-risk.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}, "count": {"content": "$2", "example": "2"}}}, "atRiskPasswordsDescMultiOrgPlural": {"message": "Your organizations are requesting you change the $COUNT$ passwords because they are at-risk.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "atRiskChangePrompt": {"message": "Your password for this site is at-risk. $ORGANIZATION$ has requested that you change it.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}, "description": "Notification body when a login triggers an at-risk password change request and the change password domain is known."}, "atRiskNavigatePrompt": {"message": "$ORGANIZATION$ wants you to change this password because it is at-risk. Navigate to your account settings to change the password.", "placeholders": {"organization": {"content": "$1", "example": "Acme Corp"}}, "description": "Notification body when a login triggers an at-risk password change request and no change password domain is provided."}, "reviewAndChangeAtRiskPassword": {"message": "Review and change one at-risk password"}, "reviewAndChangeAtRiskPasswordsPlural": {"message": "Review and change $COUNT$ at-risk passwords", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "changeAtRiskPasswordsFaster": {"message": "Change at-risk passwords faster"}, "changeAtRiskPasswordsFasterDesc": {"message": "Update your settings so you can quickly autofill your passwords and generate new ones"}, "reviewAtRiskLogins": {"message": "Review at-risk logins"}, "reviewAtRiskPasswords": {"message": "Review at-risk passwords"}, "reviewAtRiskLoginsSlideDesc": {"message": "Your organization passwords are at-risk because they are weak, reused, and/or exposed.", "description": "Description of the review at-risk login slide on the at-risk password page carousel"}, "reviewAtRiskLoginSlideImgAltPeriod": {"message": "Illustration of a list of logins that are at-risk."}, "generatePasswordSlideDesc": {"message": "Quickly generate a strong, unique password with the Bitwarden autofill menu on the at-risk site.", "description": "Description of the generate password slide on the at-risk password page carousel"}, "generatePasswordSlideImgAltPeriod": {"message": "Illustration of the Bitwarden autofill menu displaying a generated password."}, "updateInBitwarden": {"message": "Update in Bitwarden"}, "updateInBitwardenSlideDesc": {"message": "Bitwarden will then prompt you to update the password in the password manager.", "description": "Description of the update in Bitwarden slide on the at-risk password page carousel"}, "updateInBitwardenSlideImgAltPeriod": {"message": "Illustration of a Bitwarden’s notification prompting the user to update the login."}, "turnOnAutofill": {"message": "Turn on autofill"}, "turnedOnAutofill": {"message": "Turned on autofill"}, "dismiss": {"message": "<PERSON><PERSON><PERSON>"}, "websiteItemLabel": {"message": "Website $number$ (URI)", "placeholders": {"number": {"content": "$1", "example": "3"}}}, "excludedDomainsInvalidDomain": {"message": "$DOMAIN$ is not a valid domain", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "blockedDomainsSavedSuccess": {"message": "Blocked domain changes saved"}, "excludedDomainsSavedSuccess": {"message": "Excluded domain changes saved"}, "limitSendViews": {"message": "Limit views"}, "limitSendViewsHint": {"message": "No one can view this Send after the limit is reached.", "description": "Displayed under the limit views field on Send"}, "limitSendViewsCount": {"message": "$ACCESSCOUNT$ views left", "description": "Displayed under the limit views field on Send", "placeholders": {"accessCount": {"content": "$1", "example": "2"}}}, "send": {"message": "Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDetails": {"message": "Send details", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendTypeText": {"message": "ข้อความ"}, "sendTypeTextToShare": {"message": "Text to share"}, "sendTypeFile": {"message": "ไฟล์"}, "allSends": {"message": "Send ทั้งหมด", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "maxAccessCountReached": {"message": "Max access count reached", "description": "This text will be displayed after a Send has been accessed the maximum amount of times."}, "hideTextByDefault": {"message": "Hide text by default"}, "expired": {"message": "Expired"}, "passwordProtected": {"message": "Password protected"}, "copyLink": {"message": "Copy link"}, "copySendLink": {"message": "Copy Send link", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "removePassword": {"message": "ลบรหัสผ่าน"}, "delete": {"message": "ลบ"}, "removedPassword": {"message": "รหัสผ่านถูกลบแล้ว"}, "deletedSend": {"message": "Send ถูกลบแล้ว", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLink": {"message": "ลิงก์ Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "disabled": {"message": "Disabled"}, "removePasswordConfirmation": {"message": "คุณต้องการลบรหัสผ่านนี้ใช่หรือไม่"}, "deleteSend": {"message": "ลบ Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendConfirmation": {"message": "คุณต้องการลบ Send นี้ใช่หรือไม่?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deleteSendPermanentConfirmation": {"message": "Are you sure you want to permanently delete this Send?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editSend": {"message": "แก้ไข Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "deletionDate": {"message": "Deletion date"}, "deletionDateDescV2": {"message": "The Send will be permanently deleted on this date.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "expirationDate": {"message": "Expiration date"}, "oneDay": {"message": "1 วัน"}, "days": {"message": "$DAYS$ วัน", "placeholders": {"days": {"content": "$1", "example": "2"}}}, "custom": {"message": "Custom"}, "sendPasswordDescV3": {"message": "Add an optional password for recipients to access this Send.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createSend": {"message": "New Send", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "newPassword": {"message": "New password"}, "sendDisabled": {"message": "Send removed", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendDisabledWarning": {"message": "Due to an enterprise policy, you are only able to delete an existing Send.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSend": {"message": "Send created", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "createdSendSuccessfully": {"message": "Send created successfully!", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHoursSingle": {"message": "The Send will be available to anyone with the link for the next 1 hour.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInHours": {"message": "The Send will be available to anyone with the link for the next $HOURS$ hours.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"hours": {"content": "$1", "example": "5"}}}, "sendExpiresInDaysSingle": {"message": "The Send will be available to anyone with the link for the next 1 day.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendExpiresInDays": {"message": "The Send will be available to anyone with the link for the next $DAYS$ days.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated.", "placeholders": {"days": {"content": "$1", "example": "5"}}}, "sendLinkCopied": {"message": "Send link copied", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "editedSend": {"message": "Send saved", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogText": {"message": "Pop out extension?", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendFilePopoutDialogDesc": {"message": "To create a file Send, you need to pop out the extension to a new window.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendLinuxChromiumFileWarning": {"message": "In order to choose a file, open the extension in the sidebar (if possible) or pop out to a new window by clicking this banner."}, "sendFirefoxFileWarning": {"message": "In order to choose a file using Firefox, open the extension in the sidebar or pop out to a new window by clicking this banner."}, "sendSafariFileWarning": {"message": "In order to choose a file using Safari, pop out to a new window by clicking this banner."}, "popOut": {"message": "Pop out"}, "sendFileCalloutHeader": {"message": "Before you start"}, "expirationDateIsInvalid": {"message": "The expiration date provided is not valid."}, "deletionDateIsInvalid": {"message": "The deletion date provided is not valid."}, "expirationDateAndTimeRequired": {"message": "An expiration date and time are required."}, "deletionDateAndTimeRequired": {"message": "A deletion date and time are required."}, "dateParsingError": {"message": "There was an error saving your deletion and expiration dates."}, "hideYourEmail": {"message": "Hide your email address from viewers."}, "passwordPrompt": {"message": "Master password re-prompt"}, "passwordConfirmation": {"message": "Master password confirmation"}, "passwordConfirmationDesc": {"message": "This action is protected. To continue, please re-enter your master password to verify your identity."}, "emailVerificationRequired": {"message": "Email verification required"}, "emailVerifiedV2": {"message": "Email verified"}, "emailVerificationRequiredDesc": {"message": "You must verify your email to use this feature. You can verify your email in the web vault."}, "updatedMasterPassword": {"message": "Updated master password"}, "updateMasterPassword": {"message": "Update master password"}, "updateMasterPasswordWarning": {"message": "Your master password was recently changed by an administrator in your organization. In order to access the vault, you must update it now. Proceeding will log you out of your current session, requiring you to log back in. Active sessions on other devices may continue to remain active for up to one hour."}, "updateWeakMasterPasswordWarning": {"message": "Your master password does not meet one or more of your organization policies. In order to access the vault, you must update your master password now. Proceeding will log you out of your current session, requiring you to log back in. Active sessions on other devices may continue to remain active for up to one hour."}, "tdeDisabledMasterPasswordRequired": {"message": "Your organization has disabled trusted device encryption. Please set a master password to access your vault."}, "resetPasswordPolicyAutoEnroll": {"message": "Automatic enrollment"}, "resetPasswordAutoEnrollInviteWarning": {"message": "This organization has an enterprise policy that will automatically enroll you in password reset. Enrollment will allow organization administrators to change your master password."}, "selectFolder": {"message": "Select folder..."}, "noFoldersFound": {"message": "No folders found", "description": "Used as a message within the notification bar when no folders are found"}, "orgPermissionsUpdatedMustSetPassword": {"message": "Your organization permissions were updated, requiring you to set a master password.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "orgRequiresYouToSetPassword": {"message": "Your organization requires you to set a master password.", "description": "Used as a card title description on the set password page to explain why the user is there"}, "cardMetrics": {"message": "out of $TOTAL$", "placeholders": {"total": {"content": "$1", "example": "5"}}}, "verificationRequired": {"message": "Verification required", "description": "Default title for the user verification dialog."}, "hours": {"message": "Hours"}, "minutes": {"message": "Minutes"}, "vaultTimeoutPolicyAffectingOptions": {"message": "Enterprise policy requirements have been applied to your timeout options"}, "vaultTimeoutPolicyInEffect": {"message": "Your organization policies have set your maximum allowed vault timeout to $HOURS$ hour(s) and $MINUTES$ minute(s).", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyInEffect1": {"message": "$HOURS$ hour(s) and $MINUTES$ minute(s) maximum.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyMaximumError": {"message": "Timeout exceeds the restriction set by your organization: $HOURS$ hour(s) and $MINUTES$ minute(s) maximum", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}}}, "vaultTimeoutPolicyWithActionInEffect": {"message": "Your organization policies are affecting your vault timeout. Maximum allowed vault timeout is $HOURS$ hour(s) and $MINUTES$ minute(s). Your vault timeout action is set to $ACTION$.", "placeholders": {"hours": {"content": "$1", "example": "5"}, "minutes": {"content": "$2", "example": "5"}, "action": {"content": "$3", "example": "Lock"}}}, "vaultTimeoutActionPolicyInEffect": {"message": "Your organization policies have set your vault timeout action to $ACTION$.", "placeholders": {"action": {"content": "$1", "example": "Lock"}}}, "vaultTimeoutTooLarge": {"message": "Your vault timeout exceeds the restrictions set by your organization."}, "vaultExportDisabled": {"message": "Vault export unavailable"}, "personalVaultExportPolicyInEffect": {"message": "One or more organization policies prevents you from exporting your individual vault."}, "copyCustomFieldNameInvalidElement": {"message": "Unable to identify a valid form element. Try inspecting the HTML instead."}, "copyCustomFieldNameNotUnique": {"message": "No unique identifier found."}, "removeMasterPasswordForOrganizationUserKeyConnector": {"message": "A master password is no longer required for members of the following organization. Please confirm the domain below with your organization administrator."}, "organizationName": {"message": "Organization name"}, "keyConnectorDomain": {"message": "Key Connector domain"}, "leaveOrganization": {"message": "Leave organization"}, "removeMasterPassword": {"message": "Remove master password"}, "removedMasterPassword": {"message": "Master password removed"}, "leaveOrganizationConfirmation": {"message": "Are you sure you want to leave this organization?"}, "leftOrganization": {"message": "You have left the organization."}, "toggleCharacterCount": {"message": "Toggle character count"}, "sessionTimeout": {"message": "Your session has timed out. Please go back and try logging in again."}, "exportingPersonalVaultTitle": {"message": "Exporting individual vault"}, "exportingIndividualVaultDescription": {"message": "Only the individual vault items associated with $EMAIL$ will be exported. Organization vault items will not be included. Only vault item information will be exported and will not include associated attachments.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingIndividualVaultWithAttachmentsDescription": {"message": "Only the individual vault items including attachments associated with $EMAIL$ will be exported. Organization vault items will not be included", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "exportingOrganizationVaultTitle": {"message": "Exporting organization vault"}, "exportingOrganizationVaultDesc": {"message": "Only the organization vault associated with $ORGANIZATION$ will be exported. Items in individual vaults or other organizations will not be included.", "placeholders": {"organization": {"content": "$1", "example": "ACME Moving Co."}}}, "error": {"message": "Error"}, "decryptionError": {"message": "Decryption error"}, "couldNotDecryptVaultItemsBelow": {"message": "<PERSON><PERSON><PERSON> could not decrypt the vault item(s) listed below."}, "contactCSToAvoidDataLossPart1": {"message": "Contact customer success", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "contactCSToAvoidDataLossPart2": {"message": "to avoid additional data loss.", "description": "This is part of a larger sentence. The full sentence will read 'Contact customer success to avoid additional data loss.'"}, "generateUsername": {"message": "Generate username"}, "generateEmail": {"message": "Generate email"}, "spinboxBoundariesHint": {"message": "Value must be between $MIN$ and $MAX$.", "description": "Explains spin box minimum and maximum values to the user", "placeholders": {"min": {"content": "$1", "example": "8"}, "max": {"content": "$2", "example": "128"}}}, "passwordLengthRecommendationHint": {"message": " Use $RECOMMENDED$ characters or more to generate a strong password.", "description": "Appended to `spinboxBoundariesHint` to recommend a length to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "14"}}}, "passphraseNumWordsRecommendationHint": {"message": " Use $RECOMMENDED$ words or more to generate a strong passphrase.", "description": "Appended to `spinboxBoundariesHint` to recommend a number of words to the user. This must include any language-specific 'sentence' separator characters (e.g. a space in english).", "placeholders": {"recommended": {"content": "$1", "example": "6"}}}, "plusAddressedEmail": {"message": "Plus addressed email", "description": "Username generator option that appends a random sub-address to the username. For example: <EMAIL>"}, "plusAddressedEmailDesc": {"message": "Use your email provider's sub-addressing capabilities."}, "catchallEmail": {"message": "Catch-all email"}, "catchallEmailDesc": {"message": "Use your domain's configured catch-all inbox."}, "random": {"message": "Random"}, "randomWord": {"message": "Random word"}, "websiteName": {"message": "Website name"}, "service": {"message": "Service"}, "forwardedEmail": {"message": "Forwarded email alias"}, "forwardedEmailDesc": {"message": "Generate an email alias with an external forwarding service."}, "forwarderDomainName": {"message": "Email domain", "description": "Labels the domain name email forwarder service option"}, "forwarderDomainNameHint": {"message": "Choose a domain that is supported by the selected service", "description": "Guidance provided for email forwarding services that support multiple email domains."}, "forwarderError": {"message": "$SERVICENAME$ error: $ERRORMESSAGE$", "description": "Reports an error returned by a forwarding service to the user.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Invalid characters in domain name."}}}, "forwarderGeneratedBy": {"message": "Generated by <PERSON><PERSON><PERSON>.", "description": "Displayed with the address on the forwarding service's configuration screen."}, "forwarderGeneratedByWithWebsite": {"message": "Website: $WEBSITE$. Generated by Bitwarden.", "description": "Displayed with the address on the forwarding service's configuration screen.", "placeholders": {"WEBSITE": {"content": "$1", "example": "www.example.com"}}}, "forwaderInvalidToken": {"message": "Invalid $SERVICENAME$ API token", "description": "Displayed when the user's API token is empty or rejected by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidTokenWithMessage": {"message": "Invalid $SERVICENAME$ API token: $ERRORMESSAGE$", "description": "Displayed when the user's API token is rejected by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwaderInvalidOperation": {"message": "$SERVICENAME$ refused your request. Please contact your service provider for assistance.", "description": "Displayed when the user is forbidden from using the API by the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwaderInvalidOperationWithMessage": {"message": "$SERVICENAME$ refused your request: $ERRORMESSAGE$", "description": "Displayed when the user is forbidden from using the API by the forwarding service with an error message.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}, "errormessage": {"content": "$2", "example": "Please verify your email address to continue."}}}, "forwarderNoAccountId": {"message": "Unable to obtain $SERVICENAME$ masked email account ID.", "description": "Displayed when the forwarding service fails to return an account ID.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoDomain": {"message": "Invalid $SERVICENAME$ domain.", "description": "Displayed when the domain is empty or domain authorization failed at the forwarding service.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderNoUrl": {"message": "Invalid $SERVICENAME$ url.", "description": "Displayed when the url of the forwarding service wasn't supplied.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownError": {"message": "Unknown $SERVICENAME$ error occurred.", "description": "Displayed when the forwarding service failed due to an unknown error.", "placeholders": {"servicename": {"content": "$1", "example": "SimpleLogin"}}}, "forwarderUnknownForwarder": {"message": "Unknown forwarder: '$SERVICENAME$'.", "description": "Displayed when the forwarding service is not supported.", "placeholders": {"servicename": {"content": "$1", "example": "JustTrust.us"}}}, "hostname": {"message": "Hostname", "description": "Part of a URL."}, "apiAccessToken": {"message": "API Access Token"}, "apiKey": {"message": "API Key"}, "ssoKeyConnectorError": {"message": "Key connector error: make sure key connector is available and working correctly."}, "premiumSubcriptionRequired": {"message": "Premium subscription required"}, "organizationIsDisabled": {"message": "Organization suspended."}, "disabledOrganizationFilterError": {"message": "Items in suspended Organizations cannot be accessed. Contact your Organization owner for assistance."}, "loggingInTo": {"message": "Logging in to $DOMAIN$", "placeholders": {"domain": {"content": "$1", "example": "example.com"}}}, "serverVersion": {"message": "Server version"}, "selfHostedServer": {"message": "self-hosted"}, "thirdParty": {"message": "Third-party"}, "thirdPartyServerMessage": {"message": "Connected to third-party server implementation, $SERVERNAME$. Please verify bugs using the official server, or report them to the third-party server.", "placeholders": {"servername": {"content": "$1", "example": "ThirdPartyServerName"}}}, "lastSeenOn": {"message": "last seen on: $DATE$", "placeholders": {"date": {"content": "$1", "example": "Jun 15, 2015"}}}, "loginWithMasterPassword": {"message": "Log in with master password"}, "newAroundHere": {"message": "New around here?"}, "rememberEmail": {"message": "Remember email"}, "loginWithDevice": {"message": "Log in with device"}, "fingerprintPhraseHeader": {"message": "Fingerprint phrase"}, "fingerprintMatchInfo": {"message": "Please make sure your vault is unlocked and the Fingerprint phrase matches on the other device."}, "resendNotification": {"message": "Resend notification"}, "viewAllLogInOptions": {"message": "View all log in options"}, "notificationSentDevice": {"message": "A notification has been sent to your device."}, "notificationSentDevicePart1": {"message": "Unlock Bitwarden on your device or on the"}, "notificationSentDeviceAnchor": {"message": "web app"}, "notificationSentDevicePart2": {"message": "Make sure the Fingerprint phrase matches the one below before approving."}, "aNotificationWasSentToYourDevice": {"message": "A notification was sent to your device"}, "youWillBeNotifiedOnceTheRequestIsApproved": {"message": "You will be notified once the request is approved"}, "needAnotherOptionV1": {"message": "Need another option?"}, "loginInitiated": {"message": "<PERSON><PERSON> initiated"}, "logInRequestSent": {"message": "Request sent"}, "exposedMasterPassword": {"message": "Exposed Master Password"}, "exposedMasterPasswordDesc": {"message": "Password found in a data breach. Use a unique password to protect your account. Are you sure you want to use an exposed password?"}, "weakAndExposedMasterPassword": {"message": "Weak and Exposed Master Password"}, "weakAndBreachedMasterPasswordDesc": {"message": "Weak password identified and found in a data breach. Use a strong and unique password to protect your account. Are you sure you want to use this password?"}, "checkForBreaches": {"message": "Check known data breaches for this password"}, "important": {"message": "Important:"}, "masterPasswordHint": {"message": "Your master password cannot be recovered if you forget it!"}, "characterMinimum": {"message": "$LENGTH$ character minimum", "placeholders": {"length": {"content": "$1", "example": "14"}}}, "autofillPageLoadPolicyActivated": {"message": "Your organization policies have turned on autofill on page load."}, "howToAutofill": {"message": "How to autofill"}, "autofillSelectInfoWithCommand": {"message": "Select an item from this screen, use the shortcut $COMMAND$, or explore other options in settings.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillSelectInfoWithoutCommand": {"message": "Select an item from this screen, or explore other options in settings."}, "gotIt": {"message": "Got it"}, "autofillSettings": {"message": "Autofill settings"}, "autofillKeyboardShortcutSectionTitle": {"message": "Autofill shortcut"}, "autofillKeyboardShortcutUpdateLabel": {"message": "Change shortcut"}, "autofillKeyboardManagerShortcutsLabel": {"message": "Manage shortcuts"}, "autofillShortcut": {"message": "Autofill keyboard shortcut"}, "autofillLoginShortcutNotSet": {"message": "The autofill login shortcut is not set. Change this in the browser's settings."}, "autofillLoginShortcutText": {"message": "The autofill login shortcut is $COMMAND$. Manage all shortcuts in the browser's settings.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "autofillShortcutTextSafari": {"message": "Default autofill shortcut: $COMMAND$.", "placeholders": {"command": {"content": "$1", "example": "CTRL+Shift+L"}}}, "opensInANewWindow": {"message": "Opens in a new window"}, "rememberThisDeviceToMakeFutureLoginsSeamless": {"message": "Remember this device to make future logins seamless"}, "deviceApprovalRequired": {"message": "Device approval required. Select an approval option below:"}, "deviceApprovalRequiredV2": {"message": "Device approval required"}, "selectAnApprovalOptionBelow": {"message": "Select an approval option below"}, "rememberThisDevice": {"message": "Remember this device"}, "uncheckIfPublicDevice": {"message": "Uncheck if using a public device"}, "approveFromYourOtherDevice": {"message": "Approve from your other device"}, "requestAdminApproval": {"message": "Request admin approval"}, "ssoIdentifierRequired": {"message": "Organization SSO identifier is required."}, "creatingAccountOn": {"message": "Creating account on"}, "checkYourEmail": {"message": "Check your email"}, "followTheLinkInTheEmailSentTo": {"message": "Follow the link in the email sent to"}, "andContinueCreatingYourAccount": {"message": "and continue creating your account."}, "noEmail": {"message": "No email?"}, "goBack": {"message": "Go back"}, "toEditYourEmailAddress": {"message": "to edit your email address."}, "eu": {"message": "EU", "description": "European Union"}, "accessDenied": {"message": "Access denied. You do not have permission to view this page."}, "general": {"message": "General"}, "display": {"message": "Display"}, "accountSuccessfullyCreated": {"message": "Account successfully created!"}, "adminApprovalRequested": {"message": "Admin approval requested"}, "adminApprovalRequestSentToAdmins": {"message": "Your request has been sent to your admin."}, "troubleLoggingIn": {"message": "Trouble logging in?"}, "loginApproved": {"message": "Login approved"}, "userEmailMissing": {"message": "User email missing"}, "activeUserEmailNotFoundLoggingYouOut": {"message": "Active user email not found. Logging you out."}, "deviceTrusted": {"message": "<PERSON><PERSON> trusted"}, "trustOrganization": {"message": "Trust organization"}, "trust": {"message": "Trust"}, "doNotTrust": {"message": "Do not trust"}, "organizationNotTrusted": {"message": "Organization is not trusted"}, "emergencyAccessTrustWarning": {"message": "For the security of your account, only confirm if you have granted emergency access to this user and their fingerprint matches what is displayed in their account"}, "orgTrustWarning": {"message": "For the security of your account, only proceed if you are a member of this organization, have account recovery enabled, and the fingerprint displayed below matches the organization's fingerprint."}, "orgTrustWarning1": {"message": "This organization has an Enterprise policy that will enroll you in account recovery. Enrollment will allow organization administrators to change your password. Only proceed if you recognize this organization and the fingerprint phrase displayed below matches the organization's fingerprint."}, "trustUser": {"message": "Trust user"}, "sendsTitleNoItems": {"message": "Send sensitive information safely", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "sendsBodyNoItems": {"message": "Share files and data securely with anyone, on any platform. Your information will remain end-to-end encrypted while limiting exposure.", "description": "'Send' is a noun and the name of a feature called 'Bitwarden Send'. It should not be translated."}, "inputRequired": {"message": "Input is required."}, "required": {"message": "required"}, "search": {"message": "Search"}, "inputMinLength": {"message": "Input must be at least $COUNT$ characters long.", "placeholders": {"count": {"content": "$1", "example": "8"}}}, "inputMaxLength": {"message": "Input must not exceed $COUNT$ characters in length.", "placeholders": {"count": {"content": "$1", "example": "20"}}}, "inputForbiddenCharacters": {"message": "The following characters are not allowed: $CHARACTERS$", "placeholders": {"characters": {"content": "$1", "example": "@, #, $, %"}}}, "inputMinValue": {"message": "Input value must be at least $MIN$.", "placeholders": {"min": {"content": "$1", "example": "8"}}}, "inputMaxValue": {"message": "Input value must not exceed $MAX$.", "placeholders": {"max": {"content": "$1", "example": "100"}}}, "multipleInputEmails": {"message": "1 or more emails are invalid"}, "inputTrimValidator": {"message": "Input must not contain only whitespace.", "description": "Notification to inform the user that a form's input can't contain only whitespace."}, "inputEmail": {"message": "Input is not an email address."}, "fieldsNeedAttention": {"message": "$COUNT$ field(s) above need your attention.", "placeholders": {"count": {"content": "$1", "example": "4"}}}, "singleFieldNeedsAttention": {"message": "1 field needs your attention."}, "multipleFieldsNeedAttention": {"message": "$COUNT$ fields need your attention.", "placeholders": {"count": {"content": "$1", "example": "2"}}}, "selectPlaceholder": {"message": "-- Select --"}, "multiSelectPlaceholder": {"message": "-- Type to filter --"}, "multiSelectLoading": {"message": "Retrieving options..."}, "multiSelectNotFound": {"message": "No items found"}, "multiSelectClearAll": {"message": "Clear all"}, "plusNMore": {"message": "+ $QUANTITY$ more", "placeholders": {"quantity": {"content": "$1", "example": "5"}}}, "submenu": {"message": "Submenu"}, "toggleCollapse": {"message": "Toggle collapse", "description": "Toggling an expand/collapse state."}, "aliasDomain": {"message": "Alias domain"}, "passwordRepromptDisabledAutofillOnPageLoad": {"message": "Items with master password re-prompt cannot be autofilled on page load. Autofill on page load turned off.", "description": "Toast message for describing that master password re-prompt cannot be autofilled on page load."}, "autofillOnPageLoadSetToDefault": {"message": "Autofill on page load set to use default setting.", "description": "Toast message for informing the user that autofill on page load has been set to the default setting."}, "turnOffMasterPasswordPromptToEditField": {"message": "Turn off master password re-prompt to edit this field", "description": "Message appearing below the autofill on load message when master password reprompt is set for a vault item."}, "toggleSideNavigation": {"message": "Toggle side navigation"}, "skipToContent": {"message": "Skip to content"}, "bitwardenOverlayButton": {"message": "Bitwarden autofill menu button", "description": "Page title for the iframe containing the overlay button"}, "toggleBitwardenVaultOverlay": {"message": "Toggle Bitwarden autofill menu", "description": "Screen reader and tool tip label for the overlay button"}, "bitwardenVault": {"message": "Bitwarden autofill menu", "description": "Page title in overlay"}, "unlockYourAccountToViewMatchingLogins": {"message": "Unlock your account to view matching logins", "description": "Text to display in overlay when the account is locked."}, "unlockYourAccountToViewAutofillSuggestions": {"message": "Unlock your account to view autofill suggestions", "description": "Text to display in overlay when the account is locked."}, "unlockAccount": {"message": "Unlock account", "description": "Button text to display in overlay when the account is locked."}, "unlockAccountAria": {"message": "Unlock your account, opens in a new window", "description": "Screen reader text (aria-label) for unlock account button in overlay"}, "totpCodeAria": {"message": "Time-based One-Time Password Verification Code", "description": "Aria label for the totp code displayed in the inline menu for autofill"}, "totpSecondsSpanAria": {"message": "Time remaining before current TOTP expires", "description": "Aria label for the totp seconds displayed in the inline menu for autofill"}, "fillCredentialsFor": {"message": "Fill credentials for", "description": "Screen reader text for when overlay item is in focused"}, "partialUsername": {"message": "Partial username", "description": "Screen reader text for when a login item is focused where a partial username is displayed. SR will announce this phrase before reading the text of the partial username"}, "noItemsToShow": {"message": "No items to show", "description": "Text to show in overlay if there are no matching items"}, "newItem": {"message": "New item", "description": "Button text to display in overlay when there are no matching items"}, "addNewVaultItem": {"message": "Add new vault item", "description": "Screen reader text (aria-label) for new item button in overlay"}, "newLogin": {"message": "New login", "description": "Button text to display within inline menu when there are no matching items on a login field"}, "addNewLoginItemAria": {"message": "Add new vault login item, opens in a new window", "description": "Screen reader text (aria-label) for new login button within inline menu"}, "newCard": {"message": "New card", "description": "Button text to display within inline menu when there are no matching items on a credit card field"}, "addNewCardItemAria": {"message": "Add new vault card item, opens in a new window", "description": "Screen reader text (aria-label) for new card button within inline menu"}, "newIdentity": {"message": "New identity", "description": "Button text to display within inline menu when there are no matching items on an identity field"}, "addNewIdentityItemAria": {"message": "Add new vault identity item, opens in a new window", "description": "Screen reader text (aria-label) for new identity button within inline menu"}, "bitwardenOverlayMenuAvailable": {"message": "Bitwarden autofill menu available. Press the down arrow key to select.", "description": "Screen reader text for announcing when the overlay opens on the page"}, "turnOn": {"message": "Turn on"}, "ignore": {"message": "Ignore"}, "importData": {"message": "Import data", "description": "Used for the header of the import dialog, the import button and within the file-password-prompt"}, "importError": {"message": "Import error"}, "importErrorDesc": {"message": "There was a problem with the data you tried to import. Please resolve the errors listed below in your source file and try again."}, "resolveTheErrorsBelowAndTryAgain": {"message": "Resolve the errors below and try again."}, "description": {"message": "Description"}, "importSuccess": {"message": "Data successfully imported"}, "importSuccessNumberOfItems": {"message": "A total of $AMOUNT$ items were imported.", "placeholders": {"amount": {"content": "$1", "example": "2"}}}, "tryAgain": {"message": "Try again"}, "verificationRequiredForActionSetPinToContinue": {"message": "Verification required for this action. Set a PIN to continue."}, "setPin": {"message": "Set PIN"}, "verifyWithBiometrics": {"message": "Verify with biometrics"}, "awaitingConfirmation": {"message": "Awaiting confirmation"}, "couldNotCompleteBiometrics": {"message": "Could not complete biometrics."}, "needADifferentMethod": {"message": "Need a different method?"}, "useMasterPassword": {"message": "Use master password"}, "usePin": {"message": "Use PIN"}, "useBiometrics": {"message": "Use biometrics"}, "enterVerificationCodeSentToEmail": {"message": "Enter the verification code that was sent to your email."}, "resendCode": {"message": "Resend code"}, "total": {"message": "Total"}, "importWarning": {"message": "You are importing data to $ORGANIZATION$. Your data may be shared with members of this organization. Do you want to proceed?", "placeholders": {"organization": {"content": "$1", "example": "My Org Name"}}}, "duoHealthCheckResultsInNullAuthUrlError": {"message": "Error connecting with the Duo service. Use a different two-step login method or contact Duo for assistance."}, "duoRequiredForAccount": {"message": "Duo two-step login is required for your account."}, "popoutExtension": {"message": "Popout extension"}, "launchDuo": {"message": "Launch Duo"}, "importFormatError": {"message": "Data is not formatted correctly. Please check your import file and try again."}, "importNothingError": {"message": "Nothing was imported."}, "importEncKeyError": {"message": "Error decrypting the exported file. Your encryption key does not match the encryption key used export the data."}, "invalidFilePassword": {"message": "Invalid file password, please use the password you entered when you created the export file."}, "destination": {"message": "Destination"}, "learnAboutImportOptions": {"message": "Learn about your import options"}, "selectImportFolder": {"message": "Select a folder"}, "selectImportCollection": {"message": "Select a collection"}, "importTargetHint": {"message": "Select this option if you want the imported file contents moved to a $DESTINATION$", "description": "Located as a hint under the import target. Will be appended by either folder or collection, depending if the user is importing into an individual or an organizational vault.", "placeholders": {"destination": {"content": "$1", "example": "folder or collection"}}}, "importUnassignedItemsError": {"message": "File contains unassigned items."}, "selectFormat": {"message": "Select the format of the import file"}, "selectImportFile": {"message": "Select the import file"}, "chooseFile": {"message": "Choose <PERSON>"}, "noFileChosen": {"message": "No file chosen"}, "orCopyPasteFileContents": {"message": "or copy/paste the import file contents"}, "instructionsFor": {"message": "$NAME$ Instructions", "description": "The title for the import tool instructions.", "placeholders": {"name": {"content": "$1", "example": "LastPass (csv)"}}}, "confirmVaultImport": {"message": "Confirm vault import"}, "confirmVaultImportDesc": {"message": "This file is password-protected. Please enter the file password to import data."}, "confirmFilePassword": {"message": "Confirm file password"}, "exportSuccess": {"message": "Vault data exported"}, "typePasskey": {"message": "Passkey"}, "accessing": {"message": "Accessing"}, "loggedInExclamation": {"message": "Logged in!"}, "passkeyNotCopied": {"message": "Passkey will not be copied"}, "passkeyNotCopiedAlert": {"message": "The passkey will not be copied to the cloned item. Do you want to continue cloning this item?"}, "passkeyFeatureIsNotImplementedForAccountsWithoutMasterPassword": {"message": "Verification required by the initiating site. This feature is not yet implemented for accounts without master password."}, "logInWithPasskeyQuestion": {"message": "Log in with passkey?"}, "passkeyAlreadyExists": {"message": "A passkey already exists for this application."}, "noPasskeysFoundForThisApplication": {"message": "No passkeys found for this application."}, "noMatchingPasskeyLogin": {"message": "You do not have a matching login for this site."}, "noMatchingLoginsForSite": {"message": "No matching logins for this site"}, "searchSavePasskeyNewLogin": {"message": "Search or save passkey as new login"}, "confirm": {"message": "Confirm"}, "savePasskey": {"message": "Save passkey"}, "savePasskeyNewLogin": {"message": "Save passkey as new login"}, "chooseCipherForPasskeySave": {"message": "Choose a login to save this passkey to"}, "chooseCipherForPasskeyAuth": {"message": "Choose a passkey to log in with"}, "passkeyItem": {"message": "<PERSON><PERSON> Item"}, "overwritePasskey": {"message": "Overwrite passkey?"}, "overwritePasskeyAlert": {"message": "This item already contains a passkey. Are you sure you want to overwrite the current passkey?"}, "featureNotSupported": {"message": "Feature not yet supported"}, "yourPasskeyIsLocked": {"message": "Authentication required to use passkey. Verify your identity to continue."}, "multifactorAuthenticationCancelled": {"message": "Multifactor authentication cancelled"}, "noLastPassDataFound": {"message": "No LastPass data found"}, "incorrectUsernameOrPassword": {"message": "Incorrect username or password"}, "incorrectPassword": {"message": "Incorrect password"}, "incorrectCode": {"message": "Incorrect code"}, "incorrectPin": {"message": "Incorrect PIN"}, "multifactorAuthenticationFailed": {"message": "Multifactor authentication failed"}, "includeSharedFolders": {"message": "Include shared folders"}, "lastPassEmail": {"message": "LastPass Email"}, "importingYourAccount": {"message": "Importing your account..."}, "lastPassMFARequired": {"message": "LastPass multifactor authentication required"}, "lastPassMFADesc": {"message": "Enter your one-time passcode from your authentication app"}, "lastPassOOBDesc": {"message": "Approve the login request in your authentication app or enter a one-time passcode."}, "passcode": {"message": "Passcode"}, "lastPassMasterPassword": {"message": "LastPass master password"}, "lastPassAuthRequired": {"message": "LastPass authentication required"}, "awaitingSSO": {"message": "Awaiting SSO authentication"}, "awaitingSSODesc": {"message": "Please continue to log in using your company credentials."}, "seeDetailedInstructions": {"message": "See detailed instructions on our help site at", "description": "This is followed a by a hyperlink to the help website."}, "importDirectlyFromLastPass": {"message": "Import directly from LastPass"}, "importFromCSV": {"message": "Import from CSV"}, "lastPassTryAgainCheckEmail": {"message": "Try again or look for an email from LastPass to verify it's you."}, "collection": {"message": "Collection"}, "lastPassYubikeyDesc": {"message": "Insert the YubiKey associated with your LastPass account into your computer's USB port, then touch its button."}, "switchAccount": {"message": "Switch account"}, "switchAccounts": {"message": "Switch accounts"}, "switchToAccount": {"message": "Switch to account"}, "activeAccount": {"message": "Active account"}, "bitwardenAccount": {"message": "Bitwarden account"}, "availableAccounts": {"message": "Available accounts"}, "accountLimitReached": {"message": "Account limit reached. Log out of an account to add another."}, "active": {"message": "active"}, "locked": {"message": "locked"}, "unlocked": {"message": "unlocked"}, "server": {"message": "server"}, "hostedAt": {"message": "hosted at"}, "useDeviceOrHardwareKey": {"message": "Use your device or hardware key"}, "justOnce": {"message": "Just once"}, "alwaysForThisSite": {"message": "Always for this site"}, "domainAddedToExcludedDomains": {"message": "$DOMAIN$ added to excluded domains.", "placeholders": {"domain": {"content": "$1", "example": "duckduckgo.com"}}}, "commonImportFormats": {"message": "Common formats", "description": "Label indicating the most common import formats"}, "confirmContinueToBrowserSettingsTitle": {"message": "Continue to browser settings?", "description": "Title for dialog which asks if the user wants to proceed to a relevant browser settings page"}, "confirmContinueToHelpCenter": {"message": "Continue to Help Center?", "description": "Title for dialog which asks if the user wants to proceed to a relevant Help Center page"}, "confirmContinueToHelpCenterPasswordManagementContent": {"message": "Change your browser's autofill and password management settings.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser password management settings"}, "confirmContinueToHelpCenterKeyboardShortcutsContent": {"message": "You can view and set extension shortcuts in your browser's settings.", "description": "Body content for dialog which asks if the user wants to proceed to the Help Center's page about browser keyboard shortcut settings"}, "confirmContinueToBrowserPasswordManagementSettingsContent": {"message": "Change your browser's autofill and password management settings.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's password management settings page"}, "confirmContinueToBrowserKeyboardShortcutSettingsContent": {"message": "You can view and set extension shortcuts in your browser's settings.", "description": "Body content for dialog which asks if the user wants to proceed to the browser's keyboard shortcut settings page"}, "overrideDefaultBrowserAutofillTitle": {"message": "Make <PERSON><PERSON><PERSON> your default password manager?", "description": "Dialog title facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutofillDescription": {"message": "Ignoring this option may cause conflicts between Bitwarden autofill suggestions and your browser's.", "description": "Dialog message facilitating the ability to override a chrome browser's default autofill behavior"}, "overrideDefaultBrowserAutoFillSettings": {"message": "Make Bitwarden your default password manager", "description": "Label for the setting that allows overriding the default browser autofill settings"}, "privacyPermissionAdditionNotGrantedTitle": {"message": "Unable to set <PERSON><PERSON><PERSON> as the default password manager", "description": "Title for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "privacyPermissionAdditionNotGrantedDescription": {"message": "You must grant browser privacy permissions to Bitwarden to set it as the default password manager.", "description": "Description for the dialog that appears when the user has not granted the extension permission to set privacy settings"}, "makeDefault": {"message": "Make default", "description": "Button text for the setting that allows overriding the default browser autofill settings"}, "saveCipherAttemptSuccess": {"message": "Credentials saved successfully!", "description": "Notification message for when saving credentials has succeeded."}, "passwordSaved": {"message": "Password saved!", "description": "Notification message for when saving credentials has succeeded."}, "updateCipherAttemptSuccess": {"message": "Credentials updated successfully!", "description": "Notification message for when updating credentials has succeeded."}, "passwordUpdated": {"message": "Password updated!", "description": "Notification message for when updating credentials has succeeded."}, "saveCipherAttemptFailed": {"message": "Error saving credentials. Check console for details.", "description": "Notification message for when saving credentials has failed."}, "success": {"message": "Success"}, "removePasskey": {"message": "Remove passkey"}, "passkeyRemoved": {"message": "Passkey removed"}, "autofillSuggestions": {"message": "Autofill suggestions"}, "itemSuggestions": {"message": "Suggested items"}, "autofillSuggestionsTip": {"message": "Save a login item for this site to autofill"}, "yourVaultIsEmpty": {"message": "Your vault is empty"}, "noItemsMatchSearch": {"message": "No items match your search"}, "clearFiltersOrTryAnother": {"message": "Clear filters or try another search term"}, "copyInfoTitle": {"message": "Copy info - $ITEMNAME$", "description": "Title for a button that opens a menu with options to copy information from an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "copyNoteTitle": {"message": "Copy Note - $ITEMNAME$", "description": "Title for a button copies a note to the clipboard.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Note Item"}}}, "moreOptionsLabel": {"message": "More options, $ITEMNAME$", "description": "Aria label for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "moreOptionsTitle": {"message": "More options - $ITEMNAME$", "description": "Title for a button that opens a menu with more options for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitle": {"message": "View item - $ITEMNAME$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "viewItemTitleWithField": {"message": "View item - $ITEMNAME$ - $FIELD$", "description": "Title for a link that opens a view for an item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "autofillTitle": {"message": "Autofill - $ITEMNAME$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}}}, "autofillTitleWithField": {"message": "Autofill - $ITEMNAME$ - $FIELD$", "description": "Title for a button that autofills a login item.", "placeholders": {"itemname": {"content": "$1", "example": "Secret Item"}, "field": {"content": "$2", "example": "Username"}}}, "copyFieldValue": {"message": "Copy $FIELD$, $VALUE$", "description": "Title for a button that copies a field value to the clipboard.", "placeholders": {"field": {"content": "$1", "example": "Username"}, "value": {"content": "$2", "example": "Foo"}}}, "noValuesToCopy": {"message": "No values to copy"}, "assignToCollections": {"message": "Assign to collections"}, "copyEmail": {"message": "Copy email"}, "copyPhone": {"message": "Copy phone"}, "copyAddress": {"message": "Copy address"}, "adminConsole": {"message": "<PERSON><PERSON>"}, "accountSecurity": {"message": "Account security"}, "notifications": {"message": "Notifications"}, "appearance": {"message": "Appearance"}, "errorAssigningTargetCollection": {"message": "Error assigning target collection."}, "errorAssigningTargetFolder": {"message": "Error assigning target folder."}, "viewItemsIn": {"message": "View items in $NAME$", "description": "Button to view the contents of a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "backTo": {"message": "Back to $NAME$", "description": "Navigate back to a previous folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "new": {"message": "New"}, "removeItem": {"message": "Remove $NAME$", "description": "Remove a selected option, such as a folder or collection", "placeholders": {"name": {"content": "$1", "example": "Work"}}}, "itemsWithNoFolder": {"message": "Items with no folder"}, "itemDetails": {"message": "Item details"}, "itemName": {"message": "Item name"}, "organizationIsDeactivated": {"message": "Organization is deactivated"}, "owner": {"message": "Owner"}, "selfOwnershipLabel": {"message": "You", "description": "Used as a label to indicate that the user is the owner of an item."}, "contactYourOrgAdmin": {"message": "Items in deactivated organizations cannot be accessed. Contact your organization owner for assistance."}, "additionalInformation": {"message": "Additional information"}, "itemHistory": {"message": "Item history"}, "lastEdited": {"message": "Last edited"}, "ownerYou": {"message": "Owner: You"}, "linked": {"message": "Linked"}, "copySuccessful": {"message": "Copy Successful"}, "upload": {"message": "Upload"}, "addAttachment": {"message": "Add attachment"}, "maxFileSizeSansPunctuation": {"message": "Maximum file size is 500 MB"}, "deleteAttachmentName": {"message": "Delete attachment $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadAttachmentName": {"message": "Download $NAME$", "placeholders": {"name": {"content": "$1", "example": "Attachment Name"}}}, "downloadBitwarden": {"message": "Download Bitwarden"}, "downloadBitwardenOnAllDevices": {"message": "Download Bitwarden on all devices"}, "getTheMobileApp": {"message": "Get the mobile app"}, "getTheMobileAppDesc": {"message": "Access your passwords on the go with the Bitwarden mobile app."}, "getTheDesktopApp": {"message": "Get the desktop app"}, "getTheDesktopAppDesc": {"message": "Access your vault without a browser, then set up unlock with biometrics to expedite unlocking in both the desktop app and browser extension."}, "downloadFromBitwardenNow": {"message": "Download from bitwarden.com now"}, "getItOnGooglePlay": {"message": "Get it on Google Play"}, "downloadOnTheAppStore": {"message": "Download on the App Store"}, "permanentlyDeleteAttachmentConfirmation": {"message": "Are you sure you want to permanently delete this attachment?"}, "premium": {"message": "Premium"}, "freeOrgsCannotUseAttachments": {"message": "Free organizations cannot use attachments"}, "filters": {"message": "Filters"}, "filterVault": {"message": "Filter vault"}, "filterApplied": {"message": "One filter applied"}, "filterAppliedPlural": {"message": "$COUNT$ filters applied", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "personalDetails": {"message": "Personal details"}, "identification": {"message": "Identification"}, "contactInfo": {"message": "Contact info"}, "downloadAttachment": {"message": "Download - $ITEMNAME$", "placeholders": {"itemname": {"content": "$1", "example": "Your File"}}}, "cardNumberEndsWith": {"message": "card number ends with", "description": "Used within the inline menu to provide an aria description when users are attempting to fill a card cipher."}, "loginCredentials": {"message": "Login credentials"}, "authenticatorKey": {"message": "Authenticator key"}, "autofillOptions": {"message": "Autofill options"}, "websiteUri": {"message": "Website (URI)"}, "websiteUriCount": {"message": "Website (URI) $COUNT$", "description": "Label for an input field that contains a website URI. The input field is part of a list of fields, and the count indicates the position of the field in the list.", "placeholders": {"count": {"content": "$1", "example": "3"}}}, "websiteAdded": {"message": "Website added"}, "addWebsite": {"message": "Add website"}, "deleteWebsite": {"message": "Delete website"}, "defaultLabel": {"message": "Default ($VALUE$)", "description": "A label that indicates the default value for a field with the current default value in parentheses.", "placeholders": {"value": {"content": "$1", "example": "Base domain"}}}, "showMatchDetection": {"message": "Show match detection $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "hideMatchDetection": {"message": "Hide match detection $WEBSITE$", "placeholders": {"website": {"content": "$1", "example": "https://example.com"}}}, "autoFillOnPageLoad": {"message": "Autofill on page load?"}, "cardExpiredTitle": {"message": "Expired card"}, "cardExpiredMessage": {"message": "If you've renewed it, update the card's information"}, "cardDetails": {"message": "Card details"}, "cardBrandDetails": {"message": "$BRAND$ details", "placeholders": {"brand": {"content": "$1", "example": "Visa"}}}, "enableAnimations": {"message": "Enable animations"}, "showAnimations": {"message": "Show animations"}, "addAccount": {"message": "Add account"}, "loading": {"message": "Loading"}, "data": {"message": "Data"}, "passkeys": {"message": "Passkeys", "description": "A section header for a list of passkeys."}, "passwords": {"message": "Passwords", "description": "A section header for a list of passwords."}, "logInWithPasskeyAriaLabel": {"message": "Log in with passkey", "description": "ARIA label for the inline menu button that logs in with a passkey."}, "assign": {"message": "Assign"}, "bulkCollectionAssignmentDialogDescriptionSingular": {"message": "Only organization members with access to these collections will be able to see the item."}, "bulkCollectionAssignmentDialogDescriptionPlural": {"message": "Only organization members with access to these collections will be able to see the items."}, "bulkCollectionAssignmentWarning": {"message": "You have selected $TOTAL_COUNT$ items. You cannot update $READONLY_COUNT$ of the items because you do not have edit permissions.", "placeholders": {"total_count": {"content": "$1", "example": "10"}, "readonly_count": {"content": "$2"}}}, "addField": {"message": "Add field"}, "add": {"message": "Add"}, "fieldType": {"message": "Field type"}, "fieldLabel": {"message": "Field label"}, "textHelpText": {"message": "Use text fields for data like security questions"}, "hiddenHelpText": {"message": "Use hidden fields for sensitive data like a password"}, "checkBoxHelpText": {"message": "Use checkboxes if you'd like to autofill a form's checkbox, like a remember email"}, "linkedHelpText": {"message": "Use a linked field when you are experiencing autofill issues for a specific website."}, "linkedLabelHelpText": {"message": "Enter the the field's html id, name, aria-label, or placeholder."}, "editField": {"message": "Edit field"}, "editFieldLabel": {"message": "Edit $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "deleteCustomField": {"message": "Delete $LABEL$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "fieldAdded": {"message": "$LABEL$ added", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderToggleButton": {"message": "Reorder $LABEL$. Use arrow key to move item up or down.", "placeholders": {"label": {"content": "$1", "example": "Custom field"}}}, "reorderWebsiteUriButton": {"message": "Reorder website URI. Use arrow key to move item up or down."}, "reorderFieldUp": {"message": "$LABEL$ moved up, position $INDEX$ of $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "selectCollectionsToAssign": {"message": "Select collections to assign"}, "personalItemTransferWarningSingular": {"message": "1 item will be permanently transferred to the selected organization. You will no longer own this item."}, "personalItemsTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ items will be permanently transferred to the selected organization. You will no longer own these items.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}}}, "personalItemWithOrgTransferWarningSingular": {"message": "1 item will be permanently transferred to $ORG$. You will no longer own this item.", "placeholders": {"org": {"content": "$1", "example": "Organization name"}}}, "personalItemsWithOrgTransferWarningPlural": {"message": "$PERSONAL_ITEMS_COUNT$ items will be permanently transferred to $ORG$. You will no longer own these items.", "placeholders": {"personal_items_count": {"content": "$1", "example": "2"}, "org": {"content": "$2", "example": "Organization name"}}}, "successfullyAssignedCollections": {"message": "Successfully assigned collections"}, "nothingSelected": {"message": "You have not selected anything."}, "itemsMovedToOrg": {"message": "Items moved to $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "itemMovedToOrg": {"message": "Item moved to $ORGNAME$", "placeholders": {"orgname": {"content": "$1", "example": "Company Name"}}}, "reorderFieldDown": {"message": "$LABEL$ moved down, position $INDEX$ of $LENGTH$", "placeholders": {"label": {"content": "$1", "example": "Custom field"}, "index": {"content": "$2", "example": "1"}, "length": {"content": "$3", "example": "3"}}}, "itemLocation": {"message": "Item Location"}, "fileSend": {"message": "File Send"}, "fileSends": {"message": "File Sends"}, "textSend": {"message": "Text Send"}, "textSends": {"message": "Text Sends"}, "accountActions": {"message": "Account actions"}, "showNumberOfAutofillSuggestions": {"message": "Show number of login autofill suggestions on extension icon"}, "showQuickCopyActions": {"message": "Show quick copy actions on Vault"}, "systemDefault": {"message": "System default"}, "enterprisePolicyRequirementsApplied": {"message": "Enterprise policy requirements have been applied to this setting"}, "sshPrivateKey": {"message": "Private key"}, "sshPublicKey": {"message": "Public key"}, "sshFingerprint": {"message": "Fingerprint"}, "sshKeyAlgorithm": {"message": "Key type"}, "sshKeyAlgorithmED25519": {"message": "ED25519"}, "sshKeyAlgorithmRSA2048": {"message": "RSA 2048-Bit"}, "sshKeyAlgorithmRSA3072": {"message": "RSA 3072-Bit"}, "sshKeyAlgorithmRSA4096": {"message": "RSA 4096-Bit"}, "retry": {"message": "Retry"}, "vaultCustomTimeoutMinimum": {"message": "Minimum custom timeout is 1 minute."}, "additionalContentAvailable": {"message": "Additional content is available"}, "fileSavedToDevice": {"message": "File saved to device. Manage from your device downloads."}, "showCharacterCount": {"message": "Show character count"}, "hideCharacterCount": {"message": "Hide character count"}, "itemsInTrash": {"message": "Items in trash"}, "noItemsInTrash": {"message": "No items in trash"}, "noItemsInTrashDesc": {"message": "Items you delete will appear here and be permanently deleted after 30 days"}, "trashWarning": {"message": "Items that have been in trash more than 30 days will automatically be deleted"}, "restore": {"message": "Rest<PERSON>"}, "deleteForever": {"message": "Delete forever"}, "noEditPermissions": {"message": "You don't have permission to edit this item"}, "biometricsStatusHelptextUnlockNeeded": {"message": "Biometric unlock is unavailable because PIN or password unlock is required first."}, "biometricsStatusHelptextHardwareUnavailable": {"message": "Biometric unlock is currently unavailable."}, "biometricsStatusHelptextAutoSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextManualSetupNeeded": {"message": "Biometric unlock is unavailable due to misconfigured system files."}, "biometricsStatusHelptextDesktopDisconnected": {"message": "Biometric unlock is unavailable because the Bitwarden desktop app is closed."}, "biometricsStatusHelptextNotEnabledInDesktop": {"message": "Biometric unlock is unavailable because it is not enabled for $EMAIL$ in the Bitwarden desktop app.", "placeholders": {"email": {"content": "$1", "example": "<EMAIL>"}}}, "biometricsStatusHelptextUnavailableReasonUnknown": {"message": "Biometric unlock is currently unavailable for an unknown reason."}, "unlockVault": {"message": "Unlock your vault in seconds"}, "unlockVaultDesc": {"message": "You can customize your unlock and timeout settings to more quickly access your vault."}, "unlockPinSet": {"message": "Unlock PIN set"}, "authenticating": {"message": "Authenticating"}, "fillGeneratedPassword": {"message": "Fill generated password", "description": "Heading for the password generator within the inline menu"}, "passwordRegenerated": {"message": "Password regenerated", "description": "Notification message for when a password has been regenerated"}, "saveToBitwarden": {"message": "Save to Bitwarden", "description": "Confirmation message for saving a login to Bitwarden"}, "spaceCharacterDescriptor": {"message": "Space", "description": "Represents the space key in screen reader content as a readable word"}, "tildeCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ~ key in screen reader content as a readable word"}, "backtickCharacterDescriptor": {"message": "Backtick", "description": "Represents the ` key in screen reader content as a readable word"}, "exclamationCharacterDescriptor": {"message": "Exclamation mark", "description": "Represents the ! key in screen reader content as a readable word"}, "atSignCharacterDescriptor": {"message": "At sign", "description": "Represents the @ key in screen reader content as a readable word"}, "hashSignCharacterDescriptor": {"message": "Hash sign", "description": "Represents the # key in screen reader content as a readable word"}, "dollarSignCharacterDescriptor": {"message": "Dollar sign", "description": "Represents the $ key in screen reader content as a readable word"}, "percentSignCharacterDescriptor": {"message": "Percent sign", "description": "Represents the % key in screen reader content as a readable word"}, "caretCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the ^ key in screen reader content as a readable word"}, "ampersandCharacterDescriptor": {"message": "Ampersand", "description": "Represents the & key in screen reader content as a readable word"}, "asteriskCharacterDescriptor": {"message": "Asterisk", "description": "Represents the * key in screen reader content as a readable word"}, "parenLeftCharacterDescriptor": {"message": "Left parenthesis", "description": "Represents the ( key in screen reader content as a readable word"}, "parenRightCharacterDescriptor": {"message": "Right parenthesis", "description": "Represents the ) key in screen reader content as a readable word"}, "hyphenCharacterDescriptor": {"message": "Underscore", "description": "Represents the _ key in screen reader content as a readable word"}, "underscoreCharacterDescriptor": {"message": "Hyphen", "description": "Represents the - key in screen reader content as a readable word"}, "plusCharacterDescriptor": {"message": "Plus", "description": "Represents the + key in screen reader content as a readable word"}, "equalsCharacterDescriptor": {"message": "Equals", "description": "Represents the = key in screen reader content as a readable word"}, "braceLeftCharacterDescriptor": {"message": "Left brace", "description": "Represents the { key in screen reader content as a readable word"}, "braceRightCharacterDescriptor": {"message": "Right brace", "description": "Represents the } key in screen reader content as a readable word"}, "bracketLeftCharacterDescriptor": {"message": "Left bracket", "description": "Represents the [ key in screen reader content as a readable word"}, "bracketRightCharacterDescriptor": {"message": "Right bracket", "description": "Represents the ] key in screen reader content as a readable word"}, "pipeCharacterDescriptor": {"message": "<PERSON><PERSON>", "description": "Represents the | key in screen reader content as a readable word"}, "backSlashCharacterDescriptor": {"message": "Back slash", "description": "Represents the back slash key in screen reader content as a readable word"}, "colonCharacterDescriptor": {"message": "Colon", "description": "Represents the : key in screen reader content as a readable word"}, "semicolonCharacterDescriptor": {"message": "Semicolon", "description": "Represents the ; key in screen reader content as a readable word"}, "doubleQuoteCharacterDescriptor": {"message": "Double quote", "description": "Represents the double quote key in screen reader content as a readable word"}, "singleQuoteCharacterDescriptor": {"message": "Single quote", "description": "Represents the ' key in screen reader content as a readable word"}, "lessThanCharacterDescriptor": {"message": "Less than", "description": "Represents the < key in screen reader content as a readable word"}, "greaterThanCharacterDescriptor": {"message": "Greater than", "description": "Represents the > key in screen reader content as a readable word"}, "commaCharacterDescriptor": {"message": "Comma", "description": "Represents the , key in screen reader content as a readable word"}, "periodCharacterDescriptor": {"message": "Period", "description": "Represents the . key in screen reader content as a readable word"}, "questionCharacterDescriptor": {"message": "Question mark", "description": "Represents the ? key in screen reader content as a readable word"}, "forwardSlashCharacterDescriptor": {"message": "Forward slash", "description": "Represents the / key in screen reader content as a readable word"}, "lowercaseAriaLabel": {"message": "Lowercase"}, "uppercaseAriaLabel": {"message": "Uppercase"}, "generatedPassword": {"message": "Generated password"}, "compactMode": {"message": "Compact mode"}, "beta": {"message": "Beta"}, "extensionWidth": {"message": "Extension width"}, "wide": {"message": "Wide"}, "extraWide": {"message": "Extra wide"}, "sshKeyWrongPassword": {"message": "The password you entered is incorrect."}, "importSshKey": {"message": "Import"}, "confirmSshKeyPassword": {"message": "Confirm password"}, "enterSshKeyPasswordDesc": {"message": "Enter the password for the SSH key."}, "enterSshKeyPassword": {"message": "Enter password"}, "invalidSshKey": {"message": "The SSH key is invalid"}, "sshKeyTypeUnsupported": {"message": "The SSH key type is not supported"}, "importSshKeyFromClipboard": {"message": "Import key from clipboard"}, "sshKeyImported": {"message": "SSH key imported successfully"}, "cannotRemoveViewOnlyCollections": {"message": "You cannot remove collections with View only permissions: $COLLECTIONS$", "placeholders": {"collections": {"content": "$1", "example": "Work, Personal"}}}, "updateDesktopAppOrDisableFingerprintDialogTitle": {"message": "Please update your desktop application"}, "updateDesktopAppOrDisableFingerprintDialogMessage": {"message": "To use biometric unlock, please update your desktop application, or disable fingerprint unlock in the desktop settings."}, "changeAtRiskPassword": {"message": "Change at-risk password"}, "settingsVaultOptions": {"message": "Vault options"}, "emptyVaultDescription": {"message": "The vault protects more than just your passwords. Store secure logins, IDs, cards and notes securely here."}, "introCarouselLabel": {"message": "Welcome to Bitwarden"}, "securityPrioritized": {"message": "Security, prioritized"}, "securityPrioritizedBody": {"message": "Save logins, cards, and identities to your secure vault. Bitwarden uses zero-knowledge, end-to-end encryption to protect what’s important to you."}, "quickLogin": {"message": "Quick and easy login"}, "quickLoginBody": {"message": "Set up biometric unlock and autofill to log into your accounts without typing a single letter."}, "secureUser": {"message": "Level up your logins"}, "secureUserBody": {"message": "Use the generator to create and save strong, unique passwords for all your accounts."}, "secureDevices": {"message": "Your data, when and where you need it"}, "secureDevicesBody": {"message": "Save unlimited passwords across unlimited devices with Bitwarden mobile, browser, and desktop apps."}, "nudgeBadgeAria": {"message": "1 notification"}, "emptyVaultNudgeTitle": {"message": "Import existing passwords"}, "emptyVaultNudgeBody": {"message": "Use the importer to quickly transfer logins to Bitwarden without manually adding them."}, "emptyVaultNudgeButton": {"message": "Import now"}, "hasItemsVaultNudgeTitle": {"message": "Welcome to your vault!"}, "hasItemsVaultNudgeBodyOne": {"message": "Autofill items for the current page"}, "hasItemsVaultNudgeBodyTwo": {"message": "Favorite items for easy access"}, "hasItemsVaultNudgeBodyThree": {"message": "Search your vault for something else"}, "newLoginNudgeTitle": {"message": "Save time with autofill"}, "newLoginNudgeBodyOne": {"message": "Include a", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyBold": {"message": "Website", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newLoginNudgeBodyTwo": {"message": "so this login appears as an autofill suggestion.", "description": "This is in multiple parts to allow for bold text in the middle of the sentence.", "example": "Include a Website so this login appears as an autofill suggestion."}, "newCardNudgeTitle": {"message": "Seamless online checkout"}, "newCardNudgeBody": {"message": "With cards, easily autofill payment forms securely and accurately."}, "newIdentityNudgeTitle": {"message": "Simplify creating accounts"}, "newIdentityNudgeBody": {"message": "With identities, quickly autofill long registration or contact forms."}, "newNoteNudgeTitle": {"message": "Keep your sensitive data safe"}, "newNoteNudgeBody": {"message": "With notes, securely store sensitive data like banking or insurance details."}, "newSshNudgeTitle": {"message": "Developer-friendly SSH access"}, "newSshNudgeBodyOne": {"message": "Store your keys and connect with the SSH agent for fast, encrypted authentication.", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "newSshNudgeBodyTwo": {"message": "Learn more about SSH agent", "description": "Two part message", "example": "Store your keys and connect with the SSH agent for fast, encrypted authentication. Learn more about SSH agent"}, "generatorNudgeTitle": {"message": "Quickly create passwords"}, "generatorNudgeBodyOne": {"message": "Easily create strong and unique passwords by clicking on", "description": "Two part message", "example": "Easily create strong and unique passwords by clicking on {icon} to help you keep your logins secure."}, "generatorNudgeBodyTwo": {"message": "to help you keep your logins secure.", "description": "Two part message", "example": "Easily create strong and unique passwords by clicking on {icon} to help you keep your logins secure."}, "generatorNudgeBodyAria": {"message": "Easily create strong and unique passwords by clicking on the Generate password button to help you keep your logins secure.", "description": "Aria label for the body content of the generator nudge"}, "noPermissionsViewPage": {"message": "You do not have permissions to view this page. Try logging in with a different account."}}