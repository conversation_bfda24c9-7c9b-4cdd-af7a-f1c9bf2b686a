!function(){"use strict";var e;!function(e){e[e.Debug=0]="Debug",e[e.Info=1]="Info",e[e.Warning=2]="Warning",e[e.Error=3]="Error"}(e||(e={}));class t{constructor(e,t=null){this.isDev=e,this.filter=t,this.timersMap=new Map}debug(t,...r){this.isDev&&this.write(e.Debug,t,...r)}info(t,...r){this.write(e.Info,t,...r)}warning(t,...r){this.write(e.Warning,t,...r)}error(t,...r){this.write(e.Error,t,...r)}write(t,r,...i){if(null==this.filter||!this.filter(t))switch(t){case e.Debug:case e.Info:console.log(r,...i);break;case e.Warning:console.warn(r,...i);break;case e.Error:console.error(r,...i)}}}var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},r(e,t)};function i(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}Object.create;function n(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],i=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&i>=e.length&&(e=void 0),{value:e&&e[i++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function o(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var i,n,o=r.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(i=o.next()).done;)s.push(i.value)}catch(e){n={error:e}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}return s}function s(e,t,r){if(r||2===arguments.length)for(var i,n=0,o=t.length;n<o;n++)!i&&n in t||(i||(i=Array.prototype.slice.call(t,0,n)),i[n]=t[n]);return e.concat(i||Array.prototype.slice.call(t))}Object.create;"function"==typeof SuppressedError&&SuppressedError;function a(e){return"function"==typeof e}var d,c=((d=function(e){return function(t){e(this),this.message=t?t.length+" errors occurred during unsubscription:\n"+t.map((function(e,t){return t+1+") "+e.toString()})).join("\n  "):"",this.name="UnsubscriptionError",this.errors=t}}((function(e){Error.call(e),e.stack=(new Error).stack}))).prototype=Object.create(Error.prototype),d.prototype.constructor=d,d);function l(e,t){if(e){var r=e.indexOf(t);0<=r&&e.splice(r,1)}}var u=function(){function e(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._finalizers=null}var t;return e.prototype.unsubscribe=function(){var e,t,r,i,d;if(!this.closed){this.closed=!0;var l=this._parentage;if(l)if(this._parentage=null,Array.isArray(l))try{for(var u=n(l),p=u.next();!p.done;p=u.next()){p.value.remove(this)}}catch(t){e={error:t}}finally{try{p&&!p.done&&(t=u.return)&&t.call(u)}finally{if(e)throw e.error}}else l.remove(this);var h=this.initialTeardown;if(a(h))try{h()}catch(e){d=e instanceof c?e.errors:[e]}var g=this._finalizers;if(g){this._finalizers=null;try{for(var v=n(g),m=v.next();!m.done;m=v.next()){var y=m.value;try{f(y)}catch(e){d=null!=d?d:[],e instanceof c?d=s(s([],o(d)),o(e.errors)):d.push(e)}}}catch(e){r={error:e}}finally{try{m&&!m.done&&(i=v.return)&&i.call(v)}finally{if(r)throw r.error}}}if(d)throw new c(d)}},e.prototype.add=function(t){var r;if(t&&t!==this)if(this.closed)f(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=null!==(r=this._finalizers)&&void 0!==r?r:[]).push(t)}},e.prototype._hasParent=function(e){var t=this._parentage;return t===e||Array.isArray(t)&&t.includes(e)},e.prototype._addParent=function(e){var t=this._parentage;this._parentage=Array.isArray(t)?(t.push(e),t):t?[t,e]:e},e.prototype._removeParent=function(e){var t=this._parentage;t===e?this._parentage=null:Array.isArray(t)&&l(t,e)},e.prototype.remove=function(t){var r=this._finalizers;r&&l(r,t),t instanceof e&&t._removeParent(this)},e.EMPTY=((t=new e).closed=!0,t),e}();u.EMPTY;function p(e){return e instanceof u||e&&"closed"in e&&a(e.remove)&&a(e.add)&&a(e.unsubscribe)}function f(e){a(e)?e():e.unsubscribe()}var h={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},g={setTimeout:function(e,t){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];var n=g.delegate;return(null==n?void 0:n.setTimeout)?n.setTimeout.apply(n,s([e,t],o(r))):setTimeout.apply(void 0,s([e,t],o(r)))},clearTimeout:function(e){var t=g.delegate;return((null==t?void 0:t.clearTimeout)||clearTimeout)(e)},delegate:void 0};function v(){}var m=y("C",void 0,void 0);function y(e,t,r){return{kind:e,value:t,error:r}}var b=null;var C=function(e){function t(t){var r=e.call(this)||this;return r.isStopped=!1,t?(r.destination=t,p(t)&&t.add(r)):r.destination=E,r}return i(t,e),t.create=function(e,t,r){return new x(e,t,r)},t.prototype.next=function(e){this.isStopped?A(function(e){return y("N",e,void 0)}(e),this):this._next(e)},t.prototype.error=function(e){this.isStopped?A(y("E",void 0,e),this):(this.isStopped=!0,this._error(e))},t.prototype.complete=function(){this.isStopped?A(m,this):(this.isStopped=!0,this._complete())},t.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this),this.destination=null)},t.prototype._next=function(e){this.destination.next(e)},t.prototype._error=function(e){try{this.destination.error(e)}finally{this.unsubscribe()}},t.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},t}(u),w=Function.prototype.bind;function S(e,t){return w.call(e,t)}var _=function(){function e(e){this.partialObserver=e}return e.prototype.next=function(e){var t=this.partialObserver;if(t.next)try{t.next(e)}catch(e){O(e)}},e.prototype.error=function(e){var t=this.partialObserver;if(t.error)try{t.error(e)}catch(e){O(e)}else O(e)},e.prototype.complete=function(){var e=this.partialObserver;if(e.complete)try{e.complete()}catch(e){O(e)}},e}(),x=function(e){function t(t,r,i){var n,o,s=e.call(this)||this;a(t)||!t?n={next:null!=t?t:void 0,error:null!=r?r:void 0,complete:null!=i?i:void 0}:s&&h.useDeprecatedNextContext?((o=Object.create(t)).unsubscribe=function(){return s.unsubscribe()},n={next:t.next&&S(t.next,o),error:t.error&&S(t.error,o),complete:t.complete&&S(t.complete,o)}):n=t;return s.destination=new _(n),s}return i(t,e),t}(C);function O(e){var t;h.useDeprecatedSynchronousErrorHandling?(t=e,h.useDeprecatedSynchronousErrorHandling&&b&&(b.errorThrown=!0,b.error=t)):function(e){g.setTimeout((function(){var t=h.onUnhandledError;if(!t)throw e;t(e)}))}(e)}function A(e,t){var r=h.onStoppedNotification;r&&g.setTimeout((function(){return r(e,t)}))}var E={closed:!0,next:v,error:function(e){throw e},complete:v},P="function"==typeof Symbol&&Symbol.observable||"@@observable";function U(e){return e}function L(e){return 0===e.length?U:1===e.length?e[0]:function(t){return e.reduce((function(e,t){return t(e)}),t)}}var R=function(){function e(e){e&&(this._subscribe=e)}return e.prototype.lift=function(t){var r=new e;return r.source=this,r.operator=t,r},e.prototype.subscribe=function(e,t,r){var i,n=this,o=(i=e)&&i instanceof C||function(e){return e&&a(e.next)&&a(e.error)&&a(e.complete)}(i)&&p(i)?e:new x(e,t,r);return function(e){if(h.useDeprecatedSynchronousErrorHandling){var t=!b;if(t&&(b={errorThrown:!1,error:null}),e(),t){var r=b,i=r.errorThrown,n=r.error;if(b=null,i)throw n}}else e()}((function(){var e=n,t=e.operator,r=e.source;o.add(t?t.call(o,r):r?n._subscribe(o):n._trySubscribe(o))})),o},e.prototype._trySubscribe=function(e){try{return this._subscribe(e)}catch(t){e.error(t)}},e.prototype.forEach=function(e,t){var r=this;return new(t=D(t))((function(t,i){var n=new x({next:function(t){try{e(t)}catch(e){i(e),n.unsubscribe()}},error:i,complete:t});r.subscribe(n)}))},e.prototype._subscribe=function(e){var t;return null===(t=this.source)||void 0===t?void 0:t.subscribe(e)},e.prototype[P]=function(){return this},e.prototype.pipe=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return L(e)(this)},e.prototype.toPromise=function(e){var t=this;return new(e=D(e))((function(e,r){var i;t.subscribe((function(e){return i=e}),(function(e){return r(e)}),(function(){return e(i)}))}))},e.create=function(t){return new e(t)},e}();function D(e){var t;return null!==(t=null!=e?e:h.Promise)&&void 0!==t?t:Promise}const T="\\"+["/","-","."," "].join("\\").replace(" ","s"),I=(new RegExp(`[${T}]`,"g"),new RegExp(`[^\\d${T}]`,"g"),new RegExp("^(([1]{1}[0-2]{1})|(0?[1-9]{1}))$"),new RegExp("^2[0-1]{1}\\d{2}$"),"Chrome"),F="Opera",M="Edge",z="Vivaldi",V="Unknown",k="autofill_login";var B,N;!function(e){e.Web="web",e.Browser="browser",e.Desktop="desktop",e.Cli="cli"}(B||(B={})),function(e){e[e.Android=0]="Android",e[e.iOS=1]="iOS",e[e.ChromeExtension=2]="ChromeExtension",e[e.FirefoxExtension=3]="FirefoxExtension",e[e.OperaExtension=4]="OperaExtension",e[e.EdgeExtension=5]="EdgeExtension",e[e.WindowsDesktop=6]="WindowsDesktop",e[e.MacOsDesktop=7]="MacOsDesktop",e[e.LinuxDesktop=8]="LinuxDesktop",e[e.ChromeBrowser=9]="ChromeBrowser",e[e.FirefoxBrowser=10]="FirefoxBrowser",e[e.OperaBrowser=11]="OperaBrowser",e[e.EdgeBrowser=12]="EdgeBrowser",e[e.IEBrowser=13]="IEBrowser",e[e.UnknownBrowser=14]="UnknownBrowser",e[e.AndroidAmazon=15]="AndroidAmazon",e[e.UWP=16]="UWP",e[e.SafariBrowser=17]="SafariBrowser",e[e.VivaldiBrowser=18]="VivaldiBrowser",e[e.VivaldiExtension=19]="VivaldiExtension",e[e.SafariExtension=20]="SafariExtension",e[e.SDK=21]="SDK",e[e.Server=22]="Server",e[e.WindowsCLI=23]="WindowsCLI",e[e.MacOsCLI=24]="MacOsCLI",e[e.LinuxCLI=25]="LinuxCLI"}(N||(N={}));N.Android,N.iOS,N.AndroidAmazon,N.ChromeExtension,N.FirefoxExtension,N.OperaExtension,N.EdgeExtension,N.VivaldiExtension,N.SafariExtension,N.ChromeBrowser,N.FirefoxBrowser,N.OperaBrowser,N.EdgeBrowser,N.IEBrowser,N.SafariBrowser,N.VivaldiBrowser,N.UnknownBrowser,N.WindowsDesktop,N.MacOsDesktop,N.LinuxDesktop,N.UWP,N.WindowsCLI,N.MacOsCLI,N.LinuxCLI,N.SDK,N.Server;var W,q,j,$,G,H;!function(e){e[e.SCIM=1]="SCIM",e[e.DomainVerification=2]="DomainVerification",e[e.PublicApi=3]="PublicApi"}(W||(W={})),function(e){e[e.User_LoggedIn=1e3]="User_LoggedIn",e[e.User_ChangedPassword=1001]="User_ChangedPassword",e[e.User_Updated2fa=1002]="User_Updated2fa",e[e.User_Disabled2fa=1003]="User_Disabled2fa",e[e.User_Recovered2fa=1004]="User_Recovered2fa",e[e.User_FailedLogIn=1005]="User_FailedLogIn",e[e.User_FailedLogIn2fa=1006]="User_FailedLogIn2fa",e[e.User_ClientExportedVault=1007]="User_ClientExportedVault",e[e.User_UpdatedTempPassword=1008]="User_UpdatedTempPassword",e[e.User_MigratedKeyToKeyConnector=1009]="User_MigratedKeyToKeyConnector",e[e.User_RequestedDeviceApproval=1010]="User_RequestedDeviceApproval",e[e.User_TdeOffboardingPasswordSet=1011]="User_TdeOffboardingPasswordSet",e[e.Cipher_Created=1100]="Cipher_Created",e[e.Cipher_Updated=1101]="Cipher_Updated",e[e.Cipher_Deleted=1102]="Cipher_Deleted",e[e.Cipher_AttachmentCreated=1103]="Cipher_AttachmentCreated",e[e.Cipher_AttachmentDeleted=1104]="Cipher_AttachmentDeleted",e[e.Cipher_Shared=1105]="Cipher_Shared",e[e.Cipher_UpdatedCollections=1106]="Cipher_UpdatedCollections",e[e.Cipher_ClientViewed=1107]="Cipher_ClientViewed",e[e.Cipher_ClientToggledPasswordVisible=1108]="Cipher_ClientToggledPasswordVisible",e[e.Cipher_ClientToggledHiddenFieldVisible=1109]="Cipher_ClientToggledHiddenFieldVisible",e[e.Cipher_ClientToggledCardCodeVisible=1110]="Cipher_ClientToggledCardCodeVisible",e[e.Cipher_ClientCopiedPassword=1111]="Cipher_ClientCopiedPassword",e[e.Cipher_ClientCopiedHiddenField=1112]="Cipher_ClientCopiedHiddenField",e[e.Cipher_ClientCopiedCardCode=1113]="Cipher_ClientCopiedCardCode",e[e.Cipher_ClientAutofilled=1114]="Cipher_ClientAutofilled",e[e.Cipher_SoftDeleted=1115]="Cipher_SoftDeleted",e[e.Cipher_Restored=1116]="Cipher_Restored",e[e.Cipher_ClientToggledCardNumberVisible=1117]="Cipher_ClientToggledCardNumberVisible",e[e.Cipher_ClientToggledTOTPSeedVisible=1118]="Cipher_ClientToggledTOTPSeedVisible",e[e.Collection_Created=1300]="Collection_Created",e[e.Collection_Updated=1301]="Collection_Updated",e[e.Collection_Deleted=1302]="Collection_Deleted",e[e.Group_Created=1400]="Group_Created",e[e.Group_Updated=1401]="Group_Updated",e[e.Group_Deleted=1402]="Group_Deleted",e[e.OrganizationUser_Invited=1500]="OrganizationUser_Invited",e[e.OrganizationUser_Confirmed=1501]="OrganizationUser_Confirmed",e[e.OrganizationUser_Updated=1502]="OrganizationUser_Updated",e[e.OrganizationUser_Removed=1503]="OrganizationUser_Removed",e[e.OrganizationUser_UpdatedGroups=1504]="OrganizationUser_UpdatedGroups",e[e.OrganizationUser_UnlinkedSso=1505]="OrganizationUser_UnlinkedSso",e[e.OrganizationUser_ResetPassword_Enroll=1506]="OrganizationUser_ResetPassword_Enroll",e[e.OrganizationUser_ResetPassword_Withdraw=1507]="OrganizationUser_ResetPassword_Withdraw",e[e.OrganizationUser_AdminResetPassword=1508]="OrganizationUser_AdminResetPassword",e[e.OrganizationUser_ResetSsoLink=1509]="OrganizationUser_ResetSsoLink",e[e.OrganizationUser_FirstSsoLogin=1510]="OrganizationUser_FirstSsoLogin",e[e.OrganizationUser_Revoked=1511]="OrganizationUser_Revoked",e[e.OrganizationUser_Restored=1512]="OrganizationUser_Restored",e[e.OrganizationUser_ApprovedAuthRequest=1513]="OrganizationUser_ApprovedAuthRequest",e[e.OrganizationUser_RejectedAuthRequest=1514]="OrganizationUser_RejectedAuthRequest",e[e.OrganizationUser_Deleted=1515]="OrganizationUser_Deleted",e[e.OrganizationUser_Left=1516]="OrganizationUser_Left",e[e.Organization_Updated=1600]="Organization_Updated",e[e.Organization_PurgedVault=1601]="Organization_PurgedVault",e[e.Organization_ClientExportedVault=1602]="Organization_ClientExportedVault",e[e.Organization_VaultAccessed=1603]="Organization_VaultAccessed",e[e.Organization_EnabledSso=1604]="Organization_EnabledSso",e[e.Organization_DisabledSso=1605]="Organization_DisabledSso",e[e.Organization_EnabledKeyConnector=1606]="Organization_EnabledKeyConnector",e[e.Organization_DisabledKeyConnector=1607]="Organization_DisabledKeyConnector",e[e.Organization_SponsorshipsSynced=1608]="Organization_SponsorshipsSynced",e[e.Organization_CollectionManagementUpdated=1609]="Organization_CollectionManagementUpdated",e[e.Policy_Updated=1700]="Policy_Updated",e[e.ProviderUser_Invited=1800]="ProviderUser_Invited",e[e.ProviderUser_Confirmed=1801]="ProviderUser_Confirmed",e[e.ProviderUser_Updated=1802]="ProviderUser_Updated",e[e.ProviderUser_Removed=1803]="ProviderUser_Removed",e[e.ProviderOrganization_Created=1900]="ProviderOrganization_Created",e[e.ProviderOrganization_Added=1901]="ProviderOrganization_Added",e[e.ProviderOrganization_Removed=1902]="ProviderOrganization_Removed",e[e.ProviderOrganization_VaultAccessed=1903]="ProviderOrganization_VaultAccessed",e[e.OrganizationDomain_Added=2e3]="OrganizationDomain_Added",e[e.OrganizationDomain_Removed=2001]="OrganizationDomain_Removed",e[e.OrganizationDomain_Verified=2002]="OrganizationDomain_Verified",e[e.OrganizationDomain_NotVerified=2003]="OrganizationDomain_NotVerified",e[e.Secret_Retrieved=2100]="Secret_Retrieved"}(q||(q={})),function(e){e[e.Continue=100]="Continue",e[e.SwitchingProtocols=101]="SwitchingProtocols",e[e.Processing=102]="Processing",e[e.Ok=200]="Ok",e[e.Created=201]="Created",e[e.Accepted=202]="Accepted",e[e.NonAuthoritativeInformation=203]="NonAuthoritativeInformation",e[e.NoContent=204]="NoContent",e[e.ResetContent=205]="ResetContent",e[e.PartialContent=206]="PartialContent",e[e.MultiStatus=207]="MultiStatus",e[e.AlreadyReported=208]="AlreadyReported",e[e.ImUsed=226]="ImUsed",e[e.MultipleChoices=300]="MultipleChoices",e[e.MovedPermanently=301]="MovedPermanently",e[e.Found=302]="Found",e[e.SeeOther=303]="SeeOther",e[e.NotModified=304]="NotModified",e[e.UseProxy=305]="UseProxy",e[e.SwitchProxy=306]="SwitchProxy",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e[e.BadRequest=400]="BadRequest",e[e.Unauthorized=401]="Unauthorized",e[e.PaymentRequired=402]="PaymentRequired",e[e.Forbidden=403]="Forbidden",e[e.NotFound=404]="NotFound",e[e.MethodNotAllowed=405]="MethodNotAllowed",e[e.NotAcceptable=406]="NotAcceptable",e[e.ProxyAuthenticationRequired=407]="ProxyAuthenticationRequired",e[e.RequestTimeout=408]="RequestTimeout",e[e.Conflict=409]="Conflict",e[e.Gone=410]="Gone",e[e.LengthRequired=411]="LengthRequired",e[e.PreconditionFailed=412]="PreconditionFailed",e[e.PayloadTooLarge=413]="PayloadTooLarge",e[e.UriTooLong=414]="UriTooLong",e[e.UnsupportedMediaType=415]="UnsupportedMediaType",e[e.RangeNotSatisfiable=416]="RangeNotSatisfiable",e[e.ExpectationFailed=417]="ExpectationFailed",e[e.IAmATeapot=418]="IAmATeapot",e[e.MisdirectedRequest=421]="MisdirectedRequest",e[e.UnprocessableEntity=422]="UnprocessableEntity",e[e.Locked=423]="Locked",e[e.FailedDependency=424]="FailedDependency",e[e.UpgradeRequired=426]="UpgradeRequired",e[e.PreconditionRequired=428]="PreconditionRequired",e[e.TooManyRequests=429]="TooManyRequests",e[e.RequestHeaderFieldsTooLarge=431]="RequestHeaderFieldsTooLarge",e[e.UnavailableForLegalReasons=451]="UnavailableForLegalReasons",e[e.InternalServerError=500]="InternalServerError",e[e.NotImplemented=501]="NotImplemented",e[e.BadGateway=502]="BadGateway",e[e.ServiceUnavailable=503]="ServiceUnavailable",e[e.GatewayTimeout=504]="GatewayTimeout",e[e.HttpVersionNotSupported=505]="HttpVersionNotSupported",e[e.VariantAlsoNegotiates=506]="VariantAlsoNegotiates",e[e.InsufficientStorage=507]="InsufficientStorage",e[e.LoopDetected=508]="LoopDetected",e[e.NotExtended=510]="NotExtended",e[e.NetworkAuthenticationRequired=511]="NetworkAuthenticationRequired"}(j||(j={})),function(e){e.Integration="integration",e.SDK="sdk",e.SSO="sso",e.SCIM="scim",e.BWDC="bwdc",e.EVENT="event",e.DEVICE="device"}($||($={})),function(e){e[e.One=1]="One",e[e.Latest=1]="Latest"}(G||(G={})),function(e){e[e.SyncCipherUpdate=0]="SyncCipherUpdate",e[e.SyncCipherCreate=1]="SyncCipherCreate",e[e.SyncLoginDelete=2]="SyncLoginDelete",e[e.SyncFolderDelete=3]="SyncFolderDelete",e[e.SyncCiphers=4]="SyncCiphers",e[e.SyncVault=5]="SyncVault",e[e.SyncOrgKeys=6]="SyncOrgKeys",e[e.SyncFolderCreate=7]="SyncFolderCreate",e[e.SyncFolderUpdate=8]="SyncFolderUpdate",e[e.SyncCipherDelete=9]="SyncCipherDelete",e[e.SyncSettings=10]="SyncSettings",e[e.LogOut=11]="LogOut",e[e.SyncSendCreate=12]="SyncSendCreate",e[e.SyncSendUpdate=13]="SyncSendUpdate",e[e.SyncSendDelete=14]="SyncSendDelete",e[e.AuthRequest=15]="AuthRequest",e[e.AuthRequestResponse=16]="AuthRequestResponse",e[e.SyncOrganizations=17]="SyncOrganizations",e[e.SyncOrganizationStatusChanged=18]="SyncOrganizationStatusChanged",e[e.SyncOrganizationCollectionSettingChanged=19]="SyncOrganizationCollectionSettingChanged",e[e.Notification=20]="Notification",e[e.NotificationStatus=21]="NotificationStatus",e[e.PendingSecurityTasks=22]="PendingSecurityTasks"}(H||(H={}));class K{static sendMessageToApp(e,t=null,r=!1){return ne.isSafariApi?new Promise((r=>{const i=(new Date).getTime().toString()+"_"+Math.floor(Math.random()*Number.MAX_SAFE_INTEGER);browser.runtime.sendNativeMessage("com.bitwarden.desktop",{id:i,command:e,data:t,responseData:null},(e=>{r(e)}))})):Promise.resolve(null)}}var Q=function(e,t,r,i){return new(r||(r=Promise))((function(n,o){function s(e){try{d(i.next(e))}catch(e){o(e)}}function a(e){try{d(i.throw(e))}catch(e){o(e)}}function d(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,a)}d((i=i.apply(e,t||[])).next())}))};class Y{static copy(e,t){return Q(this,void 0,void 0,(function*(){if(Y.isClipboardApiSupported(e,"writeText"))try{yield e.navigator.clipboard.writeText(t)}catch(r){Y.consoleLogService.debug(`Error copying to clipboard using the clipboard API, attempting legacy method: ${r}`),this.useLegacyCopyMethod(e,t)}else this.useLegacyCopyMethod(e,t)}))}static read(e){return Q(this,void 0,void 0,(function*(){if(!Y.isClipboardApiSupported(e,"readText"))return this.useLegacyReadMethod(e);try{return yield e.navigator.clipboard.readText()}catch(t){return Y.consoleLogService.debug(`Error reading from clipboard using the clipboard API, attempting legacy method: ${t}`),this.useLegacyReadMethod(e)}}))}static useLegacyCopyMethod(e,t){if(!Y.isLegacyClipboardMethodSupported(e,"copy"))return void Y.consoleLogService.warning("Legacy copy method not supported");const r=e.document.createElement("textarea");r.textContent=t||" ",r.style.position="fixed",e.document.body.appendChild(r),r.select();try{e.document.execCommand("copy")}catch(e){Y.consoleLogService.warning(`Error writing to clipboard: ${e}`)}finally{e.document.body.removeChild(r)}}static useLegacyReadMethod(e){if(!Y.isLegacyClipboardMethodSupported(e,"paste"))return Y.consoleLogService.warning("Legacy paste method not supported"),"";const t=e.document.createElement("textarea");t.style.position="fixed",e.document.body.appendChild(t),t.focus();try{return e.document.execCommand("paste")?t.value:""}catch(e){Y.consoleLogService.warning(`Error reading from clipboard: ${e}`)}finally{e.document.body.removeChild(t)}return""}static isClipboardApiSupported(e,t){return"clipboard"in e.navigator&&t in e.navigator.clipboard}static isLegacyClipboardMethodSupported(e,t){return"queryCommandSupported"in e.document&&e.document.queryCommandSupported(t)}}Y.consoleLogService=new t(!1);var X=Y,J=function(e,t,r,i){return new(r||(r=Promise))((function(n,o){function s(e){try{d(i.next(e))}catch(e){o(e)}}function a(e){try{d(i.throw(e))}catch(e){o(e)}}function d(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,a)}d((i=i.apply(e,t||[])).next())}))};class Z{constructor(e,t,r){this.clipboardWriteCallback=e,this.globalContext=t,this.offscreenDocumentService=r}static getDevice(e){return this.deviceCache||(Z.isFirefox()?this.deviceCache=N.FirefoxExtension:Z.isOpera(e)?this.deviceCache=N.OperaExtension:Z.isEdge()?this.deviceCache=N.EdgeExtension:Z.isVivaldi()?this.deviceCache=N.VivaldiExtension:Z.isChrome(e)?this.deviceCache=N.ChromeExtension:Z.isSafari(e)&&(this.deviceCache=N.SafariExtension)),this.deviceCache}getDevice(){return Z.getDevice(this.globalContext)}getDeviceString(){return N[this.getDevice()].toLowerCase().replace("extension","")}getClientType(){return B.Browser}static isFirefox(){return-1!==navigator.userAgent.indexOf(" Firefox/")||-1!==navigator.userAgent.indexOf(" Gecko/")}isFirefox(){return this.getDevice()===N.FirefoxExtension}static isChrome(e){return e.chrome&&-1!==navigator.userAgent.indexOf(" Chrome/")}isChrome(){return this.getDevice()===N.ChromeExtension}static isEdge(){return-1!==navigator.userAgent.indexOf(" Edg/")}isEdge(){return this.getDevice()===N.EdgeExtension}static isOpera(e){var t;return!!(null===(t=e.opr)||void 0===t?void 0:t.addons)||!!e.opera||navigator.userAgent.indexOf(" OPR/")>=0}isOpera(){return this.getDevice()===N.OperaExtension}static isVivaldi(){return-1!==navigator.userAgent.indexOf(" Vivaldi/")}isVivaldi(){return this.getDevice()===N.VivaldiExtension}static isSafari(e){return!Z.isOpera(e)&&-1!==navigator.userAgent.indexOf(" Safari/")}static safariVersion(){var e;return null===(e=navigator.userAgent.match("Version/([0-9.]*)"))||void 0===e?void 0:e[1]}isSafari(){return this.getDevice()===N.SafariExtension}static shouldApplySafariHeightFix(e){var t;if(Z.getDevice(e)!==N.SafariExtension)return!1;const r=Z.safariVersion(),i=null===(t=null==r?void 0:r.split("."))||void 0===t?void 0:t.map((e=>Number(e)));return(null==i?void 0:i[0])<16||16===(null==i?void 0:i[0])&&0===(null==i?void 0:i[1])}isIE(){return!1}isMacAppStore(){return!1}isViewOpen(){return J(this,void 0,void 0,(function*(){return this.isSafari()?ne.isPopupOpen():new Promise(((e,t)=>{chrome.runtime.sendMessage({command:"checkVaultPopupHeartbeat"},(r=>{if(null!=chrome.runtime.lastError)return"Could not establish connection. Receiving end does not exist."===chrome.runtime.lastError.message?void e(!1):void t(chrome.runtime.lastError);e(Boolean(r))}))}))}))}lockTimeout(){return null}launchUri(e,t){ne.createNewTab(e,t&&!0===t.extensionPage)}getApplicationVersion(){var e;const t=chrome.runtime.getManifest();return Promise.resolve(null!==(e=t.version_name)&&void 0!==e?e:t.version)}getApplicationVersionNumber(){const e=chrome.runtime.getManifest();return Promise.resolve(e.version.split(RegExp("[+|-]"))[0].trim())}supportsWebAuthn(e){return"undefined"!=typeof PublicKeyCredential}supportsDuo(){return!0}isDev(){return!1}isSelfHost(){return!1}copyToClipboard(e,t){const r=(null==t?void 0:t.window)||this.globalContext,i=Boolean(null==t?void 0:t.clearing),n=(null==t?void 0:t.clearMs)||null,o=()=>{i||null==this.clipboardWriteCallback||this.clipboardWriteCallback(e,n)};this.isSafari()?K.sendMessageToApp("copyToClipboard",e).then(o):(this.isChrome()&&""===e&&(e="\0"),ne.isManifestVersion(3)&&this.offscreenDocumentService.offscreenApiSupported()?this.triggerOffscreenCopyToClipboard(e).then(o):X.copy(r,e).then(o))}readFromClipboard(e){return J(this,void 0,void 0,(function*(){const t=(null==e?void 0:e.window)||this.globalContext;return this.isSafari()?yield K.sendMessageToApp("readFromClipboard"):ne.isManifestVersion(3)&&this.offscreenDocumentService.offscreenApiSupported()?yield this.triggerOffscreenReadFromClipboard():yield X.read(t)}))}supportsSecureStorage(){return!1}getAutofillKeyboardShortcut(){return J(this,void 0,void 0,(function*(){let e;return this.isSafari()?e="Cmd+Shift+L":this.isFirefox()?(e=(yield browser.commands.getAll()).find((e=>e.name===k)).shortcut,"mac"===(yield browser.runtime.getPlatformInfo()).os&&"Ctrl+Shift+L"===e&&(e="Cmd+Shift+L")):yield new Promise((t=>chrome.commands.getAll((r=>t(e=r.find((e=>e.name===k)).shortcut))))),e}))}triggerOffscreenCopyToClipboard(e){return J(this,void 0,void 0,(function*(){yield this.offscreenDocumentService.withDocument([chrome.offscreen.Reason.CLIPBOARD],"Write text to the clipboard.",(()=>J(this,void 0,void 0,(function*(){yield ne.sendMessageWithResponse("offscreenCopyToClipboard",{text:e})}))))}))}triggerOffscreenReadFromClipboard(){return J(this,void 0,void 0,(function*(){const e=yield this.offscreenDocumentService.withDocument([chrome.offscreen.Reason.CLIPBOARD],"Read text from the clipboard.",(()=>J(this,void 0,void 0,(function*(){return yield ne.sendMessageWithResponse("offscreenReadFromClipboard")}))));return"string"==typeof e?e:""}))}}Z.deviceCache=null;var ee=function(e,t,r,i){return new(r||(r=Promise))((function(n,o){function s(e){try{d(i.next(e))}catch(e){o(e)}}function a(e){try{d(i.throw(e))}catch(e){o(e)}}function d(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,a)}d((i=i.apply(e,t||[])).next())}))};let te;function re(e,r){return ee(this,void 0,void 0,(function*(){return te||(te=function(){var e,r;const i=new t(!1),n=globalThis.chrome&&c(globalThis.chrome),o=/^(https?|wss?|file|ftp|\*):\/\/(\*|\*\.[^*/]+|[^*/]+)\/.*$|^file:\/\/\/.*$|^resource:\/\/(\*|\*\.[^*/]+|[^*/]+)\/.*$|^about:/,s=null===(e=globalThis.navigator)||void 0===e?void 0:e.userAgent.includes("Firefox/"),a=Boolean(null===(r=globalThis.chrome)||void 0===r?void 0:r.scripting),d="object"==typeof chrome&&"webNavigation"in chrome;function c(e){return new Proxy(e,{get(e,t){if(e[t])return"function"!=typeof e[t]?c(e[t]):(...r)=>new Promise(((i,n)=>{e[t](...r,(e=>{chrome.runtime.lastError?n(new Error(chrome.runtime.lastError.message)):i(e)}))}))}})}function l(e){if(!u(e))throw new Error(`${e} is an invalid pattern, it must match ${String(o)}`)}function u(e){return"<all_urls>"===e||o.test(e)}function p(e){l(e);let[,t,r="",i]=e.split(/(^[^:]+:[/][/])([^/]+)?/);return t=t.replace("*",s?"(https?|wss?)":"https?").replaceAll(/[/]/g,"[/]"),"*"===r?r="[^/]+":r&&(r=r.replace(/^[*][.]/,"([^/]+.)*").replaceAll(/[.]/g,"[.]").replace(/[*]$/,"[^.]+")),i=i.replaceAll(/[/]/g,"[/]").replaceAll(/[.]/g,"[.]").replaceAll(/[*]/g,".*"),"^"+t+r+"("+i+")?$"}function f(...e){return 0===e.length?/$./:e.includes("<all_urls>")?/^(https?|file|ftp):[/]+/:e.includes("*://*/*")?s?/^(https?|wss?):[/][/][^/]+([/].*)?$/:/^https?:[/][/][^/]+([/].*)?$/:new RegExp(e.map((e=>p(e))).join("|"))}function h(e){return"object"==typeof e?Object.assign(Object.assign({},e),{allFrames:!1}):{tabId:e,frameId:void 0,allFrames:!0}}function g(e){return Array.isArray(e)?e:[e]}function v(e){return void 0===e?void 0:[e]}function m(e){return ee(this,arguments,void 0,(function*({tabId:e,frameId:t,files:r,allFrames:i,matchAboutBlank:o,runAt:s},{ignoreTargetErrors:d}={}){const c=Promise.all(r.map((r=>ee(this,void 0,void 0,(function*(){return"string"==typeof r&&(r={file:r}),a?chrome.scripting.insertCSS({target:{tabId:e,frameIds:v(t),allFrames:void 0===t?i:void 0},files:"file"in r?[r.file]:void 0,css:"code"in r?r.code:void 0}):n.tabs.insertCSS(e,Object.assign(Object.assign({},r),{matchAboutBlank:o,allFrames:i,frameId:t,runAt:null!=s?s:"document_start"}))})))));d?yield S(c):yield c}))}function y(e){if(e.some((e=>"code"in e)))throw new Error("chrome.scripting does not support injecting strings of `code`")}function b(e){return ee(this,arguments,void 0,(function*({tabId:e,frameId:t,files:r,allFrames:i,matchAboutBlank:o,runAt:s},{ignoreTargetErrors:d}={}){const c=r.map((e=>"string"==typeof e?{file:e}:e));if(a){y(c);const r=chrome.scripting.executeScript({target:{tabId:e,frameIds:v(t),allFrames:void 0===t?i:void 0},files:c.map((({file:e})=>e))});return void(d?yield S(r):yield r)}const l=[];for(const r of c)"code"in r&&(yield l.at(-1)),l.push(n.tabs.executeScript(e,Object.assign(Object.assign({},r),{matchAboutBlank:o,allFrames:i,frameId:t,runAt:s})));d?yield S(Promise.all(l)):yield Promise.all(l)}))}function C(e,t){return ee(this,arguments,void 0,(function*(e,t,r={}){const i=g(e);yield Promise.all(i.map((e=>ee(this,void 0,void 0,(function*(){return w(h(e),t,r)})))))}))}function w(e,t){return ee(this,arguments,void 0,(function*({frameId:e,tabId:t,allFrames:r},i,n={}){const o=g(i).flatMap((i=>{var o,s,a,d,c,l;return[m({tabId:t,frameId:e,allFrames:r,files:null!==(o=i.css)&&void 0!==o?o:[],matchAboutBlank:null!==(s=i.matchAboutBlank)&&void 0!==s?s:i.match_about_blank,runAt:null!==(a=i.runAt)&&void 0!==a?a:i.run_at},n),b({tabId:t,frameId:e,allFrames:r,files:null!==(d=i.js)&&void 0!==d?d:[],matchAboutBlank:null!==(c=i.matchAboutBlank)&&void 0!==c?c:i.match_about_blank,runAt:null!==(l=i.runAt)&&void 0!==l?l:i.run_at},n)]}));yield Promise.all(o)}))}function S(e){return ee(this,void 0,void 0,(function*(){try{yield e}catch(e){if(!/^No frame with id \d+ in tab \d+.$|^No tab with id: \d+.$|^The tab was closed.$|^The frame was removed.$/.test(null==e?void 0:e.message))throw e}}))}function _(e){return ee(this,void 0,void 0,(function*(){return n.permissions.contains({origins:[new URL(e).origin+"/*"]})}))}return(e,t)=>ee(this,void 0,void 0,(function*(){const{js:r=[],css:o=[],matchAboutBlank:s,matches:a=[],excludeMatches:c,runAt:l}=e;let{allFrames:u}=e;if(d?u=!1:u&&i.warning("`allFrames: true` requires the `webNavigation` permission to work correctly: https://github.com/fregante/content-scripts-register-polyfill#permissions"),0===a.length)throw new Error("Type error for parameter contentScriptOptions (Error processing matches: Array requires at least 1 items; you have 0) for contentScripts.register.");yield Promise.all(a.map((e=>ee(this,void 0,void 0,(function*(){if(!(yield n.permissions.contains({origins:[e]})))throw new Error(`Permission denied to register a content script for ${e}`)})))));const p=f(...a),h=f(...null!=c?c:[]),g=(e,t,...i)=>ee(this,[e,t,...i],void 0,(function*(e,t,i=0){p.test(e)&&!h.test(e)&&(yield _(e))&&(yield C({tabId:t,frameId:i},{css:o,js:r,matchAboutBlank:s,runAt:l},{ignoreTargetErrors:!0}))})),v=(e,t,r)=>ee(this,[e,t,r],void 0,(function*(e,{status:t},{url:r}){"loading"===t&&r&&g(r,e)})),m=e=>ee(this,[e],void 0,(function*({tabId:e,frameId:t,url:r}){g(r,e,t)}));d?ne.addListener(chrome.webNavigation.onCommitted,m):ne.addListener(chrome.tabs.onUpdated,v);const y={unregister(){return ee(this,void 0,void 0,(function*(){d?chrome.webNavigation.onCommitted.removeListener(m):chrome.tabs.onUpdated.removeListener(v)}))}};return"function"==typeof t&&t(y),y}))}()),te(e,r)}))}var ie=function(e,t,r,i){return new(r||(r=Promise))((function(n,o){function s(e){try{d(i.next(e))}catch(e){o(e)}}function a(e){try{d(i.throw(e))}catch(e){o(e)}}function d(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,a)}d((i=i.apply(e,t||[])).next())}))};class ne{static get manifestVersion(){return chrome.runtime.getManifest().manifest_version}static isManifestVersion(e){return ne.manifestVersion===e}static getWindow(e){return ie(this,void 0,void 0,(function*(){return e?yield ne.getWindowById(e):ne.getCurrentWindow()}))}static getCurrentWindow(){return ie(this,void 0,void 0,(function*(){return new Promise((e=>chrome.windows.getCurrent({populate:!0},e)))}))}static getWindowById(e){return ie(this,void 0,void 0,(function*(){return new Promise((t=>chrome.windows.get(e,{populate:!0},t)))}))}static createWindow(e){return ie(this,void 0,void 0,(function*(){return new Promise((t=>{chrome.windows.create(e,(e=>ie(this,void 0,void 0,(function*(){if(!ne.isSafariApi)return t(e);const r=yield new Promise((e=>{chrome.windows.getAll({windowTypes:["normal"]},(t=>e(t)))})),i=r.find((t=>t.id!==e.id));if(null==i||!i.id)return t(e);chrome.windows.update(i.id,{focused:!0},(()=>{chrome.windows.update(e.id,{focused:!0},(()=>{t(e)}))}))}))))}))}))}static removeWindow(e){return ie(this,void 0,void 0,(function*(){return new Promise((t=>chrome.windows.remove(e,(()=>t()))))}))}static updateWindowProperties(e,t){return ie(this,void 0,void 0,(function*(){return new Promise((r=>chrome.windows.update(e,t,(()=>{r()}))))}))}static focusWindow(e){return ie(this,void 0,void 0,(function*(){yield ne.updateWindowProperties(e,{focused:!0})}))}static getTabFromCurrentWindowId(){return ie(this,void 0,void 0,(function*(){return yield ne.tabsQueryFirstCurrentWindowForSafari({active:!0,windowId:chrome.windows.WINDOW_ID_CURRENT})}))}static getBrowserClientVendor(e){switch(Z.getDevice(e)){case N.ChromeExtension:case N.ChromeBrowser:return I;case N.OperaExtension:case N.OperaBrowser:return F;case N.EdgeExtension:case N.EdgeBrowser:return M;case N.VivaldiExtension:case N.VivaldiBrowser:return z;default:return V}}static getTab(e){return ie(this,void 0,void 0,(function*(){return e?ne.isManifestVersion(3)?yield chrome.tabs.get(e):new Promise((t=>chrome.tabs.get(e,(e=>{t(e)})))):null}))}static getTabFromCurrentWindow(){return ie(this,void 0,void 0,(function*(){return yield ne.tabsQueryFirstCurrentWindowForSafari({active:!0,currentWindow:!0})}))}static getActiveTabs(){return ie(this,void 0,void 0,(function*(){return yield ne.tabsQuery({active:!0})}))}static getCurrentTab(){return ie(this,void 0,void 0,(function*(){return ne.isManifestVersion(3)?yield chrome.tabs.getCurrent():new Promise((e=>chrome.tabs.getCurrent((t=>{e(t)}))))}))}static tabsQuery(e){return ie(this,void 0,void 0,(function*(){return new Promise((t=>{chrome.tabs.query(e,(e=>{t(e)}))}))}))}static tabsQueryFirst(e){return ie(this,void 0,void 0,(function*(){const t=yield ne.tabsQuery(e);return t.length>0?t[0]:null}))}static tabsQueryFirstCurrentWindowForSafari(e){return ie(this,void 0,void 0,(function*(){var t;if(!ne.isSafariApi)return yield ne.tabsQueryFirst(e);const r=(yield ne.getCurrentWindow()).id,i=yield ne.tabsQuery(e);return i.length<=1||null==r?i[0]:null!==(t=i.find((e=>e.windowId===r)))&&void 0!==t?t:i[0]}))}static tabSendMessageData(e,t,r=null){const i={command:t};return null!=r&&(i.data=r),ne.tabSendMessage(e,i)}static tabSendMessage(e,t){return ie(this,arguments,void 0,(function*(e,t,r=null,i=!1){if(e&&e.id)return new Promise(((n,o)=>{chrome.tabs.sendMessage(e.id,t,r,(e=>{chrome.runtime.lastError&&i&&o(),n(e)}))}))}))}static sendTabsMessage(e,t,r,i){chrome.tabs.sendMessage(e,t,r,i)}static onWindowCreated(e){return ie(this,void 0,void 0,(function*(){return chrome.windows.onCreated.addListener(e)}))}static getBackgroundPage(){return void 0===chrome.extension.getBackgroundPage?null:chrome.extension.getBackgroundPage()}static isBackgroundPage(e){return void 0!==e&&e===ne.getBackgroundPage()}static getExtensionViews(e){return void 0===chrome.extension.getViews?[]:chrome.extension.getViews(e)}static isPopupOpen(){return ie(this,void 0,void 0,(function*(){return Promise.resolve(ne.getExtensionViews({type:"popup"}).length>0)}))}static createNewTab(e,t=!0){return new Promise((r=>chrome.tabs.create({url:e,active:t},(e=>r(e)))))}static getFrameDetails(e){return ie(this,void 0,void 0,(function*(){return new Promise((t=>chrome.webNavigation.getFrame(e,t)))}))}static getAllFrameDetails(e){return ie(this,void 0,void 0,(function*(){return new Promise((t=>chrome.webNavigation.getAllFrames({tabId:e},t)))}))}static messageListener(e,t){ne.addListener(chrome.runtime.onMessage,t)}static messageListener$(){return new R((e=>{const t=t=>{e.next(t)};return ne.addListener(chrome.runtime.onMessage,t),()=>ne.removeListener(chrome.runtime.onMessage,t)}))}static storageChangeListener(e){ne.addListener(chrome.storage.onChanged,e)}static addListener(e,t){e.addListener(t),ne.isSafariApi&&!ne.isBackgroundPage(self)&&(ne.trackedChromeEventListeners.push([e,t]),ne.setupUnloadListeners())}static removeListener(e,t){if(e.removeListener(t),ne.isSafariApi&&!ne.isBackgroundPage(self)){const e=ne.trackedChromeEventListeners.findIndex((([e,r])=>r==t));-1!==e&&ne.trackedChromeEventListeners.splice(e,1)}}static setupUnloadListeners(){self.addEventListener("pagehide",(()=>{for(const[e,t]of ne.trackedChromeEventListeners)e.removeListener(t)}))}static sendMessage(e,t={}){const r=Object.assign({},{command:e},t);return chrome.runtime.sendMessage(r)}static sendMessageWithResponse(e,t={}){const r=Object.assign({},{command:e},t);return new Promise((e=>chrome.runtime.sendMessage(r,e)))}static focusTab(e){return ie(this,void 0,void 0,(function*(){chrome.tabs.update(e,{active:!0,highlighted:!0})}))}static closePopup(e){ne.isWebExtensionsApi&&ne.isFirefoxOnAndroid?browser.tabs.update({active:!0}).finally(e.close):e.close()}static gaFilter(){return!1}static getUILanguage(){return chrome.i18n.getUILanguage()}static reloadExtension(){return this.isSafariApi?self.location.reload():chrome.runtime.reload()}static reloadOpenWindows(e=!1){const t=ne.getExtensionViews();if(!t.length)return;const r=self.location.href;t.filter((e=>null!=e.location.href&&!e.location.href.includes("background.html"))).filter((t=>!e||t.location.href!==r)).forEach((e=>e.location.reload()))}static connectNative(e){return ne.isWebExtensionsApi?browser.runtime.connectNative(e):ne.isChromeApi?chrome.runtime.connectNative(e):void 0}static requestPermission(e){return ne.isWebExtensionsApi?browser.permissions.request(e):new Promise((t=>{chrome.permissions.request(e,t)}))}static permissionsGranted(e){return ie(this,void 0,void 0,(function*(){return new Promise((t=>chrome.permissions.contains({permissions:e},(e=>t(e)))))}))}static getPlatformInfo(){return ne.isWebExtensionsApi?browser.runtime.getPlatformInfo():new Promise((e=>{chrome.runtime.getPlatformInfo(e)}))}static getBrowserAction(){return ne.isManifestVersion(3)?chrome.action:chrome.browserAction}static getSidebarAction(e){var t;const r=Z.getDevice(e);return r===N.FirefoxExtension?browser.sidebarAction:r===N.OperaExtension?null===(t=e.opr)||void 0===t?void 0:t.sidebarAction:null}static captureVisibleTab(){return new Promise((e=>{chrome.tabs.captureVisibleTab(null,{format:"png"},e)}))}static executeScriptInTab(e,t,r){var i;if(ne.isManifestVersion(3)){const n={tabId:e};return"number"==typeof t.frameId&&(n.frameIds=[t.frameId]),!(null===(i=n.frameIds)||void 0===i?void 0:i.length)&&t.allFrames&&(n.allFrames=t.allFrames),chrome.scripting.executeScript({target:n,files:t.file?[t.file]:null,injectImmediately:"document_start"===t.runAt,world:(null==r?void 0:r.world)||"ISOLATED"})}return new Promise((r=>{chrome.tabs.executeScript(e,t,(e=>{r(e)}))}))}static browserAutofillSettingsOverridden(){return ie(this,void 0,void 0,(function*(){if(!(yield ne.permissionsGranted(["privacy"])))return!1;const e=e=>"controlled_by_this_extension"===e.levelOfControl&&!e.value,t=yield new Promise((t=>chrome.privacy.services.autofillAddressEnabled.get({},(r=>t(e(r)))))),r=yield new Promise((t=>chrome.privacy.services.autofillCreditCardEnabled.get({},(r=>t(e(r)))))),i=yield new Promise((t=>chrome.privacy.services.passwordSavingEnabled.get({},(r=>t(e(r))))));return t&&r&&i}))}static updateDefaultBrowserAutofillSettings(e){return ie(this,void 0,void 0,(function*(){yield chrome.privacy.services.autofillAddressEnabled.set({value:e}),yield chrome.privacy.services.autofillCreditCardEnabled.set({value:e}),yield chrome.privacy.services.passwordSavingEnabled.set({value:e})}))}static registerContentScriptsMv2(e){return ie(this,void 0,void 0,(function*(){var t;return"undefined"!=typeof browser&&(null===(t=browser.contentScripts)||void 0===t?void 0:t.register)?yield browser.contentScripts.register(e):yield re(e)}))}static registerContentScriptsMv3(e){return ie(this,void 0,void 0,(function*(){yield chrome.scripting.registerContentScripts(e)}))}static unregisterContentScriptsMv3(e){return ie(this,void 0,void 0,(function*(){yield chrome.scripting.unregisterContentScripts(e)}))}}ne.isWebExtensionsApi="undefined"!=typeof browser,ne.isSafariApi=-1!==navigator.userAgent.indexOf(" Safari/")&&-1===navigator.userAgent.indexOf(" Chrome/")&&-1===navigator.userAgent.indexOf(" Chromium/"),ne.isChromeApi=!ne.isSafariApi&&"undefined"!=typeof chrome,ne.isFirefoxOnAndroid=-1!==navigator.userAgent.indexOf("Firefox/")&&-1!==navigator.userAgent.indexOf("Android"),ne.trackedChromeEventListeners=[];var oe=function(e,t,r,i){return new(r||(r=Promise))((function(n,o){function s(e){try{d(i.next(e))}catch(e){o(e)}}function a(e){try{d(i.throw(e))}catch(e){o(e)}}function d(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,a)}d((i=i.apply(e,t||[])).next())}))};class se{constructor(){this.consoleLogService=new t(!1),this.extensionMessageHandlers={offscreenCopyToClipboard:({message:e})=>this.handleOffscreenCopyToClipboard(e),offscreenReadFromClipboard:()=>this.handleOffscreenReadFromClipboard(),localStorageGet:({message:e})=>this.handleLocalStorageGet(e.key),localStorageSave:({message:e})=>this.handleLocalStorageSave(e.key,e.value),localStorageRemove:({message:e})=>this.handleLocalStorageRemove(e.key)},this.handleExtensionMessage=(e,t,r)=>{const i=this.extensionMessageHandlers[null==e?void 0:e.command];if(!i)return;const n=i({message:e,sender:t});return n?(Promise.resolve(n).then((e=>r(e))).catch((e=>this.consoleLogService.error("Error resolving extension message response",e))),!0):void 0}}init(){this.setupExtensionMessageListener()}handleOffscreenCopyToClipboard(e){return oe(this,void 0,void 0,(function*(){yield X.copy(self,e.text)}))}handleOffscreenReadFromClipboard(){return oe(this,void 0,void 0,(function*(){return yield X.read(self)}))}handleLocalStorageGet(e){return self.localStorage.getItem(e)}handleLocalStorageSave(e,t){self.localStorage.setItem(e,t)}handleLocalStorageRemove(e){self.localStorage.removeItem(e)}setupExtensionMessageListener(){ne.messageListener("offscreen-document",this.handleExtensionMessage)}}(new se).init()}();
//# sourceMappingURL=offscreen-document.js.map