{"version": 3, "file": "assets/635.js", "mappings": "mOAKCA,WAAkCC,QAAU,KAC1C,MAAiB,EAAK,E,8qJCHlB,SAASC,EAAKC,IACnBC,EAAAA,EAAAA,KAAeD,EACjB,C", "sources": ["webpack:///./src/platform/services/sdk/wasm.ts", "webpack:///../../node_modules/@bitwarden/sdk-internal/index.js"], "sourcesContent": ["import * as sdk from \"@bitwarden/sdk-internal\";\nimport * as wasm from \"@bitwarden/sdk-internal/bitwarden_wasm_internal_bg.wasm\";\n\nimport { GlobalWithWasmInit } from \"./browser-sdk-load.service\";\n\n(globalThis as GlobalWithWasmInit).initSdk = () => {\n  (sdk as any).init(wasm);\n};\n", "import { __wbg_set_wasm } from \"./bitwarden_wasm_internal_bg.js\";\n\n// In order to support a fallback strategy for web we need to conditionally load the wasm file\nexport function init(wasm) {\n  __wbg_set_wasm(wasm);\n}\n\nexport * from \"./bitwarden_wasm_internal_bg.js\";\n"], "names": ["globalThis", "initSdk", "init", "wasm", "__wbg_set_wasm"], "sourceRoot": ""}